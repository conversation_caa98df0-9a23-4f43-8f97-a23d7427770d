{"version": 3, "sources": ["tilt.jquery.js"], "names": ["factory", "define", "amd", "module", "exports", "root", "j<PERSON><PERSON><PERSON>", "undefined", "window", "require", "$", "fn", "tilt", "options", "requestTick", "ticking", "requestAnimationFrame", "updateTransforms", "bind", "bindEvents", "_this", "on", "mouseMove", "mouseEnter", "settings", "reset", "mouseLeave", "glare", "updateGlareSize", "setTransition", "timeout", "clearTimeout", "css", "speed", "easing", "glareElement", "setTimeout", "event", "call", "trigger", "getMousePositions", "pageX", "offset", "left", "outerWidth", "pageY", "top", "outerHeight", "x", "y", "mousePositions", "getV<PERSON>ues", "width", "height", "percentageX", "percentageY", "tiltX", "maxTilt", "toFixed", "tiltY", "angle", "Math", "atan2", "PI", "transforms", "perspective", "disableAxis", "scale", "max<PERSON>lare", "prepareGlare", "<PERSON><PERSON><PERSON><PERSON>", "append", "glareElementWrapper", "find", "stretch", "destroy", "each", "remove", "off", "results", "push", "data", "transition", "extend", "is", "axis", "console", "warn", "init"], "mappings": ";;;;AAAC,WAAUA,OAAV,EAAmB;AAChB,QAAI,OAAOC,MAAP,KAAkB,UAAlB,IAAgCA,OAAOC,GAA3C,EAAgD;AAC5C;AACAD,eAAO,CAAC,QAAD,CAAP,EAAmBD,OAAnB;AACH,KAHD,MAGO,IAAI,QAAOG,MAAP,yCAAOA,MAAP,OAAkB,QAAlB,IAA8BA,OAAOC,OAAzC,EAAkD;AACrD;AACAD,eAAOC,OAAP,GAAiB,UAAUC,IAAV,EAAgBC,MAAhB,EAAyB;AACtC,gBAAKA,WAAWC,SAAhB,EAA4B;AACxB;AACA;AACA;AACA;AACA,oBAAK,OAAOC,MAAP,KAAkB,WAAvB,EAAqC;AACjCF,6BAASG,QAAQ,QAAR,CAAT;AACH,iBAFD,MAGK;AACDH,6BAASG,QAAQ,QAAR,EAAkBJ,IAAlB,CAAT;AACH;AACJ;AACDL,oBAAQM,MAAR;AACA,mBAAOA,MAAP;AACH,SAfD;AAgBH,KAlBM,MAkBA;AACH;AACAN,gBAAQM,MAAR;AACH;AACJ,CA1BA,EA0BC,UAAUI,CAAV,EAAa;AACXA,MAAEC,EAAF,CAAKC,IAAL,GAAY,UAAUC,OAAV,EAAmB;;AAE3B;;;AAGA,YAAMC,cAAc,SAAdA,WAAc,GAAW;AAC3B,gBAAI,KAAKC,OAAT,EAAkB;AAClBC,kCAAsBC,iBAAiBC,IAAjB,CAAsB,IAAtB,CAAtB;AACA,iBAAKH,OAAL,GAAe,IAAf;AACH,SAJD;;AAMA;;;AAGA,YAAMI,aAAa,SAAbA,UAAa,GAAW;AAC1B,gBAAMC,QAAQ,IAAd;AACAV,cAAE,IAAF,EAAQW,EAAR,CAAW,WAAX,EAAwBC,SAAxB;AACAZ,cAAE,IAAF,EAAQW,EAAR,CAAW,YAAX,EAAyBE,UAAzB;AACA,gBAAI,KAAKC,QAAL,CAAcC,KAAlB,EAAyBf,EAAE,IAAF,EAAQW,EAAR,CAAW,YAAX,EAAyBK,UAAzB;AACzB,gBAAI,KAAKF,QAAL,CAAcG,KAAlB,EAAyBjB,EAAEF,MAAF,EAAUa,EAAV,CAAa,QAAb,EAAuBO,gBAAgBV,IAAhB,CAAqBE,KAArB,CAAvB;AAC5B,SAND;;AAQA;;;AAGA,YAAMS,gBAAgB,SAAhBA,aAAgB,GAAW;AAAA;;AAC7B,gBAAI,KAAKC,OAAL,KAAiBvB,SAArB,EAAgCwB,aAAa,KAAKD,OAAlB;AAChCpB,cAAE,IAAF,EAAQsB,GAAR,CAAY,EAAC,cAAiB,KAAKR,QAAL,CAAcS,KAA/B,WAA0C,KAAKT,QAAL,CAAcU,MAAzD,EAAZ;AACA,gBAAG,KAAKV,QAAL,CAAcG,KAAjB,EAAwB,KAAKQ,YAAL,CAAkBH,GAAlB,CAAsB,EAAC,2BAAyB,KAAKR,QAAL,CAAcS,KAAvC,WAAkD,KAAKT,QAAL,CAAcU,MAAjE,EAAtB;AACxB,iBAAKJ,OAAL,GAAeM,WAAW,YAAM;AAC5B1B,0BAAQsB,GAAR,CAAY,EAAC,cAAc,EAAf,EAAZ;AACA,oBAAG,OAAKR,QAAL,CAAcG,KAAjB,EAAwB,OAAKQ,YAAL,CAAkBH,GAAlB,CAAsB,EAAC,cAAc,EAAf,EAAtB;AAC3B,aAHc,EAGZ,KAAKR,QAAL,CAAcS,KAHF,CAAf;AAIH,SARD;;AAUA;;;AAGA,YAAMV,aAAa,SAAbA,UAAa,CAASc,KAAT,EAAgB;AAC/B,iBAAKtB,OAAL,GAAe,KAAf;AACAL,cAAE,IAAF,EAAQsB,GAAR,CAAY,EAAC,eAAe,WAAhB,EAAZ;AACAH,0BAAcS,IAAd,CAAmB,IAAnB;;AAEA;AACA5B,cAAE,IAAF,EAAQ6B,OAAR,CAAgB,iBAAhB;AACH,SAPD;;AASA;;;;AAIA,YAAMC,oBAAoB,SAApBA,iBAAoB,CAASH,KAAT,EAAgB;AACtC,gBAAI,OAAOA,KAAP,KAAkB,WAAtB,EAAmC;AAC/BA,wBAAQ;AACJI,2BAAO/B,EAAE,IAAF,EAAQgC,MAAR,GAAiBC,IAAjB,GAAwBjC,EAAE,IAAF,EAAQkC,UAAR,KAAuB,CADlD;AAEJC,2BAAOnC,EAAE,IAAF,EAAQgC,MAAR,GAAiBI,GAAjB,GAAuBpC,EAAE,IAAF,EAAQqC,WAAR,KAAwB;AAFlD,iBAAR;AAIH;AACD,mBAAO,EAACC,GAAGX,MAAMI,KAAV,EAAiBQ,GAAGZ,MAAMQ,KAA1B,EAAP;AACH,SARD;;AAUA;;;AAGA,YAAMvB,YAAY,SAAZA,SAAY,CAASe,KAAT,EAAgB;AAC9B,iBAAKa,cAAL,GAAsBV,kBAAkBH,KAAlB,CAAtB;AACAvB,wBAAYwB,IAAZ,CAAiB,IAAjB;AACH,SAHD;;AAKA;;;AAGA,YAAMZ,aAAa,SAAbA,UAAa,GAAW;AAC1BG,0BAAcS,IAAd,CAAmB,IAAnB;AACA,iBAAKb,KAAL,GAAa,IAAb;AACAX,wBAAYwB,IAAZ,CAAiB,IAAjB;;AAEA;AACA5B,cAAE,IAAF,EAAQ6B,OAAR,CAAgB,iBAAhB;AACH,SAPD;;AASA;;;;;AAKA,YAAMY,YAAY,SAAZA,SAAY,GAAW;AACzB,gBAAMC,QAAQ1C,EAAE,IAAF,EAAQkC,UAAR,EAAd;AACA,gBAAMS,SAAS3C,EAAE,IAAF,EAAQqC,WAAR,EAAf;AACA,gBAAMJ,OAAOjC,EAAE,IAAF,EAAQgC,MAAR,GAAiBC,IAA9B;AACA,gBAAMG,MAAMpC,EAAE,IAAF,EAAQgC,MAAR,GAAiBI,GAA7B;AACA,gBAAMQ,cAAc,CAAC,KAAKJ,cAAL,CAAoBF,CAApB,GAAwBL,IAAzB,IAAiCS,KAArD;AACA,gBAAMG,cAAc,CAAC,KAAKL,cAAL,CAAoBD,CAApB,GAAwBH,GAAzB,IAAgCO,MAApD;AACA;AACA,gBAAMG,QAAQ,CAAE,KAAKhC,QAAL,CAAciC,OAAd,GAAwB,CAAzB,GAAgCH,WAAD,GAAgB,KAAK9B,QAAL,CAAciC,OAA9D,EAAwEC,OAAxE,CAAgF,CAAhF,CAAd;AACA,gBAAMC,QAAQ,CAAGJ,WAAD,GAAgB,KAAK/B,QAAL,CAAciC,OAA/B,GAA2C,KAAKjC,QAAL,CAAciC,OAAd,GAAwB,CAApE,EAAwEC,OAAxE,CAAgF,CAAhF,CAAd;AACA;AACA,gBAAME,QAAQC,KAAKC,KAAL,CAAW,KAAKZ,cAAL,CAAoBF,CAApB,IAAyBL,OAAKS,QAAM,CAApC,CAAX,EAAkD,EAAG,KAAKF,cAAL,CAAoBD,CAApB,IAAyBH,MAAIO,SAAO,CAApC,CAAH,CAAlD,KAAgG,MAAIQ,KAAKE,EAAzG,CAAd;AACA;AACA,mBAAO,EAACP,YAAD,EAAQG,YAAR,EAAe,eAAeL,cAAc,GAA5C,EAAiD,eAAeC,cAAc,GAA9E,EAAmFK,YAAnF,EAAP;AACH,SAdD;;AAgBA;;;AAGA,YAAM3C,mBAAmB,SAAnBA,gBAAmB,GAAW;AAChC,iBAAK+C,UAAL,GAAkBb,UAAUb,IAAV,CAAe,IAAf,CAAlB;;AAEA,gBAAI,KAAKb,KAAT,EAAgB;AACZ,qBAAKA,KAAL,GAAa,KAAb;AACAf,kBAAE,IAAF,EAAQsB,GAAR,CAAY,WAAZ,mBAAwC,KAAKR,QAAL,CAAcyC,WAAtD;;AAEA;AACA,oBAAI,KAAKzC,QAAL,CAAcG,KAAlB,EAAwB;AACpB,yBAAKQ,YAAL,CAAkBH,GAAlB,CAAsB,WAAtB;AACA,yBAAKG,YAAL,CAAkBH,GAAlB,CAAsB,SAAtB;AACH;;AAED;AACH,aAXD,MAWO;AACHtB,kBAAE,IAAF,EAAQsB,GAAR,CAAY,WAAZ,mBAAwC,KAAKR,QAAL,CAAcyC,WAAtD,qBAAgF,KAAKzC,QAAL,CAAc0C,WAAd,KAA8B,GAA9B,GAAoC,CAApC,GAAwC,KAAKF,UAAL,CAAgBL,KAAxI,uBAA6J,KAAKnC,QAAL,CAAc0C,WAAd,KAA8B,GAA9B,GAAoC,CAApC,GAAwC,KAAKF,UAAL,CAAgBR,KAArN,sBAA0O,KAAKhC,QAAL,CAAc2C,KAAxP,SAAiQ,KAAK3C,QAAL,CAAc2C,KAA/Q,SAAwR,KAAK3C,QAAL,CAAc2C,KAAtS;;AAEA;AACA,oBAAI,KAAK3C,QAAL,CAAcG,KAAlB,EAAwB;AACpB,yBAAKQ,YAAL,CAAkBH,GAAlB,CAAsB,WAAtB,cAA6C,KAAKgC,UAAL,CAAgBJ,KAA7D;AACA,yBAAKzB,YAAL,CAAkBH,GAAlB,CAAsB,SAAtB,OAAoC,KAAKgC,UAAL,CAAgBT,WAAhB,GAA8B,KAAK/B,QAAL,CAAc4C,QAA5C,GAAuD,GAA3F;AACH;AACJ;;AAED;AACA1D,cAAE,IAAF,EAAQ6B,OAAR,CAAgB,QAAhB,EAA0B,CAAC,KAAKyB,UAAN,CAA1B;;AAEA,iBAAKjD,OAAL,GAAe,KAAf;AACH,SA5BD;;AA8BA;;;AAGA,YAAMsD,eAAe,SAAfA,YAAe,GAAY;AAC7B,gBAAMC,iBAAiB,KAAK9C,QAAL,CAAc8C,cAArC;;AAEA;AACA,gBAAI,CAACA,cAAL;AACA;AACI5D,kBAAE,IAAF,EAAQ6D,MAAR,CAAe,0EAAf;;AAEJ;AACA,iBAAKC,mBAAL,GAA2B9D,EAAE,IAAF,EAAQ+D,IAAR,CAAa,gBAAb,CAA3B;AACA,iBAAKtC,YAAL,GAAoBzB,EAAE,IAAF,EAAQ+D,IAAR,CAAa,sBAAb,CAApB;;AAEA;AACA,gBAAIH,cAAJ,EAAoB;;AAEpB;AACA,gBAAMI,UAAU;AACZ,4BAAY,UADA;AAEZ,uBAAO,GAFK;AAGZ,wBAAQ,GAHI;AAIZ,yBAAS,MAJG;AAKZ,0BAAU;AALE,aAAhB;;AAQA;AACA,iBAAKF,mBAAL,CAAyBxC,GAAzB,CAA6B0C,OAA7B,EAAsC1C,GAAtC,CAA0C;AACtC,4BAAY,QAD0B;AAEtC,kCAAkB;AAFoB,aAA1C;;AAKA;AACA,iBAAKG,YAAL,CAAkBH,GAAlB,CAAsB;AAClB,4BAAY,UADM;AAElB,uBAAO,KAFW;AAGlB,wBAAQ,KAHU;AAIlB,6GAJkB;AAKlB,8BAAYtB,EAAE,IAAF,EAAQkC,UAAR,KAAqB,CALf;AAMlB,+BAAalC,EAAE,IAAF,EAAQkC,UAAR,KAAqB,CANhB;AAOlB,6BAAa,sCAPK;AAQlB,oCAAoB,OARF;AASlB,2BAAW;AATO,aAAtB;AAYH,SA3CD;;AA6CA;;;AAGA,YAAMhB,kBAAkB,SAAlBA,eAAkB,GAAY;AAChC,iBAAKO,YAAL,CAAkBH,GAAlB,CAAsB;AAClB,8BAAYtB,EAAE,IAAF,EAAQkC,UAAR,KAAqB,CADf;AAElB,+BAAalC,EAAE,IAAF,EAAQkC,UAAR,KAAqB;AAFhB,aAAtB;AAIH,SALD;;AAOA;;;AAGAlC,UAAEC,EAAF,CAAKC,IAAL,CAAU+D,OAAV,GAAoB,YAAW;AAC3BjE,cAAE,IAAF,EAAQkE,IAAR,CAAa,YAAY;AACrBlE,kBAAE,IAAF,EAAQ+D,IAAR,CAAa,gBAAb,EAA+BI,MAA/B;AACAnE,kBAAE,IAAF,EAAQsB,GAAR,CAAY,EAAC,eAAe,EAAhB,EAAoB,aAAa,EAAjC,EAAZ;AACAtB,kBAAE,IAAF,EAAQoE,GAAR,CAAY,iCAAZ;AACH,aAJD;AAKH,SAND;;AAQApE,UAAEC,EAAF,CAAKC,IAAL,CAAUuC,SAAV,GAAsB,YAAW;AAC7B,gBAAM4B,UAAU,EAAhB;AACArE,cAAE,IAAF,EAAQkE,IAAR,CAAa,YAAY;AACrB,qBAAK1B,cAAL,GAAsBV,kBAAkBF,IAAlB,CAAuB,IAAvB,CAAtB;AACAyC,wBAAQC,IAAR,CAAa7B,UAAUb,IAAV,CAAe,IAAf,CAAb;AACH,aAHD;AAIA,mBAAOyC,OAAP;AACH,SAPD;;AASArE,UAAEC,EAAF,CAAKC,IAAL,CAAUa,KAAV,GAAkB,YAAW;AACzBf,cAAE,IAAF,EAAQkE,IAAR,CAAa,YAAY;AAAA;;AACrB,qBAAK1B,cAAL,GAAsBV,kBAAkBF,IAAlB,CAAuB,IAAvB,CAAtB;AACA,qBAAKd,QAAL,GAAgBd,EAAE,IAAF,EAAQuE,IAAR,CAAa,UAAb,CAAhB;AACAvD,2BAAWY,IAAX,CAAgB,IAAhB;AACAF,2BAAW,YAAM;AACb,2BAAKX,KAAL,GAAa,KAAb;AACH,iBAFD,EAEG,KAAKD,QAAL,CAAc0D,UAFjB;AAGH,aAPD;AAQH,SATD;;AAWA;;;AAGA,eAAO,KAAKN,IAAL,CAAU,YAAY;AAAA;;AAEzB;;;;;AAKA,iBAAKpD,QAAL,GAAgBd,EAAEyE,MAAF,CAAS;AACrB1B,yBAAS/C,EAAE,IAAF,EAAQ0E,EAAR,CAAW,iBAAX,IAAgC1E,EAAE,IAAF,EAAQuE,IAAR,CAAa,UAAb,CAAhC,GAA2D,EAD/C;AAErBhB,6BAAavD,EAAE,IAAF,EAAQ0E,EAAR,CAAW,yBAAX,IAAwC1E,EAAE,IAAF,EAAQuE,IAAR,CAAa,kBAAb,CAAxC,GAA2E,GAFnE;AAGrB/C,wBAAQxB,EAAE,IAAF,EAAQ0E,EAAR,CAAW,oBAAX,IAAmC1E,EAAE,IAAF,EAAQuE,IAAR,CAAa,aAAb,CAAnC,GAAiE,+BAHpD;AAIrBd,uBAAOzD,EAAE,IAAF,EAAQ0E,EAAR,CAAW,mBAAX,IAAkC1E,EAAE,IAAF,EAAQuE,IAAR,CAAa,YAAb,CAAlC,GAA+D,GAJjD;AAKrBhD,uBAAOvB,EAAE,IAAF,EAAQ0E,EAAR,CAAW,mBAAX,IAAkC1E,EAAE,IAAF,EAAQuE,IAAR,CAAa,YAAb,CAAlC,GAA+D,KALjD;AAMrBC,4BAAYxE,EAAE,IAAF,EAAQ0E,EAAR,CAAW,wBAAX,IAAuC1E,EAAE,IAAF,EAAQuE,IAAR,CAAa,iBAAb,CAAvC,GAAyE,IANhE;AAOrBf,6BAAaxD,EAAE,IAAF,EAAQ0E,EAAR,CAAW,0BAAX,IAAyC1E,EAAE,IAAF,EAAQuE,IAAR,CAAa,mBAAb,CAAzC,GAA6E,IAPrE;AAQrBI,sBAAM3E,EAAE,IAAF,EAAQ0E,EAAR,CAAW,kBAAX,IAAiC1E,EAAE,IAAF,EAAQuE,IAAR,CAAa,WAAb,CAAjC,GAA6D,IAR9C;AASrBxD,uBAAOf,EAAE,IAAF,EAAQ0E,EAAR,CAAW,mBAAX,IAAkC1E,EAAE,IAAF,EAAQuE,IAAR,CAAa,YAAb,CAAlC,GAA+D,IATjD;AAUrBtD,uBAAOjB,EAAE,IAAF,EAAQ0E,EAAR,CAAW,mBAAX,IAAkC1E,EAAE,IAAF,EAAQuE,IAAR,CAAa,YAAb,CAAlC,GAA+D,KAVjD;AAWrBb,0BAAU1D,EAAE,IAAF,EAAQ0E,EAAR,CAAW,sBAAX,IAAqC1E,EAAE,IAAF,EAAQuE,IAAR,CAAa,eAAb,CAArC,GAAqE;AAX1D,aAAT,EAYbpE,OAZa,CAAhB;;AAcA;AACA,gBAAG,KAAKW,QAAL,CAAc6D,IAAd,KAAuB,IAA1B,EAA+B;AAC3BC,wBAAQC,IAAR,CAAa,iIAAb;AACA,qBAAK/D,QAAL,CAAc0C,WAAd,GAA4B,KAAK1C,QAAL,CAAc6D,IAA1C;AACH;;AAED,iBAAKG,IAAL,GAAY,YAAM;AACd;AACA9E,0BAAQuE,IAAR,CAAa,UAAb,EAAyB,OAAKzD,QAA9B;;AAEA;AACA,oBAAG,OAAKA,QAAL,CAAcG,KAAjB,EAAwB0C,aAAa/B,IAAb;;AAExB;AACAnB,2BAAWmB,IAAX;AACH,aATD;;AAWA;AACA,iBAAKkD,IAAL;AAEH,SAzCM,CAAP;AA0CH,KA7QD;;AA+QA;;;AAGA9E,MAAE,aAAF,EAAiBE,IAAjB;;AAEA,WAAO,IAAP;AACH,CAhTA,CAAD", "file": "tilt.jquery.js", "sourcesContent": ["(function (factory) {\n    if (typeof define === 'function' && define.amd) {\n        // AMD. Register as an anonymous module.\n        define(['jquery'], factory);\n    } else if (typeof module === 'object' && module.exports) {\n        // Node/CommonJS\n        module.exports = function( root, jQuery ) {\n            if ( jQuery === undefined ) {\n                // require('jQuery') returns a factory that requires window to\n                // build a jQuery instance, we normalize how we use modules\n                // that require this pattern but the window provided is a noop\n                // if it's defined (how jquery works)\n                if ( typeof window !== 'undefined' ) {\n                    jQuery = require('jquery');\n                }\n                else {\n                    jQuery = require('jquery')(root);\n                }\n            }\n            factory(jQuery);\n            return jQuery;\n        };\n    } else {\n        // Browser globals\n        factory(jQuery);\n    }\n}(function ($) {\n    $.fn.tilt = function (options) {\n\n        /**\n         * RequestAnimationFrame\n         */\n        const requestTick = function() {\n            if (this.ticking) return;\n            requestAnimationFrame(updateTransforms.bind(this));\n            this.ticking = true;\n        };\n\n        /**\n         * Bind mouse movement evens on instance\n         */\n        const bindEvents = function() {\n            const _this = this;\n            $(this).on('mousemove', mouseMove);\n            $(this).on('mouseenter', mouseEnter);\n            if (this.settings.reset) $(this).on('mouseleave', mouseLeave);\n            if (this.settings.glare) $(window).on('resize', updateGlareSize.bind(_this));\n        };\n\n        /**\n         * Set transition only on mouse leave and mouse enter so it doesn't influence mouse move transforms\n         */\n        const setTransition = function() {\n            if (this.timeout !== undefined) clearTimeout(this.timeout);\n            $(this).css({'transition': `${this.settings.speed}ms ${this.settings.easing}`});\n            if(this.settings.glare) this.glareElement.css({'transition': `opacity ${this.settings.speed}ms ${this.settings.easing}`});\n            this.timeout = setTimeout(() => {\n                $(this).css({'transition': ''});\n                if(this.settings.glare) this.glareElement.css({'transition': ''});\n            }, this.settings.speed);\n        };\n\n        /**\n         * When user mouse enters tilt element\n         */\n        const mouseEnter = function(event) {\n            this.ticking = false;\n            $(this).css({'will-change': 'transform'});\n            setTransition.call(this);\n\n            // Trigger change event\n            $(this).trigger(\"tilt.mouseEnter\");\n        };\n\n        /**\n         * Return the x,y position of the mouse on the tilt element\n         * @returns {{x: *, y: *}}\n         */\n        const getMousePositions = function(event) {\n            if (typeof(event) === \"undefined\") {\n                event = {\n                    pageX: $(this).offset().left + $(this).outerWidth() / 2,\n                    pageY: $(this).offset().top + $(this).outerHeight() / 2\n                };\n            }\n            return {x: event.pageX, y: event.pageY};\n        };\n\n        /**\n         * When user mouse moves over the tilt element\n         */\n        const mouseMove = function(event) {\n            this.mousePositions = getMousePositions(event);\n            requestTick.call(this);\n        };\n\n        /**\n         * When user mouse leaves tilt element\n         */\n        const mouseLeave = function() {\n            setTransition.call(this);\n            this.reset = true;\n            requestTick.call(this);\n\n            // Trigger change event\n            $(this).trigger(\"tilt.mouseLeave\");\n        };\n\n        /**\n         * Get tilt values\n         *\n         * @returns {{x: tilt value, y: tilt value}}\n         */\n        const getValues = function() {\n            const width = $(this).outerWidth();\n            const height = $(this).outerHeight();\n            const left = $(this).offset().left;\n            const top = $(this).offset().top;\n            const percentageX = (this.mousePositions.x - left) / width;\n            const percentageY = (this.mousePositions.y - top) / height;\n            // x or y position inside instance / width of instance = percentage of position inside instance * the max tilt value\n            const tiltX = ((this.settings.maxTilt / 2) - ((percentageX) * this.settings.maxTilt)).toFixed(2);\n            const tiltY = (((percentageY) * this.settings.maxTilt) - (this.settings.maxTilt / 2)).toFixed(2);\n            // angle\n            const angle = Math.atan2(this.mousePositions.x - (left+width/2),- (this.mousePositions.y - (top+height/2)) )*(180/Math.PI);\n            // Return x & y tilt values\n            return {tiltX, tiltY, 'percentageX': percentageX * 100, 'percentageY': percentageY * 100, angle};\n        };\n\n        /**\n         * Update tilt transforms on mousemove\n         */\n        const updateTransforms = function() {\n            this.transforms = getValues.call(this);\n\n            if (this.reset) {\n                this.reset = false;\n                $(this).css('transform', `perspective(${this.settings.perspective}px) rotateX(0deg) rotateY(0deg)`);\n\n                // Rotate glare if enabled\n                if (this.settings.glare){\n                    this.glareElement.css('transform', `rotate(180deg) translate(-50%, -50%)`);\n                    this.glareElement.css('opacity', `0`);\n                }\n\n                return;\n            } else {\n                $(this).css('transform', `perspective(${this.settings.perspective}px) rotateX(${this.settings.disableAxis === 'x' ? 0 : this.transforms.tiltY}deg) rotateY(${this.settings.disableAxis === 'y' ? 0 : this.transforms.tiltX}deg) scale3d(${this.settings.scale},${this.settings.scale},${this.settings.scale})`);\n\n                // Rotate glare if enabled\n                if (this.settings.glare){\n                    this.glareElement.css('transform', `rotate(${this.transforms.angle}deg) translate(-50%, -50%)`);\n                    this.glareElement.css('opacity', `${this.transforms.percentageY * this.settings.maxGlare / 100}`);\n                }\n            }\n\n            // Trigger change event\n            $(this).trigger(\"change\", [this.transforms]);\n\n            this.ticking = false;\n        };\n\n        /**\n         * Prepare elements\n         */\n        const prepareGlare = function () {\n            const glarePrerender = this.settings.glarePrerender;\n\n            // If option pre-render is enabled we assume all html/css is present for an optimal glare effect.\n            if (!glarePrerender)\n            // Create glare element\n                $(this).append('<div class=\"js-tilt-glare\"><div class=\"js-tilt-glare-inner\"></div></div>');\n\n            // Store glare selector if glare is enabled\n            this.glareElementWrapper = $(this).find(\".js-tilt-glare\");\n            this.glareElement = $(this).find(\".js-tilt-glare-inner\");\n\n            // Remember? We assume all css is already set, so just return\n            if (glarePrerender) return;\n\n            // Abstracted re-usable glare styles\n            const stretch = {\n                'position': 'absolute',\n                'top': '0',\n                'left': '0',\n                'width': '100%',\n                'height': '100%',\n            };\n\n            // Style glare wrapper\n            this.glareElementWrapper.css(stretch).css({\n                'overflow': 'hidden',\n                'pointer-events': 'none',\n            });\n\n            // Style glare element\n            this.glareElement.css({\n                'position': 'absolute',\n                'top': '50%',\n                'left': '50%',\n                'background-image': `linear-gradient(0deg, rgba(255,255,255,0) 0%, rgba(255,255,255,1) 100%)`,\n                'width': `${$(this).outerWidth()*2}`,\n                'height': `${$(this).outerWidth()*2}`,\n                'transform': 'rotate(180deg) translate(-50%, -50%)',\n                'transform-origin': '0% 0%',\n                'opacity': '0',\n            });\n\n        };\n\n        /**\n         * Update glare on resize\n         */\n        const updateGlareSize = function () {\n            this.glareElement.css({\n                'width': `${$(this).outerWidth()*2}`,\n                'height': `${$(this).outerWidth()*2}`,\n            });\n        };\n\n        /**\n         * Public methods\n         */\n        $.fn.tilt.destroy = function() {\n            $(this).each(function () {\n                $(this).find('.js-tilt-glare').remove();\n                $(this).css({'will-change': '', 'transform': ''});\n                $(this).off('mousemove mouseenter mouseleave');\n            });\n        };\n\n        $.fn.tilt.getValues = function() {\n            const results = [];\n            $(this).each(function () {\n                this.mousePositions = getMousePositions.call(this);\n                results.push(getValues.call(this));\n            });\n            return results;\n        };\n\n        $.fn.tilt.reset = function() {\n            $(this).each(function () {\n                this.mousePositions = getMousePositions.call(this);\n                this.settings = $(this).data('settings');\n                mouseLeave.call(this);\n                setTimeout(() => {\n                    this.reset = false;\n                }, this.settings.transition);\n            });\n        };\n\n        /**\n         * Loop every instance\n         */\n        return this.each(function () {\n\n            /**\n             * Default settings merged with user settings\n             * Can be set trough data attributes or as parameter.\n             * @type {*}\n             */\n            this.settings = $.extend({\n                maxTilt: $(this).is('[data-tilt-max]') ? $(this).data('tilt-max') : 20,\n                perspective: $(this).is('[data-tilt-perspective]') ? $(this).data('tilt-perspective') : 300,\n                easing: $(this).is('[data-tilt-easing]') ? $(this).data('tilt-easing') : 'cubic-bezier(.03,.98,.52,.99)',\n                scale: $(this).is('[data-tilt-scale]') ? $(this).data('tilt-scale') : '1',\n                speed: $(this).is('[data-tilt-speed]') ? $(this).data('tilt-speed') : '400',\n                transition: $(this).is('[data-tilt-transition]') ? $(this).data('tilt-transition') : true,\n                disableAxis: $(this).is('[data-tilt-disable-axis]') ? $(this).data('tilt-disable-axis') : null,\n                axis: $(this).is('[data-tilt-axis]') ? $(this).data('tilt-axis') : null,\n                reset: $(this).is('[data-tilt-reset]') ? $(this).data('tilt-reset') : true,\n                glare: $(this).is('[data-tilt-glare]') ? $(this).data('tilt-glare') : false,\n                maxGlare: $(this).is('[data-tilt-maxglare]') ? $(this).data('tilt-maxglare') : 1,\n            }, options);\n\n            // Add deprecation warning & set disableAxis to deprecated axis setting\n            if(this.settings.axis !== null){\n                console.warn('Tilt.js: the axis setting has been renamed to disableAxis. See https://github.com/gijsroge/tilt.js/pull/26 for more information');\n                this.settings.disableAxis = this.settings.axis;\n            }\n\n            this.init = () => {\n                // Store settings\n                $(this).data('settings', this.settings);\n\n                // Prepare element\n                if(this.settings.glare) prepareGlare.call(this);\n\n                // Bind events\n                bindEvents.call(this);\n            };\n\n            // Init\n            this.init();\n\n        });\n    };\n\n    /**\n     * Auto load\n     */\n    $('[data-tilt]').tilt();\n\n    return true;\n}));"]}