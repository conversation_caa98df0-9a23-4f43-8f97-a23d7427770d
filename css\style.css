@charset "UTF-8";

/* Litho - v1.1
 * https://www.themezaa.com/
 * Copyright (c) 2021 themezaa.com

----------------------------------
Table of contents

01. Google font
02. Reset
03. Typography
04. Background color
05. Custom
06. Button
07. Border
08. Video
09. Background image
10. Margin
11. Padding
12. Display and overflow
13. Position
14. Width
15. Height
16. Icon
17. Magnific popup
18. Header
19. Page title
20. Swiper carousel
21. Grid
22. Portfolio
23. Blog
24. Shop
25. Elements
26. Footer
27. home and other pages
28. Others
 
*/

/* ===================================
    01. Google font
====================================== */

@import url('https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900&display=swap');
@import url('https://fonts.googleapis.com/css?family=Poppins:100,100i,200,200i,300,300i,400,400i,500,500i,600,600i,700,700i,800,800i,900,900i&display=swap');

/* ===================================
    02. Reset
====================================== */

html { font-size: 14px; }
*, *::after, *::before { box-sizing: border-box; }
body { font-size: 15px; line-height: 30px; font-weight: 400; color: #828282; font-family: 'Roboto', sans-serif; padding: 0; margin: 0; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; word-break: break-word }
body, html { -moz-osx-font-smoothing: grayscale; }
a:hover, a:focus { text-decoration: none; outline: none; outline-offset:0; color: #0038e3; }
a, a:active, a:focus { color: #828282; }
a, .btn, input, textarea, select { transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out; }
img { max-width: 100%; height: auto; }
video { background-size: cover; display: table-cell; vertical-align: middle; width: 100%; }
p { margin: 0 0 25px; }
b, strong { font-weight: 600; }
.alt-font strong { font-weight: 600; }
.last-paragraph-no-margin p:last-of-type { margin-bottom:0; }
ul, ol, dl { list-style-position: outside; margin-bottom: 25px; }
ul { margin: 0; padding: 0; }
input, textarea, select { border: 1px solid #dfdfdf; padding: 10px 15px; width: 100%; margin: 0 0 15px 0; max-width: 100%; resize: none; outline: none; color: #828282; line-height: initial; }
select { background-image: url(../images/down-arrow.png); background-position: right 14px center; background-repeat: no-repeat; background-size: 10px; border-radius: 0; -webkit-appearance: none; -moz-appearance: none; appearance: none; cursor: pointer;}

input[type="submit"] { width: auto; }
input[type="button"], input[type="text"], input[type="email"], input[type="search"], input[type="password"], textarea, input[type="submit"] { -webkit-appearance: none; }
input:focus, textarea:focus { border: 1px solid #b9b9b9; outline: none; }
input[type="submit"]:focus { outline: none; }
input[type=number]::-webkit-inner-spin-button, input[type=number]::-webkit-outer-spin-button { -webkit-appearance: none; -moz-appearance: none; appearance: none; margin: 0;  }
input::-ms-clear { display: none; }
input:-webkit-autofill, input:-webkit-autofill:hover, input:-webkit-autofill:focus, input:-webkit-autofill:active, select:-webkit-autofill, select:-webkit-autofill:hover, select:-webkit-autofill:focus, select:-webkit-autofill:active { transition: background-color 5000s; transition: all 0s 50000s;  -webkit-animation-name: autofill; -webkit-animation-fill-mode: both;  }
select::-ms-expand { display:none; }
iframe { border: 0; }
button, button:focus, button:hover { outline: none; }
.main-content { z-index: 2; background-color: #fff; overflow: visible; position: relative; }

::selection { color: #000; background:#dbdbdb; }
::-moz-selection { color:#a6a6a6; background:#dbdbdb; }
::-webkit-input-placeholder { color: #a6a6a6; text-overflow: ellipsis; }
::-moz-placeholder { color: #a6a6a6; text-overflow: ellipsis; opacity:1; }
:-ms-input-placeholder { color: #a6a6a6; text-overflow: ellipsis; opacity:1; }

.placeholder-light::-webkit-input-placeholder { color: #fff; }
.placeholder-light::-moz-placeholder { color: #fff;}
.placeholder-light:-ms-input-placeholder { color: #fff;}

.placeholder-dark::-webkit-input-placeholder { color: #232323; }
.placeholder-dark::-moz-placeholder { color: #232323;}
.placeholder-dark:-ms-input-placeholder { color: #232323;}

.no-transition { transition: all 0s ease-in-out !important; -moz-transition: all 0s ease-in-out !important; -webkit-transition: all 0s ease-in-out !important; -ms-transition: all 0s ease-in-out !important; -o-transition: all 0s ease-in-out !important; }
.transition { transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out; }

/* ===================================
    03. Typography
====================================== */

/* font family */
.alt-font { font-family: 'Poppins', sans-serif; }
.main-font { font-family: 'Roboto', sans-serif !important; }

/* heading size */
h1, h2, h3, h4, h5, h6 { margin:0 0 25px 0; padding:0; letter-spacing: -0.5px; font-weight: inherit; }
h1 { font-size:5rem; line-height: 5rem; }
h2 { font-size:3.9rem; line-height:4.2rem; }
h3 { font-size:3.4rem; line-height:3.9rem; }
h4 { font-size:2.85rem; line-height:3.4rem; }
h5 { font-size:2.30rem; line-height:3rem; }
h6 { font-size:1.80rem; line-height:2.5rem; }

/* text size */
.font-size-0 { font-size: 0; }
.text-very-small { font-size:11px; line-height:14px; }
.text-extra-small { font-size:12px; line-height:16px; }
.text-small { font-size:13px; line-height:22px; }
.text-medium { font-size:14px; line-height:20px; }
.text-extra-medium { font-size:16px; line-height:28px; }
.text-large { font-size:18px; line-height:22px; }
.text-extra-large { font-size: 20px; line-height:28px; }
.title-extra-small { font-size: 30px; line-height: 32px; }
.title-small { font-size: 36px; line-height: 42px; }
.title-large { font-size: 100px; line-height:95px; }
.title-large-2 { font-size: 70px; line-height:80px; }
.title-large-3 { font-size: 80px; line-height:80px; }
.title-extra-large { font-size: 130px; line-height:120px; }
.title-extra-large-heavy { font-size: 11rem; line-height: 11rem; }
.text-big{ font-size: 200px; line-height: 200px;}
.text-extra-big{ font-size: 230px; line-height: 230px;}
.text-extra-big-2{ font-size: 300px; line-height: 260px;}
.parallax-title { padding: 16px 35px; display: inline-block }

/* text color, link color and link hover color */
.text-white, .btn-link.text-white, a.text-white-hover:hover { color:#fff; }
.text-black, .btn-link.text-black,  a.text-black-hover:hover { color:#000; }
.text-extra-dark-gray, .btn-link.text-extra-dark-gray, a.text-extra-dark-gray-hover:hover { color:#232323; }
.text-dark-gray, .btn-link.text-dark-gray, a.text-dark-gray-hover:hover { color:#626262; }
.text-extra-medium-gray, .btn-link.text-extra-medium-gray, a.text-extra-medium-gray-hover:hover { color: #757575; }
.text-medium-gray, .btn-link.text-medium-gray, a.text-medium-gray-hover:hover { color:#939393; }
.text-extra-light-gray, .btn-link.text-extra-light-gray, a.text-extra-light-gray-hover:hover { color:#b7b7b7; }
.text-light-gray, .btn-link.text-light-gray, a.text-light-gray-hover:hover { color:#d6d5d5; }
.text-very-light-gray, .btn-link.text-very-light-gray, a.text-very-light-gray-hover:hover { color:#ededed; }
.text-fast-blue, .btn-link.text-fast-blue, a.text-fast-blue-hover:hover { color: #0038e3; }
.text-neon-blue, .btn-link.text-neon-blue, a.text-neon-blue-hover:hover { color: #3452ff; }
.text-light-pink, .btn-link.text-light-pink, a.text-light-pink-hover:hover { color: #f18c8c; }
.text-red, .btn-link.text-red, a.text-red-hover:hover { color: #fe0127; }
.text-dark-red, .btn-link.text-dark-red, a.text-dark-red-hover:hover { color: #d23134; }
.text-parrot-green, .btn-link.text-parrot-green, a.text-parrot-green-hover:hover { color: #cee002; }
.text-green, .btn-link.text-green, a.text-green-hover:hover { color: #27AE60; }
.text-olivine-green, .btn-link.text-olivine-green, a.text-olivine-green-hover:hover { color: #8bb867; }
.text-dark-charcoal, .btn-link.text-dark-charcoal, a.text-dark-charcoal-hover:hover { color: #2f2f2f; }
.text-penguin-white, .btn-link.text-penguin-white, a.text-penguin-white-hover:hover { color: #f1edea; }
.text-orange, .btn-link.text-orange, a.text-orange-hover:hover { color: #fd961e; }
.text-dark-orange, .btn-link.text-dark-orange, a.text-dark-orange-hover:hover { color: #ff6437; }
.text-neon-orange, .btn-link.text-neon-orange, a.text-neon-orange-hover:hover { color: #ff7a56; }
.text-light-orange, .btn-link.text-light-orange, a.text-light-orange-hover:hover { color: #e8bf47; }
.text-dark-red, .btn-link.text-dark-red, a.text-dark-red-hover:hover { color: #e12837; }
.text-radical-red, .btn-link.text-radical-red, a.text-radical-red-hover:hover { color: #fb4f58; }
.text-golden, .btn-link.text-golden, a.text-golden-hover:hover { color: #ca943d; }
.text-golden-yellow, .btn-link.text-golden-yellow, a.text-golden-yellow-hover:hover { color: #FF9C00; }
.text-dark-golden-yellow, .btn-link.text-dark-golden-yellow, a.text-dark-golden-yellow-hover:hover { color: #cb933d; }
.text-golden-brown, .btn-link.text-golden-brown, a.text-golden-brown-hover:hover { color: #cd9452; }
.text-slate-blue, .btn-link.text-slate-blue, a.text-slate-blue-hover:hover { color: #262b35; }
.text-slate-blue-light, .btn-link.text-slate-blue-light, a.text-slate-blue-light-hover:hover { color: #8890a4; }
.text-medium-slate-blue, .btn-link.text-medium-slate-blue, a.text-medium-slate-blue-hover:hover { color: #374162; }
.text-dark-slate-blue, .btn-link.text-dark-slate-blue, a.text-dark-slate-blue-hover:hover { color: #1f232c; }
.text-brown, .btn-link.text-brown, a.text-brown-hover:hover { color: #986248; }
.text-blue-wood, .btn-link.text-blue-wood, a.text-blue-wood-hover:hover { color: #373a63; }
.text-purple, .btn-link.text-purple, a.text-purple-hover:hover { color: #502e95; }
.text-medium-purple, .btn-link.text-medium-purple, a.text-medium-purple-hover:hover { color: #3a285e; }
.text-dark-purple, .btn-link.text-dark-purple, a.text-dark-purple-hover:hover { color: #333045; }
.text-very-light-desert-storm, .btn-link.text-very-light-desert-storm, a.very-light-desert-storm:hover { color: #faf6f3; }
.text-yellow-ochre, .btn-link.text-yellow-ochre, a.text-yellow-ochre-hover:hover { color: #c89965; }
.text-yellow-ochre-light, .btn-link.text-yellow-ochre-light, a.text-yellow-ochre-light-hover:hover { color: #c3964e; }
.text-fast-yellow, .btn-link.text-fast-yellow, a.text-fast-yellow-hover:hover { color: #F4D956; }
.text-yellow, .btn-link.text-yellow, a.text-yellow-hover:hover { color: #ffeb04; }
.text-salmon-rose, .btn-link.text-salmon-rose, a.text-salmon-rose-hover:hover { color: #ea9d8d; }
.text-tussock, .btn-link.text-tussock, a.text-tussock-hover:hover { color: #bf8c4c; }
.text-greenish-slate, .btn-link.text-greenish-slate, a.text-greenish-slate-hover:hover { color: #445a58; }
.text-seashell, .btn-link.text-seashell, a.text-seashell-hover:hover { color: #f9f6f3; }
.text-shamrock-green, .btn-link.text-shamrock-green, a.text-shamrock-green-hover:hover { color: #2ed47c; }
.text-light-brownish-orange, .btn-link.text-light-brownish-orange, a.text-light-brownish-orange-hover:hover { color: #e6994e; }
.text-greenish-gray, .btn-link.text-greenish-gray, a.text-greenish-gray-hover:hover { color: #969896; }
.text-light-greenish-gray, .btn-link.text-light-greenish-gray, a.text-light-greenish-gray-hover:hover { color: #9fa29f; }
.text-yellowish-green, .btn-link.text-yellowish-green, a.text-yellowish-green-hover:hover { color: #c7da26; }
.text-copper-red, .btn-link.text-copper-red, a.text-copper-red-hover:hover { color: #cc754c; }
.text-sunglow, .btn-link.text-sunglow, a.text-sunglow-hover:hover { color: #ffcc2e; }
.text-orange-zest, .btn-link.text-orange-zest, a.text-orange-zest-hover:hover  { color: #CA7741; }
.text-dark-tussock, .btn-link.text-dark-tussock, a.text-dark-tussock-hover:hover  { color: #BD884C; }
.text-echo-blue, .btn-link.text-echo-blue, a.text-echo-blue-hover:hover  { color: #abadb7; }
.text-violet, .btn-link.text-violet, a.text-violet-hover:hover  { color: #89af44; }
.text-iris-blue, .btn-link.text-iris-blue, a.text-iris-blue-hover:hover  { color: #00B8B8; }
.text-very-light-gray-transparent, .btn-link.text-very-light-gray-transparent, a.text-very-light-gray-transparent-hover:hover { color:rgba(237, 237, 237, 0.22); }
.text-extra-dark-gray-transparent, .btn-link.text-extra-dark-gray-transparent, a.text-extra-dark-gray-transparent-hover:hover { color: rgba(46,46,46,0.15); }
.text-dark-gray-transparent, .btn-link.text-dark-gray-transparent, a.text-dark-gray-transparent-hover:hover { color: rgba(98,98,98,0.1); }
.text-white-transparent, .btn-link.text-white-transparent, a.text-white-transparent-hover:hover { color: rgba(255,255,255,0.7); }
.text-medium-white-transparent, .btn-link.text-medium-white-transparent, a.text-very-light-white-transparent-hover:hover { color: rgba(255,255,255,0.5); }
.text-very-light-white-transparent, .btn-link.text-very-light-white-transparent, a.text-very-light-white-transparent-hover:hover { color: rgba(255,255,255,0.3); }

/* text gradient color */
.text-gradient-magenta-orange { background: linear-gradient(to right, #e42464, #e42464, #f44f5c, #fa6259, #ff7357); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
.text-gradient-magenta-orange-2 { background: linear-gradient(to right, #ec606c, #ee6362, #ee6757, #ed6c4d, #eb7242); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
.text-gradient-purple-magenta { background: linear-gradient(to right, #502970, #502970, #ac2775, #d42d6b, #f34259); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
.text-gradient-light-purple-light-orange { background: linear-gradient(to right, #b783ff, #b783ff, #fa7cc1, #ff85a6, #ff9393); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
.text-gradient-sky-blue-pink { background: linear-gradient(to right, #556fff, #556fff, #e05fc4, #f767a6, #ff798e); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
.text-gradient-sky-blue-dark-pink { background: linear-gradient(to right, #5065da, #556fff, #c557ad, #e65f9a, #e66176); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
.text-gradient-sky-blue-pink-2 { background: linear-gradient(to right top, #6670ed, #556fff, #d45eba, #f767a6, #f5667d); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
.text-gradient-peacock-blue-crome-yellow { background: linear-gradient(to right, #36c1e1, #36c1e1, #74cf8e, #bac962, #feb95b); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
.text-gradient-peacock-blue-crome-yellow-2 { background-image: linear-gradient(to right, #5cc088, #50c28d, #83b879, #c9a95e, #dda556);; -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
.text-gradient-fast-blue-purple { background: linear-gradient(to right, #0039e3, #0039e3, #5e28dd, #741bd9, #8600d4); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
.text-gradient-tan-geraldine { background-image: linear-gradient(to right, #ff9e78, #ff9979, #ff887c, #ff7c7d, #ff767e); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
.text-gradient-tussock-greenish-slate { background: linear-gradient(90deg, rgba(191,140,76,1) 0%, rgba(68,90,88,1) 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
.text-gradient-light-purple-light-red { background: linear-gradient(to right, #975ade, #af63d2, #d473c0, #ef7da7, #e6686f); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
.text-gradient-light-brownish-orange-black { background: linear-gradient(to right, #e79e57, #9d9049, #62794c, #405c4c, #353d3c); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
.text-gradient-turquoise-green-yellowish { background: linear-gradient(to right, #3edfb6, #58e49a, #7ce878, #a5e850, #d0e51b); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
.text-gradient-red-violet-purple { background-image: linear-gradient(to right, #b93c90, #a92a96, #951a9d, #7b10a5, #5412ae); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
.text-gradient-orange-pink, .btn-link.text-gradient-orange-pink, a.text-gradient-orange-pink, a.text-gradient-orange-pink-hover:hover { background: linear-gradient(to right, #ff6557 0%, #ff367c 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
.text-gradient-fuego-turquoise, .btn-link.text-gradient-fuego-turquoise, a.text-gradient-fuego-turquoise, a.text-gradient-fuego-turquoise-hover:hover { background: linear-gradient(to left, #0cd6a2, #56de9c, #91dc5d, #aedb40, #c7da26); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }

/* text stroke */
.text-border { -webkit-text-fill-color: transparent; -webkit-text-stroke-width: 1px; -webkit-text-stroke-color: white; }

/* text stroke width */
.text-border-width-1px { -webkit-text-stroke-width: 1px; }
.text-border-width-2px { -webkit-text-stroke-width: 2px; }
.text-border-width-3px { -webkit-text-stroke-width: 3px; }
.text-border-width-4px { -webkit-text-stroke-width: 4px; }
.text-border-width-5px { -webkit-text-stroke-width: 5px; }

/* text stroke color */
.text-border-color-black { -webkit-text-stroke-color: #000000; }

/* letter spacing */
.letter-spacing-1-half { letter-spacing: 0.50px; }
.letter-spacing-1px { letter-spacing: 1px; }
.letter-spacing-2px { letter-spacing: 2px; }
.letter-spacing-3px { letter-spacing: 3px; }
.letter-spacing-4px { letter-spacing: 4px; }
.letter-spacing-5px { letter-spacing: 5px; }
.letter-spacing-8px { letter-spacing: 8px; }
.letter-spacing-10px { letter-spacing: 10px; }
.letter-spacing-15px { letter-spacing: 15px; }
.letter-spacing-20px { letter-spacing: 20px; }
.letter-spacing-22px { letter-spacing: 22px; }
.letter-spacing-25px { letter-spacing: 25px; }

/* letter spacing minus */
.letter-spacing-minus-1-half { letter-spacing: -0.50px; }
.letter-spacing-minus-1px { letter-spacing: -1px; }
.letter-spacing-minus-2px { letter-spacing: -2px; }
.letter-spacing-minus-3px { letter-spacing: -3px; }
.letter-spacing-minus-4px { letter-spacing: -4px; }
.letter-spacing-minus-5px { letter-spacing: -5px; }
.letter-spacing-minus-6px { letter-spacing: -6px; }
.letter-spacing-minus-7px { letter-spacing: -7px; }
.letter-spacing-minus-8px { letter-spacing: -8px; }
.letter-spacing-minus-9px { letter-spacing: -9px; }
.letter-spacing-minus-10px { letter-spacing: -10px; }
.letter-spacing-inherit { letter-spacing: inherit; }
.white-space-normal { white-space: normal; }

/* font weight */
.font-weight-100 { font-weight: 100 !important; }
.font-weight-200 { font-weight: 200 !important; }
.font-weight-300 { font-weight: 300 !important; }
.font-weight-400 { font-weight: 400 !important; }
.font-weight-500 { font-weight: 500 !important; }
.font-weight-600 { font-weight: 600 !important; }
.font-weight-700 { font-weight: 700 !important; }
.font-weight-800 { font-weight: 800 !important; }
.font-weight-900 { font-weight: 900 !important; }

/* text property */
.text-normal { text-transform: unset !important; }
.text-decoration-underline { text-decoration:underline; }
.text-decoration-none { text-decoration: none; }
.word-wrap { word-wrap: break-word; }
.text-transform-none { text-transform:none; }
.line-height-unset { line-height: unset; }
.line-height-normal { line-height: normal; }
.line-height-none { line-height: 0; }
.text-decoration-line-through { text-decoration:line-through; }
.text-decoration-underline-yellow { text-decoration: underline; text-decoration-color: #fee11c; -webkit-text-decoration-color: #fee11c; }
.text-highlight { padding: 4px 16px }

.text-middle-line, .text-decoration-line-wide, .text-decoration-line-thick, .text-decoration-line-bottom, .text-decoration-line-bottom-thick, .text-decoration-line-bottom-medium { position: relative; white-space: nowrap; }
.text-middle-line:before { border-bottom: 1px solid; position: absolute; content: ""; width: 100%; top: 50%; left: 0; opacity: 0.35; }
.text-decoration-line-wide:before { content: ""; border-bottom: 1px solid; left: -10px; position: absolute; top: 50%; width: calc(100% + 20px);-webkit-transform: translateY(-50%); -moz-transform: translateY(-50%); -ms-transform: translateY(-50%); -o-transform: translateY(-50%); transform: translateY(-50%); }
.text-decoration-line-thick:before { content: ""; position: absolute; width: 100%; height: 4px; background: #333333; left: 0; top: 50%; transform: translateY(-50%); -moz-transform: translateY(-50%); -ms-transform: translateY(-50%); -webkit-transform: translateY(-50%); z-index: -1; }
.text-decoration-line-bottom:before { content: ""; border-bottom: 1px solid; left: 0; position: absolute; top: 100%; width: 100%; }
.text-decoration-line-bottom-medium:before { content: ""; border-bottom: 2px solid; left: 0; position: absolute; top: 90%; width: 100%; }
.text-decoration-line-bottom-thick:before { content: ""; border-bottom: 3px solid; left: 0; position: absolute; top: 85%; width: 100%; }
.text-decoration-line-bottom-gradient {position: relative}
.text-decoration-line-bottom-gradient span {position: absolute; left: 0; bottom: 0; width: 100%; height: 1px}

/* dropcap */
.first-letter { float: left; font-size: 50px; line-height: normal; margin: 0 20px 0 0; text-align: center; padding: 10px 0; font-weight: 600; }
.first-letter-big { float: left; font-size: 100px; line-height: 100px; margin: 0 35px 0 0; padding:0 8px; text-align: center; font-weight: 600; position: relative; }
.first-letter-big:before { position: absolute; border-bottom: 1px solid; content: ""; display: block; width: 100%; top: 55%; left: 0; }
.first-letter-block { font-size: 30px; height: 55px; line-height: 25px; padding: 15px 0; width: 55px; font-weight: 500; margin-top: 5px; }
.first-letter-block-round { border-radius:6px; border:1px solid; }
.first-letter-round { border-radius: 50%; font-size:35px; padding: 15px; width: 65px; height: 65px; line-height: 35px; }

/* lineheight */
.line-height-0px { line-height: 0px; }
.line-height-8px { line-height: 8px; }
.line-height-10px { line-height: 10px; }
.line-height-12px { line-height: 12px; }
.line-height-14px { line-height: 14px; }
.line-height-15px { line-height: 15px; }
.line-height-16px { line-height: 16px; }
.line-height-18px { line-height: 18px; }
.line-height-20px { line-height: 20px; }
.line-height-22px { line-height: 22px; }
.line-height-24px { line-height: 24px; }
.line-height-26px { line-height: 26px; }
.line-height-28px { line-height: 28px; }
.line-height-30px { line-height: 30px; }
.line-height-32px { line-height: 32px; }
.line-height-34px { line-height: 34px; }
.line-height-36px { line-height: 36px; }
.line-height-38px { line-height: 38px; }
.line-height-40px { line-height: 40px; }
.line-height-42px { line-height: 42px; }
.line-height-44px { line-height: 44px; }
.line-height-46px { line-height: 46px; }
.line-height-50px { line-height: 50px; }
.line-height-55px { line-height: 55px; }
.line-height-60px { line-height: 60px; }
.line-height-65px { line-height: 65px; }
.line-height-70px { line-height: 70px; }
.line-height-75px { line-height: 75px; }
.line-height-80px { line-height: 80px; }
.line-height-85px { line-height: 85px; }
.line-height-90px { line-height: 90px; }
.line-height-95px { line-height: 95px; }
.line-height-100px { line-height: 100px; }
.line-height-120px { line-height: 120px; }
.line-height-140px { line-height: 140px; }
.line-height-160px { line-height: 160px; }
.line-height-170px { line-height: 170px; }

/* ===================================
    04. Background color
====================================== */

.bg-white { background-color:#FFF; }
.bg-black { background-color:#000; }
.bg-smoky-black { background-color:#101010; }
.bg-dark-gray { background-color:#757575; }
.bg-extra-dark-gray { background-color:#232323; }
.bg-extra-dark-gray-2 { background-color:#060606; }
.bg-extra-medium-gray { background-color:#939393; }
.bg-medium-gray { background-color:#e4e4e4; }
.bg-extra-light-gray { background-color:#cfcfcf; }
.bg-medium-light-gray { background-color:#ededed; }
.bg-very-light-gray { background-color:#fafafa; }
.bg-nero-gray { background-color:#141414; }
.bg-light-gray { background-color:#f7f7f7; }
.bg-bright-red { background-color: #98272a; }
.bg-fast-blue { background-color: #0038e3; }
.bg-neon-blue { background-color: #3452ff; }
.bg-transparent { background-color: transparent; }
.bg-sky-blue { background-color: #2e94eb; }
.bg-green { background-color: #27ae60; }
.bg-olivine-green { background-color: #8bb867; }
.bg-orange { background-color: #fd961e; }
.bg-dark-orange { background-color: #ff6437; }
.bg-neon-orange { background-color: #ff7a56; }
.bg-very-light-orange{ background-color: #fff9f6; }
.bg-dark-red { background-color: #e12837; }
.bg-red { background-color: #fe0127; }
.bg-dark-red { background-color: #d23134; }
.bg-light-pink { background-color: #f74961; }
.bg-very-light-pink{ background-color: #f7edee; }
.bg-parrot-green { background-color: #cee002; }
.bg-slate-blue { background-color: #262b35; }
.bg-medium-slate-blue { background-color: #374162; }
.bg-extra-medium-slate-blue { background-color: #23262d; }
.bg-dark-slate-blue { background-color: #1f232c; }
.bg-light-peach{ background-color: #ffd7c4; }
.bg-brown{ background-color: #986248; }
.bg-blue-wood{ background-color: #373a63; }
.bg-purple { background-color: #502e95; }
.bg-medium-purple { background-color: #3a285e; }
.bg-dark-purple { background-color: #333045; }
.bg-dark-purple-2 { background-color: #241526; }
.bg-yellow-ochre { background-color: #c89965; }
.bg-light-desert-storm { background-color: #f8f2eb; }
.bg-medium-desert-storm { background-color: #eee7de; }
.bg-yellow-ochre-light { background-color: #c3964e; }
.bg-yellow { background-color: #ffeb04; }
.bg-very-light-desert-storm { background-color: #faf6f3 ; }
.bg-light-blue { background-color: #F7F8FC; }
.bg-penguin-white { background-color: #f1edea; }
.bg-tussock { background-color: #bf8c4c; }
.bg-greenish-slate{ background-color: #445a58; }
.bg-seashell { background-color: #f9f6f3; }
.bg-shamrock-green { background-color: #2ed47c; }
.bg-light-orange { background-color: #ffded8;}
.bg-light-cream { background-color: #f8f4f0;}
.bg-golden { background-color: #ca943d;}
.bg-light-brownish-orange { background-color: #e6994e;}
.bg-greenish-gray { background-color: #969896;}
.bg-fast-yellow { background-color: #F4D956;}
.bg-light-greenish-gray { background-color: #f0f2f0;}
.bg-cod-gray { background-color: #131313;}
.bg-yellowish-green { background-color: #c7da26; }
.bg-copper-red { background-color: #cc754c; }
.bg-sunglow { background-color: #ffcc2e; }
.bg-orange-zest { background-color: #CA7741; }
.bg-dark-tussock { background-color: #BD884C; }
.bg-golden-brown { background-color: #cd9452; }
.bg-solitude { background-color: #f4f5f9; }
.bg-east-bay { background-color: #4c505f; }
.bg-quartz { background-color: #e1e2e9; }
.bg-iris-blue { background-color: #00B8B8; }
.bg-light-iris-blue { background-color: #F0F9F9; }
.bg-dark-iris-blue { background-color: #048C8C; }

.bg-transparent-black { background-color: rgba(0, 0, 0, 0.70); }
.bg-transparent-black-light { background-color: rgba(0, 0, 0, 0.35); }
.bg-transparent-black-very-light { background-color: rgba(0, 0, 0, 0.12); }
.bg-transparent-deep-green { background-color: rgba(115, 180, 103, 0.9); }
.bg-transparent-dark-gray { background-color: rgba(117,117,117,.5); }
.bg-transparent-mengo-yellow { background-color: rgba(254, 187, 26, 0.9); }
.bg-transparent-chocolate { background-color: rgba(201, 153, 101, 0.9); }
.bg-transparent-extra-dark-gray { background-color: rgba(28, 28, 28, 0.9); }
.bg-transparent-extra-dark-gray2 { background-color: rgba(30, 30, 30, 0.8); }
.bg-transparent-white { background-color: rgba(255, 255, 255, 0.9); }
.bg-transparent-white2 { background-color: rgba(255, 255, 255, 0.1); }
.bg-transparent-white3 { background-color: rgba(255, 255, 255, 0.18); }
.bg-transparent-dark-purple { background-color: rgba(86, 79, 220, 0.9); }
.bg-transparent-dark-purple-2 { background-color: rgba(36, 21, 38, 0.9); }
.bg-transparent-red { background-color: rgba(225, 40, 55, 0.9); }
.bg-transparent-light-red { background-color: rgba(254, 1, 39, .8); }
.bg-transparent-sky-blue { background-color: rgba(33, 136, 224, .8); }
.bg-transparent-fast-blue { background-color: rgba(0, 56, 227, .90); }
.bg-transparent-fast-blue2 { background-color: rgba(0, 56, 227, .80); }
.bg-transparent-yellow-ochre { background-color: rgba(200, 153, 101, .8); }
.bg-transparent-golden-brown { background-color: rgba(205, 148, 82, .8); }
.bg-transparent-iris-blue { background-color: rgba(0, 184, 184, .9); }

/* background gradient color */
.bg-gradient-magenta-orange { background-image: linear-gradient(to right top, #e42464, #ed3c5f, #f44f5c, #fa6259, #ff7357); }
.bg-gradient-magenta-orange-2 { background-image: linear-gradient(to right top, #ec606c, #ee6362, #ee6757, #ed6c4d, #eb7242); }
.bg-gradient-purple-magenta { background-image: linear-gradient(to right top, #502970, #7f2977, #ac2775, #d42d6b, #f34259); }
.bg-gradient-light-purple-light-orange { background-image: linear-gradient(to right top, #b783ff, #e37be0, #fa7cc1, #ff85a6, #ff9393); }
.bg-gradient-sky-blue-pink { background-image: linear-gradient(to right top, #556fff, #b263e4, #e05fc4, #f767a6, #ff798e); }
.bg-gradient-peacock-blue-crome-yellow { background-image: linear-gradient(to right top, #36c1e1, #28ccc0, #74cf8e, #bac962, #feb95b); }
.bg-gradient-fast-blue-purple { background-image: linear-gradient(to right top, #0039e3, #4132e0, #5e28dd, #741bd9, #8600d4); }
.bg-gradient-orange-pink { background-image: linear-gradient(to right top, #ff6557, #ff5a5f, #ff4e68, #ff4271, #ff367c); }
.bg-gradient-light-orange-light-pink {background-image: linear-gradient(to right top, #f5bb8d, #feb399, #ffaea8, #fdaab9, #f3aaca);}
.bg-gradient-white-light-gray { background-image: linear-gradient(to bottom, #ffffff, #fdfdfd, #fbfbfb, #f9f9f9, #f7f7f7); }
.bg-gradient-solitude-white { background-image: linear-gradient(to top, #f4f5f9, #f4f5f9, #fbfbfd, #ffffff, #ffffff); }
.bg-gradient-light-gray-white { background-image: linear-gradient(to bottom, #f7f7f7, #f9f9f9, #fbfbfb, #fdfdfd, #ffffff); }
.bg-gradient-tan-geraldine { background-image: linear-gradient(to right top, #fb7f87, #fc8a82, #fb957f, #fa9f7f, #f7aa80); }
.bg-gradient-white-light { background-image: linear-gradient(to right, #000000, rgba(0, 0, 0, 0.66), hsla(0, 0%, 0%, 0.4), rgba(0, 0, 0, 0.42), rgba(0, 0, 0, 0.1)) }
.bg-gradient-black-transparent { background: rgba(0,0,0,0); background: -moz-linear-gradient(top, rgba(0,0,0,0) 0%, rgba(0,0,0,1) 100%); background: -webkit-gradient(left top, left bottom, color-stop(0%, rgba(0,0,0,0)), color-stop(100%, rgba(0,0,0,1))); background: -webkit-linear-gradient(top, rgba(0,0,0,0) 0%, rgba(0,0,0,1) 100%); background: -o-linear-gradient(top, rgba(0,0,0,0) 0%, rgba(0,0,0,1) 100%); background: -ms-linear-gradient(top, rgba(0,0,0,0) 0%, rgba(0,0,0,1) 100%); background: linear-gradient(to bottom, rgba(0,0,0,0) 0%, rgba(0,0,0,1) 100%); filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#000000', GradientType=0 ); }
.bg-gradient-tan-geraldine-2 { background-image: linear-gradient(to right, #ff788d, #FF8286, #FE8681, #FE8D7D, #ff9578);  }
.bg-gradient-dark-slate-blue { background-image: linear-gradient(to right, #252c3e, rgba(37, 44, 62, 0.66), rgba(37, 44, 62, 0.42), rgba(37, 44, 62, 0.1), rgba(37, 44, 62, 0)) }
.bg-gradient-light-violet-light-orange { background-image: linear-gradient(to right top, #2f0959, #3b1059, #5c2359, #b85457, #fd7956); }
.bg-gradient-tussock-greenish-slate { background: linear-gradient(40deg, rgba(191,140,76,1) 0%, rgba(68,90,88,1) 100%); }
.bg-gradient-black-blue { background: linear-gradient(30deg, rgba(4,5,6,1) 0%, rgba(19,14,62,1) 100%); }
.bg-gradient-shamrock-green-light-orange { background-image: linear-gradient(20deg, rgba(46,212,124,1) 0%, rgba(232,191,71,1) 100%);}
.bg-gradient-dark-slate-blue-transparent { background: rgba(31,35,44,0); background: -moz-linear-gradient(top, rgba(0,0,0,0) 0%, rgba(31,35,44,1) 100%); background: -webkit-gradient(left top, left bottom, color-stop(0%, rgba(0,0,0,0)), color-stop(100%, rgba(31,35,44,1))); background: -webkit-linear-gradient(top, rgba(0,0,0,0) 0%, rgba(31,35,44,1) 100%); background: -o-linear-gradient(top, rgba(0,0,0,0) 0%, rgba(31,35,44,1) 100%); background: -ms-linear-gradient(top, rgba(0,0,0,0) 0%, rgba(31,35,44,1) 100%); background: linear-gradient(to bottom, rgba(0,0,0,0) 0%, rgba(31,35,44,1) 100%); filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#000000', GradientType=0 ); }
.bg-gradient-light-purple-light-red { background-image: linear-gradient(to right top, #bb85f9, #c887e5, #da8bca, #eb8fb0, #fb9398); }
.bg-gradient-white-light-greenish-gray { background-image: linear-gradient(to bottom, #ffffff, #f0f2f0); }
.bg-gradient-yellowish-green-turquoise { background-image: linear-gradient(to right top, #d0e51b, #a5e850, #7ce878, #58e49a, #3edfb6); }
.bg-gradient-extra-dark-gray-transparent { background: rgba(35,35,35,0); background: -moz-linear-gradient(top, rgba(0,0,0,0) 30%, rgba(35,35,35,1) 100%); background: -webkit-gradient(left top, left bottom, color-stop(30%, rgba(0,0,0,0)), color-stop(100%, rgba(35,35,35,1))); background: -webkit-linear-gradient(top, rgba(0,0,0,0) 30%, rgba(35,35,35,1) 100%); background: -o-linear-gradient(top, rgba(0,0,0,0) 30%, rgba(35,35,35,1) 100%); background: -ms-linear-gradient(top, rgba(0,0,0,0) 30%, rgba(35,35,35,1) 100%); background: linear-gradient(to bottom, rgba(0,0,0,0) 30%, rgba(35,35,35,1) 100%); filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#232323', GradientType=0 ); }
.bg-gradient-peacock-blue-crome-yellow-2 { background-image: linear-gradient(to right top, #5dcaa3, #84c685, #a8bf6d, #cab561, #e7a965); }
.bg-gradient-midium-dark-gray-transparent { background: rgba(20,20,20,0); background: -moz-linear-gradient(top, rgba(0,0,0,0) 0%, rgba(20,20,20,1) 100%); background: -webkit-gradient(left top, left bottom, color-stop(0%, rgba(0,0,0,0)), color-stop(100%, rgba(20,20,20,1))); background: -webkit-linear-gradient(top, rgba(0,0,0,0) 0%, rgba(20,20,20,1) 100%); background: -o-linear-gradient(top, rgba(0,0,0,0) 0%, rgba(20,20,20,1) 100%); background: -ms-linear-gradient(top, rgba(0,0,0,0) 0%, rgba(20,20,20,1) 100%); background: linear-gradient(to bottom, rgba(0,0,0,0) 0%, rgba(20,20,20,1) 100%); filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#141414', GradientType=0 ); }
.bg-gradient-midium-gray-transparent { background: rgba(20,20,20,0); background: -moz-linear-gradient(top, rgba(0,0,0,0) 0%, rgba(20,20,20,1) 150%); background: -webkit-gradient(left top, left bottom, color-stop(0%, rgba(0,0,0,0)), color-stop(150%, rgba(20,20,20,1))); background: -webkit-linear-gradient(top, rgba(0,0,0,0) 0%, rgba(20,20,20,1) 150%); background: -o-linear-gradient(top, rgba(0,0,0,0) 0%, rgba(20,20,20,1) 150%); background: -ms-linear-gradient(top, rgba(0,0,0,0) 0%, rgba(20,20,20,1) 150%); background: linear-gradient(to bottom, rgba(0,0,0,0) 0%, rgba(20,20,20,1) 150%); filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#141414', GradientType=0 ); }
.bg-gradient-extra-midium-gray-transparent { background: rgba(20,20,20,0); background: -moz-linear-gradient(top, rgba(0,0,0,0) 50%, rgba(20,20,20,.6) 150%); background: -webkit-gradient(left top, left bottom, color-stop(50%, rgba(0,0,0,0)), color-stop(150%, rgba(20,20,20,.6))); background: -webkit-linear-gradient(top, rgba(0,0,0,0) 50%, rgba(20,20,20,.6) 150%); background: -o-linear-gradient(top, rgba(0,0,0,0) 50%, rgba(20,20,20,.6) 150%); background: -ms-linear-gradient(top, rgba(0,0,0,0) 50%, rgba(20,20,20,.6) 150%); background: linear-gradient(to bottom, rgba(0,0,0,0) 50%, rgba(20,20,20,.6) 150%); filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#141414', GradientType=0 ); }
.bg-gradient-red-violet-purple { background-image: linear-gradient(to right top, #b93c90, #a92a96, #951a9d, #7b10a5, #5412ae); }
.bg-gradient-turquoise-green-yellowish { background-image: linear-gradient(to right top, #3edfb6, #58e49a, #7ce878, #a5e850, #d0e51b); }

/* background transparent gradient color */
.bg-transparent-gradient-magenta-orange { background-image: linear-gradient(to right top, rgba(228, 36, 100, .8), rgba(237, 60, 95, .8), rgba(244, 79, 92, .8), rgba(250, 98, 89, .8), rgba(255, 115, 87, .8)); }
.bg-transparent-gradient-purple-magenta { background-image: linear-gradient(to right top, rgba(80, 41, 112, .8), rgba(127, 41, 119, .8), rgba(172, 39, 117, .8), rgba(212, 45, 107, .8), rgba(243, 66, 89, .8)); }
.bg-transparent-gradient-light-purple-light-orange { background-image: linear-gradient(to right top, rgba(183, 131, 255, .95), rgba(227, 123, 224, .95), rgba(250, 124, 193, .95), rgba(255, 133, 166, .95), rgba(255, 147, 147, .95)); }
.bg-transparent-gradient-sky-blue-pink { background-image: linear-gradient(to right top, rgba(85, 111, 255, .8), rgba(178, 99, 228, .8), rgba(224, 95, 196, .8), rgba(247, 103, 166, .8), rgba(255, 121, 142, .8)); }
.bg-transparent-gradient-peacock-blue-crome-yellow { background-image: linear-gradient(to right top, rgba(54, 193, 225, .8), rgba(40, 204, 192, .8), rgba(116, 207, 142, .8), rgba(186, 201, 98, .8), rgba(254, 185, 91, .8)); }
.bg-transparent-gradient-fast-blue-purple { background-image: linear-gradient(to right top, rgba(0, 57, 227, .8), rgba(65, 50, 224, .8), rgba(94, 40, 221, .8), rgba(116, 27, 217, .8), rgba(134, 0, 212, .8)); }
.bg-transparent-gradient-light-red-orange { background-image: linear-gradient(to right top, rgba(248,78,48,0.8), rgba(251,111,86,0)); }
.bg-transparent-gradiant-black { background: rgb(35,35,35); background: linear-gradient(0deg, rgba(35,35,35,1) 0%, rgba(35,35,35,0) 89%); }
.bg-transparent-gradiant-white-black { background: -moz-linear-gradient(top, rgba(255,255,255,0) 0%, rgba(0,0,0,0.18) 18%, rgba(0,0,0,.95) 100%); background: -webkit-linear-gradient(top, rgba(255,255,255,0) 0%,rgba(0,0,0,0.18) 18%,rgba(0,0,0,.95) 100%); background: linear-gradient(to bottom, rgba(255,255,255,0) 0%,rgba(0,0,0,0.18) 18%,rgba(0,0,0,.95) 100%); filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00ffffff', endColorstr='#000000',GradientType=0 ); }
.bg-transparent-gradient-light-violet-light-orange { background-image: linear-gradient(to right top, rgba(43, 8, 89, .90), rgba(74, 24, 86, .90), rgba(127, 53, 88, .90), rgba(200, 92, 87, .90), rgba(255, 122, 86, .90)); }
.bg-transparent-gradient-tussock-greenish-slate { background: linear-gradient(40deg, rgba(191,140,76,.90) 0%, rgba(68,90,88,1) 90%); }
.bg-transparent-gradient-shamrock-green-light-orange { background: linear-gradient(45deg, rgba(46,212,124,0.9) 0%, rgba(232,166,38,0.9) 100%);}
.bg-transparent-gradient-smoky-black {background: rgb(16,16,16); background: -moz-radial-gradient(circle, rgba(16,16,16,0) 0%, rgba(16,16,16,1) 100%); background: -webkit-radial-gradient(circle, rgba(16,16,16,0) 0%, rgba(16,16,16,1) 100%); background: radial-gradient(circle, rgba(16,16,16,0) 0%, rgba(16,16,16,1) 100%); filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#101010",endColorstr="#101010",GradientType=1);}

/* hover background */
.feature-box-bg-white-hover:hover  { background-color: #fff; border-color: #fff !important}

/* ===================================
    05. Custom
====================================== */

section { padding: 130px 0; overflow: hidden; }
section.big-section { padding:160px 0; }
section.extra-big-section { padding:200px 0; }
section.half-section { padding:80px 0; }
.cursor-none { cursor: none; }
.cursor-default { cursor: default; }

/* box layout */
.box-layout { padding:0 90px; }
.box-layout-large { padding:0 150px; }

/* overlap */
.overlap-section { position: relative; }
.overlap-section-bottom { position: relative; }
.overlayer-box { position: absolute; height: 100%; width: 100%; top: 0; left: 0; transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out; }
.overlap-image { margin-bottom: -215px; z-index: 1;}
.text-overlap { margin-bottom: -60px; position: relative; z-index: -1; }
.text-overlap-style-01 { position: absolute; right: 0; bottom: -110px; font-size: 300px; line-height: 300px; letter-spacing: -10px; white-space: nowrap; }
.text-overlap-style-02 { position: absolute; left: -25px; top: -60px; font-size: 154px; line-height: 154px; letter-spacing: -8px; white-space: nowrap; }
.text-overlap-style-03 { position: absolute; left: -115px; top: 0; font-size: 255px; line-height: 255px; height: 100%; white-space: nowrap; text-align: center; writing-mode: vertical-rl; -ms-writing-mode: tb-rl; width: auto; -webkit-transform: rotate(180deg); -ms-transform: rotate(180deg); transform: rotate(180deg); }
.text-overlap-style-04 { position: absolute; top: 0; right: -20px; font-size: 80px; line-height: 80px; height: 100%; white-space: nowrap; text-align: center; writing-mode: vertical-rl; -ms-writing-mode: tb-rl; width: auto; -webkit-transform: rotate(180deg); -ms-transform: rotate(180deg); transform: rotate(180deg); }

/* revolution slider bullet */
.hermes .tp-bullet { height: 10px; width: 10px; }
.uranus.tparrows:before { font-size: 26px; }

/* svg drawing path */
.drawing-animation path, .drawing-animation circle, drawing-animation rect, .drawing-animation polygon, .drawing-animation ellipse { fill: none; stroke-width: 20px; stroke-dasharray: 20000; stroke-dashoffset: 20000; -webkit-animation: drawing-animation 6s linear forwards; animation: drawing-animation 6s linear forwards; -webkit-animation-delay: 1.5s; -moz-animation-delay: 1.5s; -o-animation-delay: 1.5s; animation-delay: 1.5s; }

/* google map filters */
.filter-grayscale-50 { -ms-filter: grayscale(50%); filter: grayscale(50%); }
.filter-grayscale-100 { -ms-filter: grayscale(100%); filter: grayscale(100%); }
.filter-invert-50 { -ms-filter: invert(50%); filter: invert(50%); }
.filter-invert-100 { -ms-filter: invert(100%); filter: invert(100%); }
.filter-mix-100 { -ms-filter: grayscale(100%) invert(100%); filter: grayscale(100%) invert(100%); }
.map iframe { height: 619px; vertical-align: bottom; width: 100%; }

/* blockquote */
blockquote { border-left: 2px solid; color: #232323; font-size: 18px; line-height: 30px; font-weight: 500; padding: 2px 40px; margin-top: 50px; margin-bottom: 50px; }
blockquote p { margin-bottom: 17px !important; }
blockquote footer { color: #828282; }

/* blog blockquote */
.blog-image blockquote h6::before { content: "\e67f"; font-family: 'themify'; font-size: 40px; top: 5px; position: relative; margin-right: 12px; color: #2e94eb; }
.blog-image blockquote { padding: 60px; border: 0; margin: 0; }

/* input box border */
.input-border-bottom, .input-border-bottom:focus { border: 0; border-bottom: 1px solid; padding-left: 0; padding-right: 0; }

/* input size */
.small-input, .small-textarea { padding: 13px 15px; font-size: 14px; }
.small-select select { padding: 13px 15px; }
.medium-input, .medium-textarea, .medium-select select { padding: 15px 20px; font-size: 14px;}
.extra-medium-textarea { font-size: 14px; }
.large-input { padding: 18px 25px 19px; }
.extra-large-input { padding: 20px 36px 23px; font-size: 16px; }

/* newsletter size */
.newsletter {position: relative;}
.newsletter input {padding-left: 0; padding-right: 0;}
.newsletter input + a.btn { position: absolute; right: 0; top: 0; margin: 0; padding-right: 0;}

.newsletter input.small-input + a.btn {top: 0px}
.newsletter input.medium-input + a.btn {top: 3px}
.newsletter input.large-input + a.btn {top: 8px}

/* newsletter popup */
.newsletter-popup form { position: relative; }
.newsletter-popup form .btn-newsletter { border: 1px solid #dfdfdf; background: transparent;}

/* tag */
.tag-cloud a { display: inline-block; text-align: center; font-size: 13px; line-height: 20px; margin: 0 6px 10px 0; padding: 5px 18px 6px; border-radius: 4px; border: 1px solid #e4e4e4}
.tag-cloud a:hover, .tag-cloud a.active { box-shadow: 0 0 10px rgba(23,23,23,.1); color: #828282 }

/* separator */
.separator-line-vertical { transform: rotate(-90deg); -webkit-transform: rotate(-90deg); -moz-transform: rotate(-90deg); -ms-transform: rotate(-90deg); -o-transform: rotate(-90deg); }
.left-line { position: relative; }
.left-line:after { border-bottom: 3px solid; border-color: #1dcf79; content: ""; left: -60px; position: absolute; top: 48%; width: 40px; }
.left-line-double { position: relative; }
.left-line-double:after { border-bottom: 3px solid; border-color: #1dcf79; content: ""; left: -60px; position: absolute; top: 18px; width: 40px; }
.text-side-line { position: relative; display: inline-block; }
.text-side-line:after { content: ""; height: 1px; left: -65px; position: absolute; top: 50%; width: 45px; background: #00e092; }
.text-side-line:before { content: ""; height: 1px; right: -65px; position: absolute; top: 50%; width: 45px; background: #00e092; }
.divider-full .divider-border:before, .divider-full .divider-border:after { flex: 1 1 auto; -webkit-box-flex: 1 1 auto; -ms-flex: 1 1 auto; content: ""; border-top: 1px solid #e4e4e4; }
.divider-full .divider-border.divider-light:before, .divider-full .divider-border.divider-light:after { border-color: rgba(255,255,255,.1); }

/* overlay background */
.overlay-bg {position: absolute; height: 100%; width: 100%; opacity: 1; top:0; left: 0;}

/* opacity */
.opacity-very-light {position: absolute; height: 100%; width: 100%; opacity: 0.2; top:0; left: 0;}
.opacity-light {position: absolute; height: 100%; width: 100%; opacity: 0.3; top:0; left: 0;}
.opacity-extra-medium {position: absolute; height: 100%; width: 100%; opacity: 0.5; top:0; left: 0;}
.opacity-extra-medium-2 {position: absolute; height: 100%; width: 100%; opacity: 0.6; top:0; left: 0;}
.opacity-medium {position: absolute; height: 100%; width: 100%; opacity: 0.75; top:0; left: 0;}
.opacity-medium-2 {position: absolute; height: 100%; width: 100%; opacity: 0.70; top:0; left: 0;}
.opacity-full {position: absolute; height: 100%; width: 100%; opacity: 0.8; top:0; left: 0;}
.opacity-full-dark {position: absolute; height: 100%; width: 100%; opacity: 0.9; top:0; left: 0;}

.opacity-0 { opacity:0; }
.opacity-0-half { opacity:0.05; }
.opacity-1 { opacity:0.1; }
.opacity-1-half { opacity:0.15; }
.opacity-2 { opacity:0.2; }
.opacity-2-half { opacity:0.25; }
.opacity-3 { opacity:0.3; }
.opacity-3-half { opacity:0.35; }
.opacity-4 { opacity:0.4; }
.opacity-4-half { opacity:0.45; }
.opacity-5 { opacity:0.5; }
.opacity-5-half { opacity:0.55; }
.opacity-6 { opacity:0.6; }
.opacity-6-half { opacity:0.65; }
.opacity-7 { opacity:0.7; }
.opacity-7-half { opacity:0.75; }
.opacity-8 { opacity:0.8; }
.opacity-8-half { opacity:0.85; }
.opacity-9 { opacity:0.9; }
.opacity-9-half { opacity:0.95; }

/* box shadow */
.btn-shadow { box-shadow: 0 0 6px rgba(0,0,0,.3); }
.box-shadow, .box-shadow-hover:hover { box-shadow: 0 0 10px rgba(0,0,0,.08); transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out; }
.box-shadow-double-large, .box-shadow-double-large-hover:hover { box-shadow: 0 0 35px rgba(40,42,54,.18); transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out; }
.box-shadow-large, .box-shadow-large-hover:hover { box-shadow: 0 0 30px rgba(0,0,0,.08); transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out; }
.box-shadow-extra-large, .box-shadow-extra-large-hover:hover { box-shadow: 0 0 25px rgba(0,0,0,.08); transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out; }
.box-shadow-medium, .box-shadow-medium-hover:hover { box-shadow: 0 0 20px rgba(0,0,0,.08); transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out; }
.box-shadow-small, .box-shadow-small-hover:hover { box-shadow: 0 0 15px rgba(0,0,0,.08); transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out; }
.box-shadow-very-light-hover:hover, .box-shadow-light-hover:hover, .box-shadow-dark-hover:hover, .box-shadow-large-hover:hover, .box-shadow-extra-large-hover:hover, .box-shadow-medium-hover:hover, .box-shadow-small-hover:hover, .box-shadow-double-large-hover:hover{-moz-transform: translate3d(0, -3px, 0); -o-transform: translate3d(0, -3px, 0); -ms-transform: translate3d(0, -3px, 0); -webkit-transform: translate3d(0, -3px, 0); transform: translate3d(0, -3px, 0);}

/* text shadow */
.text-shadow-large { text-shadow: 0 0 50px rgba(0, 0, 0, 0.9);}
.text-shadow-extra-large { text-shadow: 0 0 60px rgba(0, 0, 0, 0.9);}
.text-shadow-double-large { text-shadow: 0 0 100px rgba(0, 0, 0, .9);}

/* drop shadow */
.drop-shadow svg { filter: drop-shadow( 0px 1px 4px rgba(0, 0, 0, .1)); }

/* z-index */
.z-index-1 { z-index:1; }
.z-index-2 { z-index:2; }
.z-index-3 { z-index:3; }
.z-index-4 { z-index:4; }
.z-index-5 { z-index:5; }
.z-index-6 { z-index:6; }
.z-index-7 { z-index:7; }
.z-index-8 { z-index:8; }
.z-index-9 { z-index:9; }
.z-index-0 { z-index:0; }
.z-index-minus-1 { z-index:-1; }
.z-index-minus-2 { z-index:-2; }
.z-index-minus-3 { z-index:-3; }
.z-index-minus-4 { z-index:-4; }
.z-index-minus-5 { z-index:-5; }

/* absolute middle center */
.absolute-middle-center { left: 50%; top: 50%; position: absolute; -ms-transform: translateX(-50%) translateY(-50%); -moz-transform: translateX(-50%) translateY(-50%); -webkit-transform: translateX(-50%) translateY(-50%); transform: translateX(-50%) translateY(-50%); }
.absolute-middle-center.move-top-bottom, .absolute-middle-center.move-bottom-top { margin-left: -10px; margin-top: -10px; }

/* checkbox - radio button */
input[type="checkbox"] { width: auto; }
input[type="checkbox"] + label { color: #939393; font-weight: normal; margin-bottom: 0; }
input[type="checkbox"]:checked + label { color: inherit; }
input[type="checkbox"] + label span { display:inline-block; width:15px; height:15px; margin:0 10px 0 0; vertical-align:middle; cursor:pointer; border: 1px solid #d9d9d9; border-radius: 0; position: relative; }
input[type="checkbox"]:checked + label span:after { position: absolute; top: 0; left: 0; height: 100%; width: 100%; text-align: center; font-size: 12px; line-height: 15px; content: "\f00c"; font-family: "Font Awesome 5 Free"; font-weight: 900; }

input[type="radio"] { width: auto; }
input[type="radio"] + label { font-weight: normal; margin-bottom: 0; }
input[type="radio"] + label span { display:inline-block; width:15px; height:15px; margin:0 10px 0 0; vertical-align:middle; cursor:pointer; border-radius: 0; position: relative; border-radius: 100%; }
input[type="radio"]:checked + label span:after { position: absolute; top: 0; left: 0; color: #fff; height: 100%; width: 100%; text-align: center; font-size: 9px; line-height: 15px; content: "\f00c"; font-family: "Font Awesome 5 Free"; font-weight: 900; }

/* ===================================
    06. Button
====================================== */

.btn { display:inline-block; border:2px solid transparent; border-radius: 0; text-transform: uppercase; width: auto; font-family: 'Poppins', sans-serif; font-weight: 500; }
.btn.active.focus, .btn.active:focus, .btn.focus, .btn.focus:active, .btn:active:focus, .btn:focus { outline: medium none; outline-offset: 0; }
input.btn:focus { border-width: 2px; }
.btn.active, .btn:active, .btn:focus { box-shadow: none; }
.btn i { vertical-align: middle; position: relative; top: -1px }

/* button size */
.btn.btn-very-small { font-size:10px; padding: 6px 16px; }
.btn.btn-small { font-size:11px; padding: 8px 22px;}
.btn.btn-medium { font-size:12px; padding: 10px 26px;}
.btn.btn-large { font-size:13px; padding: 12px 32px;}
.btn.btn-extra-large { font-size:14px; padding: 16px 40px;}
.btn-dual .btn { margin-left: 10px; margin-right: 10px; }
.btn-dual .btn:first-child {margin-left: 0}
.btn .right-icon, .btn-link .right-icon { margin-left: 8px; transition: none; -moz-transition: none; -webkit-transition: none; -ms-transition: none; -o-transition: none; }
.btn .left-icon, .btn-link .left-icon { margin-right: 8px; transition: none; -moz-transition: none; -webkit-transition: none; -ms-transition: none; -o-transition: none; }

/* rounded button */
.btn.btn-round-edge-small { border-radius: 2px; }
.btn.btn-rounded { border-radius: 50px; }
.btn.btn-rounded.btn-very-small { padding: 6px 20px; }
.btn.btn-rounded.btn-small { padding: 8px 26px }
.btn.btn-rounded.btn-medium { padding: 10px 32px; }
.btn.btn-rounded.btn-large { padding: 12px 38px; }
.btn.btn-rounded.btn-extra-large { padding: 16px 44px; }
.btn.btn-round-edge { border-radius: 4px; }
.btn-fixed-bottom { position: absolute; bottom: 30px; right: 30px; z-index: 1; height: 40px; width: 40px; line-height: 40px; text-align: center; }
.btn.btn-rounded.btn-extra-large.btn-expand-ltr { padding: 15px 28px 15px; }
.btn.btn-rounded.btn-extra-large.btn-expand-ltr span { width: 50px;}
.btn.btn-rounded.btn-large.btn-expand-ltr { padding: 14px 26px 14px; }
.btn.btn-rounded.btn-large.btn-expand-ltr span { width: 46px;}
.btn.btn-rounded.btn-medium.btn-expand-ltr { padding: 7px 21px 7px; }
.btn.btn-rounded.btn-medium.btn-expand-ltr span { width: 32px;}
.btn.btn-rounded.btn-small.btn-expand-ltr { padding: 7px 20px 7px; }
.btn.btn-rounded.btn-small.btn-expand-ltr span { width: 30px;}
.btn.btn-rounded.btn-very-small.btn-expand-ltr { padding: 5px 17px 5px }
.btn.btn-rounded.btn-very-small.btn-expand-ltr span { width: 25px;}

/* animation button */
.btn.btn-slide-up-bg, .btn.btn-slide-down-bg, .btn.btn-slide-left-bg, .btn.btn-slide-right-bg { position: relative; z-index: 1; overflow: hidden; }
.btn.btn-slide-up-bg span, .btn.btn-slide-down-bg span, .btn.btn-slide-left-bg span, .btn.btn-slide-right-bg span { position: absolute; height: 100%; width: 101%; z-index: -1; left: -1px; top: 0; }
.btn.btn-slide-up-bg span, .btn.btn-slide-down-bg span, .btn.btn-slide-left-bg span, .btn.btn-slide-right-bg span { position: absolute; height: 100%; width: 101%; z-index: -1; left: -1px; top: 0; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }

/* slide button */
.btn.btn-slide-up-bg span { -webkit-transform: translateY(100%); -moz-transform: translateY(100%); -ms-transform: translateY(100%); -o-transform: translateY(100%); transform: translateY(100%); }
.btn.btn-slide-down-bg span { -webkit-transform: translateY(-100%); -moz-transform: translateY(-100%); -ms-transform: translateY(-100%); -o-transform: translateY(-100%); transform: translateY(-100%); }
.btn.btn-slide-up-bg:hover span, .btn.btn-slide-down-bg:hover span { -webkit-transform: translateY(0); -moz-transform: translateY(0); -ms-transform: translateY(0); -o-transform: translateY(0); transform: translateY(0); }
.btn.btn-slide-left-bg span { -webkit-transform: translateX(100%); -moz-transform: translateX(100%); -ms-transform: translateX(100%); -o-transform: translateX(100%); transform: translateX(100%); }
.btn.btn-slide-right-bg span { -webkit-transform: translateX(-100%); -moz-transform: translateX(-100%); -ms-transform: translateX(-100%); -o-transform: translateX(-100%); transform: translateX(-100%); }
.btn.btn-slide-left-bg:hover span, .btn.btn-slide-right-bg:hover span { -webkit-transform: translateX(0); -moz-transform: translateX(0); -ms-transform: translateX(0); -o-transform: translateX(0); transform: translateX(0); }
.btn-slide-icon i {-webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s;}
.btn-slide-icon:hover i { -webkit-transform: translateX(5px); -moz-transform: translateX(5px); -ms-transform: translateX(5px); -o-transform: translateX(5px); transform: translateX(5px); -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }
.btn-slide-icon-left i {-webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s;}
.btn-slide-icon-left:hover i { -webkit-transform: translateX(-5px); -moz-transform: translateX(-5px); -ms-transform: translateX(-5px); -o-transform: translateX(-5px); transform: translateX(-5px); -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }

/* fancy button */
.btn.btn-fancy.btn-very-small { font-size:10px; letter-spacing: 0.5px; padding: 7px 18px; }
.btn.btn-fancy.btn-small { font-size:11px; letter-spacing: 0.5px; padding: 10px 24px;}
.btn.btn-fancy.btn-medium { font-size:12px; letter-spacing: 1px; padding: 12px 28px;}
.btn.btn-fancy.btn-large { font-size:13px; letter-spacing: 1px; padding: 13px 32px;}
.btn.btn-fancy.btn-extra-large { font-size:15px; letter-spacing: 1px; padding: 18px 38px; }

/* expanded button */
.btn.btn-expand-ltr { position: relative; z-index: 1; }
.btn.btn-expand-ltr.btn-rounded span { border-radius: 50px; }
.btn.btn-expand-ltr:hover span { width: 100% !important; }
.btn.btn-expand-ltr span { position: absolute; top: 0; left: 0; width: 50%; height: 100%; z-index: -1; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }
.btn.btn-expand-ltr i { display: inline-block; margin-left: 3px; opacity: 0; transform: translateX(-10px); -webkit-transform: translateX(-10px); -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.4s; -ms-transition-duration: 0.4s; -o-transition-duration: 0.4s; transition-duration: 0.4s; }
.btn.btn-expand-ltr:hover i { opacity: 1; transform: translateX(0); -webkit-transition-duration: 0.5s; -moz-transition-duration: 0.5s; -ms-transition-duration: 0.5s; -o-transition-duration: 0.5s; transition-duration: 0.5s; }

/* link button */
.btn.btn-link { padding: 7px 0 2px; position: relative; text-decoration: none; border: 0; border-bottom: 2px solid; background-color: transparent; }
.btn.btn-link:hover, .btn.btn-link-gradient:hover { opacity: .7; }
.btn.btn-link.thin { padding: 7px 0 2px; position: relative; text-decoration: none; border: 0; border-bottom: 1px solid; background-color: transparent; }
.btn.btn-link.thin:hover, .btn.btn-link-gradient:hover { opacity: .7; }
.btn.btn-link-gradient { padding: 7px 0 2px; position: relative; border: 0; background-color: transparent; }
.btn.btn-link-gradient > span { position: absolute; bottom: 0; left: 0; height: 2px; width: 100%; }

/* button box shadow */
.btn-box-shadow { box-shadow: 0 0 15px rgba(0,0,0,.1);}
.btn-box-shadow:hover {box-shadow: 0 8px 30px -5px rgba(0,0,0,.2); -moz-transform: translate3d(0, -3px, 0); -o-transform: translate3d(0, -3px, 0); -ms-transform: translate3d(0, -3px, 0); -webkit-transform: translate3d(0, -3px, 0); transform: translate3d(0, -3px, 0);}
.btn.btn-box-shadow.btn-white { background:#ffffff; border-color: #ffffff; color: #232323; }
.btn.btn-box-shadow.btn-white:hover, .btn.btn-white:focus { background: #ffffff; color: #232323; }
.btn.btn-box-shadow.btn-dark-gray { background:#232323; border-color: #232323; color: #fff; }
.btn.btn-box-shadow.btn-dark-gray:hover, .btn.btn-extra-dark-gray:focus { background: #232323; color: #fff; }
.btn.btn-box-shadow.btn-golden-brown { background:#CD9452; border-color: #CD9452; color: #fff; }
.btn.btn-box-shadow.btn-golden-brown:hover, .btn.btn-extra-dark-gray:focus { background: #CD9452; color: #fff; }

/* button box shadow flat */
.btn-box-shadow-flat { box-shadow: 2px 2px 0 rgba(0,0,0,1); border-width: 1px;}
.btn-box-shadow-flat:hover {box-shadow: 0 0 0 rgba(0,0,0,0); }
.btn.btn-box-shadow-flat.btn-transparent-dark-gray { background:transparent; border-color: #232323; color: #232323; }
.btn.btn-box-shadow-flat.btn-transparent-dark-gray:hover, .btn.btn-transparent-dark-gray:focus { background: #232323; color: #fff; border-color: transparent }

/* button flat background */
.btn.btn-white { background:#ffffff; border-color: #ffffff; color: #232323; }
.btn.btn-white:hover, .btn.btn-white:focus { background: transparent; color: #fff; }
.btn.btn-black { background:#000000; border-color: #000000; color: #fff; }
.btn.btn-black:hover, .btn.btn-black:focus { background: transparent; color: #000; }
.btn.btn-dark-gray { background: #232323; border-color: #232323; color: #fff; }
.btn.btn-dark-gray:hover, .btn.btn-dark-gray:focus { background: transparent; color: #232323; }
.btn.btn-light-gray { background: #dbdbdb; border-color: #dbdbdb; color: #232323; }
.btn.btn-light-gray:hover, .btn.btn-light-gray:focus { background: transparent; border-color: #dbdbdb; color: #dbdbdb; }
.btn.btn-fast-blue { background: #0038e3; border-color: #0038e3; color: #ffffff; }
.btn.btn-fast-blue:hover, .btn.btn-fast-blue:focus { background: transparent; color: #0038e3; }
.btn.btn-green { background: #27AE60; border-color: #27AE60; color: #ffffff; }
.btn.btn-green:hover, .btn.btn-green:focus { background: transparent; color: #27AE60; }
.btn.btn-olivine-green { background: #8bb867; border-color: #8bb867; color: #ffffff; }
.btn.btn-olivine-green:hover, .btn.btn-olivine-green:focus { background: transparent; color: #8bb867; }
.btn.btn-sky-blue { background: #2e94eb; border-color: #2e94eb; color: #ffffff; }
.btn.btn-sky-blue:hover, .btn.btn-sky-blue:focus { background: transparent; color: #2e94eb; }
.btn.btn-yellow-ochre { background: #c89965; border-color: #c89965; color: #ffffff; }
.btn.btn-yellow-ochre:hover, .btn.btn-yellow-ochre:focus { background: transparent; color: #c89965; }
.btn.btn-fast-yellow { background: #F4D956; border-color: #F4D956; color: #232323; }
.btn.btn-fast-yellow:hover, .btn.btn-fast-yellow:focus { background: transparent; color: #F4D956; }
.btn.btn-dark-purple { background: #333045; border-color: #333045; color: #ffffff; }
.btn.btn-dark-purple:hover, .btn.btn-dark-purple:focus { background: transparent; color: #333045; }
.btn.btn-slate-blue { background: #262b35; border-color: #262b35; color: #ffffff; }
.btn.btn-slate-blue:hover, .btn.btn-slate-blue:focus { background: transparent; color: #262b35; }
.btn.btn-medium-slate-blue { background: #374162; border-color: #374162; color: #ffffff; }
.btn.btn-medium-slate-blue:hover, .btn.btn-medium-slate-blue:focus { background: transparent; color: #374162; }
.btn.btn-neon-orange { background: #ff7a56; border-color: #ff7a56; color: #ffffff; }
.btn.btn-neon-orange:hover, .btn.btn-neon-orange:focus { background: transparent; color: #ff7a56; }
.btn.btn-salmon-rose { background: #ea9d8d; border-color: #ea9d8d; color: #232323; }
.btn.btn-salmon-rose:hover, .btn.btn-salmon-rose:focus { background: transparent; color: #ea9d8d; }
.btn.btn-tussock { background: #bf8c4c; border-color: #bf8c4c; color: #ffffff; }
.btn.btn-tussock:hover, .btn.btn-tussock:focus { background: transparent; color: #bf8c4c; }
.btn.btn-golden { background: #ca943d; border-color: #ca943d; color: #ffffff; }
.btn.btn-golden:hover, .btn.btn-golden:focus { background: transparent; color: #ca943d; }
.btn.btn-golden-brown { background: #CD9452; border-color: #CD9452; color: #ffffff; }
.btn.btn-golden-brown:hover, .btn.btn-golden-brown:focus { background: transparent; color: #CD9452; }
.btn.btn-iris-blue { background: #00B8B8; border-color: #00B8B8; color: #ffffff; }
.btn.btn-iris-blue:hover, .btn.btn-iris-blue:focus { background: transparent; color: #00B8B8; }

/* button only border */
.btn.btn-transparent-white { background: transparent; border-color: #ffffff; color: #ffffff; }
.btn.btn-transparent-white:hover, .btn.btn-transparent-white:focus { background: #ffffff; border-color: #ffffff; color: #232323; }
.btn.btn-transparent-black { background: transparent; border-color: #000000; color: #000000; }
.btn.btn-transparent-black:hover, .btn.btn-transparent-black:focus { background: #000000; border-color: #000000; color: #ffffff; }
.btn.btn-transparent-dark-gray { background: transparent; border-color: #232323; color: #232323; }
.btn.btn-transparent-dark-gray:hover, .btn.btn-transparent-dark-gray:focus { background: #232323; border-color: #232323; color: #ffffff; }
.btn.btn-transparent-light-gray { background: transparent; border-color: #dbdbdb; color: #232323; }
.btn.btn-transparent-light-gray:hover, .btn.btn-transparent-light-gray:focus { background: #232323; border-color: #232323; color: #ffffff; }
.btn.btn-transparent-medium-light-gray { background: transparent; border-color: #757575; color: #757575; }
.btn.btn-transparent-medium-light-gray:hover, .btn.btn-transparent-medium-light-gray:focus { background: #dbdbdb; border-color: #dbdbdb; color: #232323; }
.btn.btn-transparent-fast-blue { background: transparent; border-color: #0038e3; color: #0038e3; }
.btn.btn-transparent-fast-blue:hover, .btn.btn-transparent-fast-blue:focus { background: #0038e3; border-color: #0038e3; color: #fff; }
.btn.btn-transparent-green { background: transparent; border-color: #1dcf79; color: #1dcf79; }
.btn.btn-transparent-green:hover, .btn.btn-transparent-green:focus { background: #1dcf79; border-color: #1dcf79; color: #fff; }
.btn.btn-transparent-slate-blue { background: transparent; border-color: #262b35; color: #262b35; }
.btn.btn-transparent-slate-blue:hover, .btn.btn-transparent-slate-blue:focus { background: #262b35; border-color: #262b35; color: #fff; }
.btn.btn-transparent-medium-slate-blue { background: transparent; border-color: #374162; color: #374162; }
.btn.btn-transparent-medium-slate-blue:hover, .btn.btn-transparent-medium-slate-blue:focus { background: #374162; border-color: #374162; color: #fff; }
.btn.btn-transparent-tussock { background: transparent; border-color: #bf8c4c; color: #bf8c4c; }
.btn.btn-transparent-tussock:hover, .btn.btn-transparent-tussock:focus { background: #bf8c4c; border-color: #bf8c4c; color: #fff; }
.btn.btn-transparent-golden-brown { background: transparent; border-color: #CD9452; color: #CD9452; }
.btn.btn-transparent-golden-brown:hover, .btn.btn-transparent-golden-brown:focus { background: #CD9452; border-color: #CD9452; color: #fff; }

/* button gradient */
.btn-gradient-magenta-orange { background-image: linear-gradient(to right, #e42564, #fa6259, #e42564); color: #fff; background-size: 200% auto; transition: all 0.3s cubic-bezier(.25,.8,.25,1); }
.btn-gradient-magenta-orange-2 { background-image: linear-gradient(to right, #ec606c, #eb7242, #ec606c); color: #fff; background-size: 200% auto; transition: all 0.3s cubic-bezier(.25,.8,.25,1); }
.btn-gradient-purple-magenta { background: linear-gradient(to right, #502970, #f34259, #502970); color: #fff; background-size: 200% auto; transition: all 0.3s cubic-bezier(.25,.8,.25,1); }
.btn-gradient-light-purple-light-orange { background-image: linear-gradient(to right, #b884fd, #fe73a8, #b884fd); color: #fff; background-size: 200% auto; transition: all 0.3s cubic-bezier(.25,.8,.25,1); }
.btn-gradient-sky-blue-pink { background: linear-gradient(to right, #556fff, #ff798e, #556fff); color: #fff; background-size: 200% auto; transition: all 0.3s cubic-bezier(.25,.8,.25,1); }
.btn-gradient-peacock-blue-crome-yellow { background: linear-gradient(to right, #36c1e1, #feb95b, #36c1e1); color: #fff; background-size: 200% auto; transition: all 0.3s cubic-bezier(.25,.8,.25,1); }
.btn-gradient-fast-blue-purple { background: linear-gradient(to right, #0039e3, #8600d4, #0039e3); color: #fff; background-size: 200% auto; transition: all 0.3s cubic-bezier(.25,.8,.25,1); }
.btn-gradient-tan-geraldine { background-image: linear-gradient(to right, #fe8182,  #f8a380, #fe8182); color: #fff; background-size: 200% auto; transition: all 0.3s cubic-bezier(.25,.8,.25,1); }
.btn-gradient-tan-geraldine-2 { background-image: linear-gradient(to right, #ff798c, #ff7e89, #ff8584, #ff8d7e, #ff927a); color: #fff; background-size: 200% auto; transition: all 0.3s cubic-bezier(.25,.8,.25,1); }
.btn-gradient-shamrock-green-light-orange { background-image: linear-gradient(to right, #2ed47c, #e8a626, #2ed47c); color: #fff; background-size: 200% auto; transition: all 0.3s cubic-bezier(.25,.8,.25,1);}
.btn-gradient-light-purple-light-red { background-image: linear-gradient(to right, #b884fd, #fd9395, #b884fd); color: #fff; background-size: 200% auto; transition: all 0.3s cubic-bezier(.25,.8,.25,1); }
.btn-gradient-yellowish-green-turquoise { background-image: linear-gradient(to right, #d0e51b, #3edfb6, #d0e51b); color: #232323; background-size: 200% auto; transition: all 0.3s cubic-bezier(.25,.8,.25,1); }
.btn-gradient-peacock-blue-crome-yellow-2 { background-image: linear-gradient(to right, #5cc088, #e7a965, #5cc088); color: #ffffff; background-size: 200% auto; transition: all 0.3s cubic-bezier(.25,.8,.25,1); }
.btn-gradient-shamrock-green-light-orange:hover, .btn-gradient-shamrock-green-light-orange:focus, .btn-gradient-tan-geraldine-2:hover, .btn-gradient-tan-geraldine-2:focus, .btn-gradient-magenta-orange:hover, .btn-gradient-magenta-orange:focus, .btn-gradient-magenta-orange-2:hover, .btn-gradient-magenta-orange-2:focus, .btn-gradient-purple-magenta:hover, .btn-gradient-purple-magenta:focus, .btn-gradient-light-purple-light-orange:hover, .btn-gradient-light-purple-light-orange:focus, .btn-gradient-sky-blue-pink:hover, .btn-gradient-sky-blue-pink:focus, .btn-gradient-peacock-blue-crome-yellow:hover, .btn-gradient-peacock-blue-crome-yellow:focus, .btn-gradient-fast-blue-purple:hover, .btn-gradient-fast-blue-purple:focus, .btn-gradient-tan-geraldine:hover, .btn-gradient-tan-geraldine:focus, .btn-gradient-light-purple-light-red:hover, .btn-gradient-light-purple-light-red:focus, .btn-gradient-yellowish-green-turquoise:hover, .btn-gradient-yellowish-green-turquoise:focus, .btn-gradient-peacock-blue-crome-yellow-2:hover, .btn-gradient-peacock-blue-crome-yellow-2:focus { background-position: right center; color: #fff; }

/* button gradient border */
.btn.btn-transparent-gradient-sky-blue-pink { background: transparent; color: #232323; border-image-slice: 1; border-image-source: linear-gradient(45deg, rgb(85, 111, 255), rgb(255, 121, 142));}
.btn.btn-transparent-gradient-sky-blue-pink:hover, .btn.btn-transparent-gradient-sky-blue-pink:focus { background: transparent; color: #232323; }

/* ===================================
    07. Border
====================================== */

.border-all { border: 1px solid !important; }
.border-top { border-top: 1px solid !important; }
.border-bottom { border-bottom: 1px solid !important; }
.border-left { border-left: 1px solid !important; }
.border-right { border-right: 1px solid !important; }
.border-lr { border-left: 1px solid; border-right: 1px solid !important; }
.border-tb { border-top: 1px solid; border-bottom: 1px solid !important; }

/* border width */
.border-width-1px { border-width:1px !important; }
.border-width-2px { border-width:2px !important; }
.border-width-3px { border-width:3px !important; }
.border-width-4px { border-width:4px !important; }
.border-width-5px { border-width:5px !important; }
.border-width-6px { border-width:6px !important; }
.border-width-7px { border-width:7px !important; }
.border-width-8px { border-width:8px !important; }
.border-width-9px { border-width:9px !important; }
.border-width-10px { border-width:10px !important; }
.border-width-11px { border-width:11px !important; }
.border-width-12px { border-width:12px !important; }
.border-width-13px { border-width:13px !important; }
.border-width-14px { border-width:14px !important; }
.border-width-15px { border-width:15px !important; }
.border-width-16px { border-width:16px !important; }
.border-width-17px { border-width:17px !important; }
.border-width-18px { border-width:18px !important; }
.border-width-19px { border-width:19px !important; }
.border-width-20px { border-width:20px !important; }

/* border color */
.border-color-white { border-color: #fff !important; }
.border-color-black { border-color: #000 !important; }
.border-color-sky-blue { border-color: #2e94eb !important; }
.border-color-extra-dark-gray { border-color: #232323 !important; }
.border-color-medium-dark-gray { border-color: #363636 !important; }
.border-color-dark-gray { border-color: #939393 !important; }
.border-color-extra-medium-gray { border-color: #dbdbdb !important; }
.border-color-medium-gray { border-color: #e4e4e4 !important; }
.border-color-medium-light-gray { border-color: #757575 !important; }
.border-color-extra-light-gray { border-color: #ededed !important; }
.border-color-light-gray { border-color: #f5f5f5 !important; }
.border-color-light-pink { border-color: #ff85a6 !important; }
.border-color-fast-blue { border-color: #0038e3 !important; }
.border-color-light-peach { border-color: #ffd7c4 !important; }
.border-color-neon-orange { border-color: #ff7a56 !important; }
.border-color-green { border-color: #27ae60 !important; }
.border-color-olivine-green { border-color: #8bb867 !important; }
.border-color-shamrock-green { border-color: #2ed47c !important; }
.border-color-light-brownish-orange {border-color: #e6994e !important;}
.border-color-golden-brown {border-color: #CD9452 !important;}

/* transparent border */
.border-color-transparent { border-color: transparent !important; }
.border-color-black-transparent { border-color: rgba(0,0,0,.1) !important; }
.border-color-white-transparent { border-color: rgba(255,255,255,.1) !important; }
.border-color-golden-transparent { border-color: rgba(202, 148, 61, 0.4) !important; }
.border-color-pink-transparent { border-color: rgba(255, 33, 79, 0.45) !important; }
.border-color-dark-white-transparent { border-color: rgba(255,255,255,0.2) !important; }
.border-color-medium-white-transparent { border-color: rgba(255,255,255,0.4) !important; }
.border-color-full-dark-white-transparent { border-color: rgba(255,255,255,0.05) !important; }
.border-color-light-white-transparent { border-color: rgba(255,255,255,0.1) !important; }
.border-color-nero-transparent { border-color: rgba(25,25,25,0.1) !important; }
.border-color-extra-medium-gray-transparent { border-color: rgba(219,219,219,.04) !important; }
.border-color-copper-red-transparent { border-color: rgba(204,117,76,.3) !important; }

/* border gradient */
.border-gradient-magenta-orange { border-image: linear-gradient(to right top, #e42464, #ed3c5f, #f44f5c, #fa6259, #ff7357); border-image-slice: 1;  }
.border-gradient-purple-magenta { border-image: linear-gradient(to right top, #502970, #7f2977, #ac2775, #d42d6b, #f34259); border-image-slice: 1; }
.border-gradient-light-purple-light-orange { border-image: linear-gradient(to right top, #b783ff, #e37be0, #fa7cc1, #ff85a6, #ff9393); border-image-slice: 1; }
.border-gradient-sky-blue-pink { border-image: linear-gradient(to right top, #556fff, #b263e4, #e05fc4, #f767a6, #ff798e); border-image-slice: 1; }
.border-gradient-peacock-blue-crome-yellow { border-image: linear-gradient(to right top, #36c1e1, #28ccc0, #74cf8e, #bac962, #feb95b); border-image-slice: 1; }
.border-gradient-fast-blue-purple { border-image: linear-gradient(to right top, #0039e3, #4132e0, #5e28dd, #741bd9, #8600d4); border-image-slice: 1; }
.border-gradient-light-purple-light-red { border-image: linear-gradient(to right top, #bb85f9, #c887e5, #da8bca, #eb8fb0, #fb9398); border-image-slice: 1; }
.border-gradient-light-brownish-orange-black { border-image: linear-gradient(to right top, #e79e57, #9d9049, #62794c, #405c4c, #353d3c); border-image-slice: 1; }
.border-gradient-red-violet-purple { border-image: linear-gradient(to right, #b93d90, #a92b95, #951b9c, #7a11a4, #5412ad); border-image-slice: 1; }

/* border style */
.border-dotted { border-style: dotted !important; }
.border-dashed { border-style: dashed !important; }
.border-solid { border-style: solid !important; }
.border-double { border-style: double !important; }
.border-groove { border-style: groove !important; }
.border-ridge { border-style: ridge !important; }
.border-inset { border-style: inset !important; }
.border-outset { border-style: outset !important; }
.border-none { border-style: none !important; }
.border-hidden { border-style: hidden !important; }
.border-transperent { border-color: transparent !important; }

/* no border */
.no-border { border:0 !important; }
.no-border-top { border-top:0 !important; }
.no-border-bottom { border-bottom:0 !important; }
.no-border-right { border-right:0 !important; }
.no-border-left { border-left:0 !important; }
.no-border-top-bottom { border-top:0 !important; border-bottom: 0 !important; }
.no-border-left-right { border-left:0 !important; border-right: 0 !important; }

/* border radius */
.border-radius-0px { border-radius: 0; }
.border-radius-1px { border-radius:1px; }
.border-radius-2px { border-radius:2px; }
.border-radius-3px { border-radius:3px; }
.border-radius-4px { border-radius:4px; }
.border-radius-5px { border-radius:5px; }
.border-radius-6px { border-radius:6px; }
.border-radius-7px { border-radius:7px; }
.border-radius-8px { border-radius:8px; }
.border-radius-9px { border-radius:9px; }
.border-radius-10px { border-radius:10px; }
.border-radius-20px { border-radius:20px; }
.border-radius-30px { border-radius:30px; }
.border-radius-40px { border-radius:40px; }
.border-radius-50px { border-radius:50px; }
.border-radius-100px { border-radius:100px; }
.border-radius-50 { border-radius:50%; }
.border-radius-100 { border-radius:100%; }
.no-border-radius { border-radius:0 !important; }

/* ===================================
    08. Video
====================================== */

.video-wrapper { height: 100%; overflow: hidden; position: absolute; width: 100%; }
.html-video { background-color: #000; left: 50%; width: auto; height:auto; min-height: 100%; min-width: 100%; position: absolute; top: 0; -ms-transform: translate(-50%, 0); -webkit-transform: translate(-50%, 0); transform: translate(-50%, 0); -moz-transform: translate(-50%, 0); -o-transform: translate(-50%, 0); z-index: -3; }
.video-wrapper iframe { height: 100%; left: 0; position: absolute; top: -5px !important; width: 100%; }
.video-background { position: absolute; height: 100%; width: 100%; z-index: 1; top:0; background: rgba(67,67,67,0.5) }

.html-video-play { position: relative; text-align: center; }
.html-video-play .play-icon, .html-video-play .pause-icon { position: absolute; left: 50%; padding-left: 8px; -webkit-transform: translate(-50%, -50%); -moz-transform: translate(-50%, -50%); -ms-transform: translate(-50%, -50%); -o-transform: translate(-50%, -50%); transform: translate(-50%, -50%); }
.html-video-play[ playing = "true" ] .play-icon, .html-video-play .pause-icon { opacity: 0; top: 0; visibility: hidden; -webkit-transition-duration: 0.5s; -moz-transition-duration: 0.5s; -ms-transition-duration: 0.5s; -o-transition-duration: 0.5s; transition-duration: 0.5s; }
.html-video-play[ playing = "true" ] .play-icon { top: 0; }
.html-video-play .pause-icon { top: 100%; }
.html-video-play .play-icon, .html-video-play[ playing = "true" ] .pause-icon { opacity: 1; top: 50%; visibility: visible; -webkit-transition-duration: 0.5s; -moz-transition-duration: 0.5s; -ms-transition-duration: 0.5s; -o-transition-duration: 0.5s; transition-duration: 0.5s; }
.play-icon-animation { -webkit-transform: scale(1); -ms-transform: scale(1); -moz-transform: scale(1); transform: scale(1); -o-transform: scale(1); transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out ;}
.play-icon-animation:hover { -webkit-transform: scale(1.1); -ms-transform: scale(1.1); -moz-transform: scale(1.1); transform: scale(1.1); -o-transform: scale(1.1); transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out; }

.bg-video-wrapper { position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: -1; pointer-events: none; overflow: hidden; }
.bg-video-wrapper iframe { width: 100vw; height: 56.25vw; min-height: 100vh; min-width: 177.77vh; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); }

/* ===================================
    09. Background image
====================================== */

.cover-background { background-repeat: no-repeat !important; background-size: cover !important; overflow: hidden; position: relative; background-position: center center !important; }
.fix-background { position: relative; background-size: cover !important; -webkit-animation-duration: 0s; -webkit-animation-fill-mode: none; overflow: hidden; background-position: center center; background-repeat: no-repeat !important; background-attachment: fixed !important; }
.background-size-inherit { background-size: inherit; }
.background-attachment-inherit { background-attachment: inherit; }
.background-position-left { background-position: left center; }
.background-position-left-bottom { background-position: left bottom; }
.background-position-right { background-position: right center; }
.background-position-top { background-position: right top; }
.background-position-center-top { background-position: center top; }
.background-position-right-bottom { background-position: right bottom; }
.background-position-center-bottom { background-position: center bottom !important; }
.background-repeat { background-repeat:repeat; }
.background-no-repeat { background-repeat:no-repeat; }
.background-position-center { background-position: center; }
.up-down-ani { display:table; position: relative; -webkit-animation-name: down-arrow; -webkit-animation-duration:0.8s; -webkit-animation-timing-function: linear; -webkit-animation-delay:0.8s; -webkit-animation-iteration-count: infinite; -webkit-animation-direction: alternate; animation-name: down-arrow; animation-duration:0.8s; animation-timing-function: linear; animation-delay:0.8s; animation-iteration-count: infinite; animation-direction: alternate; margin:0 auto; }
.cover-img { object-fit: cover; height: 100%; }

/* background parallax */
.parallax { position: relative !important; background-size: cover !important; overflow: hidden; background-attachment: fixed !important; transition-duration: 0s; -moz-transition-duration: 0s; -webkit-transition-duration: 0s; -o-transition-duration: 0s; }
.parallax-fix { background-attachment: fixed !important; background-position: center center; background-repeat: no-repeat; background-size: cover !important; overflow: hidden; position: relative; transition-duration: 0.2s; transition-timing-function: ease-in; }

/* ===================================
    10. Margin
====================================== */

.margin-one-all { margin:1%; }
.margin-two-all { margin:2%; }
.margin-three-all { margin:3%; }
.margin-four-all { margin:4%; }
.margin-five-all { margin:5%; }
.margin-six-all { margin:6%; }
.margin-seven-all { margin:7%; }
.margin-eight-all { margin:8%; }
.margin-nine-all { margin:9%; }
.margin-ten-all { margin:10%; }
.margin-eleven-all { margin:11%; }
.margin-twelve-all { margin:12%; }
.margin-thirteen-all { margin:13%; }
.margin-fourteen-all { margin:14%; }
.margin-fifteen-all { margin:15%; }
.margin-sixteen-all { margin:16%; }
.margin-seventeen-all { margin:17%; }
.margin-eighteen-all { margin:18%; }
.margin-nineteen-all { margin:19%; }
.margin-twenty-all { margin:20%; }
.margin-5px-all { margin:5px; }
.margin-10px-all { margin:10px; }
.margin-15px-all { margin:15px; }
.margin-20px-all { margin:20px; }
.margin-25px-all { margin:25px; }
.margin-30px-all { margin:30px; }
.margin-35px-all { margin:35px; }
.margin-40px-all { margin:40px; }
.margin-45px-all { margin:45px; }
.margin-50px-all { margin:50px; }
.margin-55px-all { margin:55px; }
.margin-60px-all { margin:60px; }
.margin-65px-all { margin:65px; }
.margin-70px-all { margin:70px; }
.margin-75px-all { margin:75px; }
.margin-80px-all { margin:80px; }
.margin-85px-all { margin:85px; }
.margin-90px-all { margin:90px; }
.margin-95px-all { margin:95px; }
.margin-100px-all { margin:100px; }
.margin-1-rem-all { margin: 1rem; }
.margin-1-half-rem-all { margin: 1.5rem; }
.margin-2-rem-all { margin: 2rem; }
.margin-2-half-rem-all { margin: 2.5rem; }
.margin-3-rem-all { margin: 3rem; }
.margin-3-half-rem-all { margin: 3.5rem; }
.margin-4-rem-all { margin: 4rem; }
.margin-4-half-rem-all { margin: 4.5rem; }
.margin-5-rem-all { margin: 5rem; }
.margin-5-half-rem-all { margin: 5.5rem; }
.margin-6-rem-all { margin: 6rem; }
.margin-6-half-rem-all { margin: 6.5rem; }
.margin-7-rem-all { margin: 7rem; }
.margin-7-half-rem-all { margin: 7.5rem; }
.margin-8-rem-all { margin: 8rem; }
.margin-8-half-rem-all { margin: 8.5rem; }
.margin-9-rem-all { margin: 9rem; }
.margin-9-half-rem-all { margin: 9.5rem; }
.margin-10-rem-all { margin: 10rem; }
.margin-10-half-rem-all { margin: 10.5rem; }

/* margin top */
.margin-one-top { margin-top:1%; }
.margin-two-top { margin-top:2%; }
.margin-three-top { margin-top:3%; }
.margin-four-top { margin-top:4%; }
.margin-five-top { margin-top:5%; }
.margin-six-top { margin-top:6%; }
.margin-seven-top { margin-top:7%; }
.margin-eight-top { margin-top:8%; }
.margin-nine-top { margin-top:9%; }
.margin-ten-top { margin-top:10%; }
.margin-eleven-top { margin-top:11%; }
.margin-twelve-top { margin-top:12%; }
.margin-thirteen-top { margin-top:13%; }
.margin-fourteen-top { margin-top:14%; }
.margin-fifteen-top { margin-top:15%; }
.margin-sixteen-top { margin-top:16%; }
.margin-seventeen-top { margin-top:17%; }
.margin-eighteen-top { margin-top:18%; }
.margin-nineteen-top { margin-top:19%; }
.margin-twenty-top { margin-top:20%; }
.margin-twenty-five-top { margin-top:20%; }
.margin-5px-top { margin-top:5px; }
.margin-10px-top { margin-top:10px; }
.margin-15px-top { margin-top:15px; }
.margin-20px-top { margin-top:20px; }
.margin-25px-top { margin-top:25px; }
.margin-30px-top { margin-top:30px; }
.margin-35px-top { margin-top:35px; }
.margin-40px-top { margin-top:40px; }
.margin-45px-top { margin-top:45px; }
.margin-50px-top { margin-top:50px; }
.margin-55px-top { margin-top:55px; }
.margin-60px-top { margin-top:60px; }
.margin-65px-top { margin-top:65px; }
.margin-70px-top { margin-top:70px; }
.margin-75px-top { margin-top:75px; }
.margin-80px-top { margin-top:80px; }
.margin-85px-top { margin-top:85px; }
.margin-90px-top { margin-top:90px; }
.margin-95px-top { margin-top:95px; }
.margin-100px-top { margin-top:100px; }
.margin-1-rem-top { margin-top: 1rem; }
.margin-1-half-rem-top { margin-top: 1.5rem; }
.margin-2-rem-top { margin-top: 2rem; }
.margin-2-half-rem-top { margin-top: 2.5rem; }
.margin-3-rem-top { margin-top: 3rem; }
.margin-3-half-rem-top { margin-top: 3.5rem; }
.margin-4-rem-top { margin-top: 4rem; }
.margin-4-half-rem-top { margin-top: 4.5rem; }
.margin-5-rem-top { margin-top: 5rem; }
.margin-5-half-rem-top { margin-top: 5.5rem; }
.margin-6-rem-top { margin-top: 6rem; }
.margin-6-half-rem-top { margin-top: 6.5rem; }
.margin-7-rem-top { margin-top: 7rem; }
.margin-7-half-rem-top { margin-top: 7.5rem; }
.margin-8-rem-top { margin-top: 8rem; }
.margin-8-half-rem-top { margin-top: 8.5rem; }
.margin-9-rem-top { margin-top: 9rem; }
.margin-9-half-rem-top { margin-top: 9.5rem; }
.margin-10-rem-top { margin-top: 10rem; }
.margin-10-half-rem-top { margin-top: 10.5rem; }

/* margin bottom */
.margin-one-bottom { margin-bottom:1%; }
.margin-two-bottom { margin-bottom:2%; }
.margin-three-bottom { margin-bottom:3%; }
.margin-four-bottom { margin-bottom:4%; }
.margin-five-bottom { margin-bottom:5%; }
.margin-six-bottom { margin-bottom:6%; }
.margin-seven-bottom { margin-bottom:7%; }
.margin-eight-bottom { margin-bottom:8%; }
.margin-nine-bottom { margin-bottom:9%; }
.margin-ten-bottom { margin-bottom:10%; }
.margin-eleven-bottom { margin-bottom:11%; }
.margin-twelve-bottom { margin-bottom:12%; }
.margin-thirteen-bottom { margin-bottom:13%; }
.margin-fourteen-bottom { margin-bottom:14%; }
.margin-fifteen-bottom { margin-bottom:15%; }
.margin-sixteen-bottom { margin-bottom:16%; }
.margin-seventeen-bottom { margin-bottom:17%; }
.margin-eighteen-bottom { margin-bottom:18%; }
.margin-nineteen-bottom { margin-bottom:19%; }
.margin-twenty-bottom { margin-bottom:20%; }
.margin-5px-bottom { margin-bottom:5px; }
.margin-10px-bottom { margin-bottom:10px; }
.margin-15px-bottom { margin-bottom:15px; }
.margin-20px-bottom { margin-bottom:20px; }
.margin-25px-bottom { margin-bottom:25px; }
.margin-30px-bottom { margin-bottom:30px; }
.margin-35px-bottom { margin-bottom:35px; }
.margin-40px-bottom { margin-bottom:40px; }
.margin-45px-bottom { margin-bottom:45px; }
.margin-50px-bottom { margin-bottom:50px; }
.margin-55px-bottom { margin-bottom:55px; }
.margin-60px-bottom { margin-bottom:60px; }
.margin-65px-bottom { margin-bottom:65px; }
.margin-70px-bottom { margin-bottom:70px; }
.margin-75px-bottom { margin-bottom:75px; }
.margin-80px-bottom { margin-bottom:80px; }
.margin-85px-bottom { margin-bottom:85px; }
.margin-90px-bottom { margin-bottom:90px; }
.margin-95px-bottom { margin-bottom:95px; }
.margin-100px-bottom { margin-bottom:100px; }
.margin-1-rem-bottom { margin-bottom: 1rem; }
.margin-1-half-rem-bottom { margin-bottom: 1.5rem; }
.margin-2-rem-bottom { margin-bottom: 2rem; }
.margin-2-half-rem-bottom { margin-bottom: 2.5rem; }
.margin-3-rem-bottom { margin-bottom: 3rem; }
.margin-3-half-rem-bottom { margin-bottom: 3.5rem; }
.margin-4-rem-bottom { margin-bottom: 4rem; }
.margin-4-half-rem-bottom { margin-bottom: 4.5rem; }
.margin-5-rem-bottom { margin-bottom: 5rem; }
.margin-5-half-rem-bottom { margin-bottom: 5.5rem; }
.margin-6-rem-bottom { margin-bottom: 6rem; }
.margin-6-half-rem-bottom { margin-bottom: 6.5rem; }
.margin-7-rem-bottom { margin-bottom: 7rem; }
.margin-7-half-rem-bottom { margin-bottom: 7.5rem; }
.margin-8-rem-bottom { margin-bottom: 8rem; }
.margin-8-half-rem-bottom { margin-bottom: 8.5rem; }
.margin-9-rem-bottom { margin-bottom: 9rem; }
.margin-9-half-rem-bottom { margin-bottom: 9.5rem; }
.margin-10-rem-bottom { margin-bottom: 10rem; }
.margin-10-half-rem-bottom { margin-bottom: 10.5rem; }
.margin-15-rem-bottom { margin-bottom: 15rem; }
.margin-27-rem-bottom { margin-bottom: 27rem; }

/* margin right */
.margin-one-right { margin-right:1%; }
.margin-two-right { margin-right:2%; }
.margin-three-right { margin-right:3%; }
.margin-four-right { margin-right:4%; }
.margin-five-right { margin-right:5%; }
.margin-six-right { margin-right:6%; }
.margin-seven-right { margin-right:7%; }
.margin-eight-right { margin-right:8%; }
.margin-nine-right { margin-right:9%; }
.margin-ten-right { margin-right:10%; }
.margin-eleven-right { margin-right:11%; }
.margin-twelve-right { margin-right:12%; }
.margin-thirteen-right { margin-right:13%; }
.margin-fourteen-right { margin-right:14%; }
.margin-fifteen-right { margin-right:15%; }
.margin-sixteen-right { margin-right:16%; }
.margin-seventeen-right { margin-right:17%; }
.margin-eighteen-right { margin-right:18%; }
.margin-nineteen-right { margin-right:19%; }
.margin-twenty-right { margin-right:20%; }
.margin-5px-right { margin-right:5px; }
.margin-10px-right { margin-right:10px; }
.margin-15px-right { margin-right:15px; }
.margin-20px-right { margin-right:20px; }
.margin-25px-right { margin-right:25px; }
.margin-30px-right { margin-right:30px; }
.margin-35px-right { margin-right:35px; }
.margin-40px-right { margin-right:40px; }
.margin-45px-right { margin-right:45px; }
.margin-50px-right { margin-right:50px; }
.margin-55px-right { margin-right:55px; }
.margin-60px-right { margin-right:60px; }
.margin-65px-right { margin-right:65px; }
.margin-70px-right { margin-right:70px; }
.margin-75px-right { margin-right:75px; }
.margin-80px-right { margin-right:80px; }
.margin-85px-right { margin-right:85px; }
.margin-90px-right { margin-right:90px; }
.margin-95px-right { margin-right:95px; }
.margin-100px-right { margin-right:100px; }
.margin-1-rem-right { margin-right: 1rem; }
.margin-1-half-rem-right { margin-right: 1.5rem; }
.margin-2-rem-right { margin-right: 2rem; }
.margin-2-half-rem-right { margin-right: 2.5rem; }
.margin-3-rem-right { margin-right: 3rem; }
.margin-3-half-rem-right { margin-right: 3.5rem; }
.margin-4-rem-right { margin-right: 4rem; }
.margin-4-half-rem-right { margin-right: 4.5rem; }
.margin-5-rem-right { margin-right: 5rem; }
.margin-5-half-rem-right { margin-right: 5.5rem; }
.margin-6-rem-right { margin-right: 6rem; }
.margin-6-half-rem-right { margin-right: 6.5rem; }
.margin-7-rem-right { margin-right: 7rem; }
.margin-7-half-rem-right { margin-right: 7.5rem; }
.margin-8-rem-right { margin-right: 8rem; }
.margin-8-half-rem-right { margin-right: 8.5rem; }
.margin-9-rem-right { margin-right: 9rem; }
.margin-9-half-rem-right { margin-right: 9.5rem; }
.margin-10-rem-right { margin-right: 10rem; }
.margin-10-half-rem-right { margin-right: 10.5rem; }

/* margin left */
.margin-one-left { margin-left:1%; }
.margin-two-left { margin-left:2%; }
.margin-three-left { margin-left:3%; }
.margin-four-left { margin-left:4%; }
.margin-five-left { margin-left:5%; }
.margin-six-left { margin-left:6%; }
.margin-seven-left { margin-left:7%; }
.margin-eight-left { margin-left:8%; }
.margin-nine-left { margin-left:9%; }
.margin-ten-left { margin-left:10%; }
.margin-eleven-left { margin-left:11%; }
.margin-twelve-left { margin-left:12%; }
.margin-thirteen-left { margin-left:13%; }
.margin-fourteen-left { margin-left:14%; }
.margin-fifteen-left { margin-left:15%; }
.margin-sixteen-left { margin-left:16%; }
.margin-seventeen-left { margin-left:17%; }
.margin-eighteen-left { margin-left:18%; }
.margin-nineteen-left { margin-left:19%; }
.margin-twenty-left { margin-left:20%; }
.margin-5px-left { margin-left:5px; }
.margin-10px-left { margin-left:10px; }
.margin-15px-left { margin-left:15px; }
.margin-20px-left { margin-left:20px; }
.margin-25px-left { margin-left:25px; }
.margin-30px-left { margin-left:30px; }
.margin-35px-left { margin-left:35px; }
.margin-40px-left { margin-left:40px; }
.margin-45px-left { margin-left:45px; }
.margin-50px-left { margin-left:50px; }
.margin-55px-left { margin-left:55px; }
.margin-60px-left { margin-left:60px; }
.margin-65px-left { margin-left:65px; }
.margin-70px-left { margin-left:70px; }
.margin-75px-left { margin-left:75px; }
.margin-80px-left { margin-left:80px; }
.margin-85px-left { margin-left:85px; }
.margin-90px-left { margin-left:90px; }
.margin-95px-left { margin-left:95px; }
.margin-100px-left { margin-left:100px; }
.margin-1-rem-left { margin-left: 1rem; }
.margin-1-half-rem-left { margin-left: 1.5rem; }
.margin-2-rem-left { margin-left: 2rem; }
.margin-2-half-rem-left { margin-left: 2.5rem; }
.margin-3-rem-left { margin-left: 3rem; }
.margin-3-half-rem-left { margin-left: 3.5rem; }
.margin-4-rem-left { margin-left: 4rem; }
.margin-4-half-rem-left { margin-left: 4.5rem; }
.margin-5-rem-left { margin-left: 5rem; }
.margin-5-half-rem-left { margin-left: 5.5rem; }
.margin-6-rem-left { margin-left: 6rem; }
.margin-6-half-rem-left { margin-left: 6.5rem; }
.margin-7-rem-left { margin-left: 7rem; }
.margin-7-half-rem-left { margin-left: 7.5rem; }
.margin-8-rem-left { margin-left: 8rem; }
.margin-8-half-rem-left { margin-left: 8.5rem; }
.margin-9-rem-left { margin-left: 9rem; }
.margin-9-half-rem-left { margin-left: 9.5rem; }
.margin-10-rem-left { margin-left: 10rem; }
.margin-10-half-rem-left { margin-left: 10.5rem; }

/* margin left right */
.margin-one-lr { margin-left:1%; margin-right:1%; }
.margin-two-lr { margin-left:2%; margin-right:2%; }
.margin-three-lr { margin-left:3%; margin-right:3%; }
.margin-four-lr { margin-left:4%; margin-right:4%; }
.margin-five-lr { margin-left:5%; margin-right:5%; }
.margin-six-lr { margin-left:6%; margin-right:6%; }
.margin-seven-lr { margin-left:7%; margin-right:7%; }
.margin-eight-lr { margin-left:8%; margin-right:8%; }
.margin-nine-lr { margin-left:9%; margin-right:9%; }
.margin-ten-lr { margin-left:10%; margin-right:10%; }
.margin-eleven-lr { margin-left:11%; margin-right:11%; }
.margin-twelve-lr { margin-left:12%; margin-right:12%; }
.margin-thirteen-lr { margin-left:13%; margin-right:13%; }
.margin-fourteen-lr { margin-left:14%; margin-right:14%; }
.margin-fifteen-lr { margin-left:15%; margin-right:15%; }
.margin-sixteen-lr { margin-left:16%; margin-right:16%; }
.margin-seventeen-lr { margin-left:17%; margin-right:17%; }
.margin-eighteen-lr { margin-left:18%; margin-right:18%; }
.margin-nineteen-lr { margin-left:19%; margin-right:19%; }
.margin-twenty-lr { margin-left:20%; margin-right:20%; }
.margin-5px-lr { margin-left:5px; margin-right:5px; }
.margin-10px-lr { margin-left:10px; margin-right:10px; }
.margin-15px-lr { margin-left:15px; margin-right:15px; }
.margin-20px-lr { margin-left:20px; margin-right:20px; }
.margin-25px-lr { margin-left:25px; margin-right:25px; }
.margin-30px-lr { margin-left:30px; margin-right:30px; }
.margin-35px-lr { margin-left:35px; margin-right:35px; }
.margin-40px-lr { margin-left:40px; margin-right:40px; }
.margin-45px-lr { margin-left:45px; margin-right:45px; }
.margin-50px-lr { margin-left:50px; margin-right:50px; }
.margin-55px-lr { margin-left:55px; margin-right:55px; }
.margin-60px-lr { margin-left:60px; margin-right:60px; }
.margin-65px-lr { margin-left:65px; margin-right:65px; }
.margin-70px-lr { margin-left:70px; margin-right:70px; }
.margin-75px-lr { margin-left:75px; margin-right:75px; }
.margin-80px-lr { margin-left:80px; margin-right:80px; }
.margin-85px-lr { margin-left:85px; margin-right:85px; }
.margin-90px-lr { margin-left:90px; margin-right:90px; }
.margin-95px-lr { margin-left:95px; margin-right:95px; }
.margin-100px-lr { margin-left:100px; margin-right:100px; }
.margin-1-rem-lr { margin-left: 1rem; margin-right: 1rem; }
.margin-1-half-rem-lr { margin-left: 1.5rem; margin-right: 1.5rem; }
.margin-2-rem-lr { margin-left: 2rem; margin-right: 2rem; }
.margin-2-half-rem-lr { margin-left: 2.5rem; margin-right: 2.5rem; }
.margin-3-rem-lr { margin-left: 3rem; margin-right: 3rem; }
.margin-3-half-rem-lr { margin-left: 3.5rem; margin-right: 3.5rem; }
.margin-4-rem-lr { margin-left: 4rem; margin-right: 4rem; }
.margin-4-half-rem-lr { margin-left: 4.5rem; margin-right: 4.5rem; }
.margin-5-rem-lr { margin-left: 5rem; margin-right: 5rem; }
.margin-5-half-rem-lr { margin-left: 5.5rem; margin-right: 5.5rem; }
.margin-6-rem-lr { margin-left: 6rem; margin-right: 6rem; }
.margin-6-half-rem-lr { margin-left: 6.5rem; margin-right: 6.5rem; }
.margin-7-rem-lr { margin-left: 7rem; margin-right: 7rem; }
.margin-7-half-rem-lr { margin-left: 7.5rem; margin-right: 7.5rem; }
.margin-8-rem-lr { margin-left: 8rem; margin-right: 8rem; }
.margin-8-half-rem-lr { margin-left: 8.5rem; margin-right: 8.5rem; }
.margin-9-rem-lr { margin-left: 9rem; margin-right: 9rem; }
.margin-9-half-rem-lr { margin-left: 9.5rem; margin-right: 9.5rem; }
.margin-10-rem-lr { margin-left: 10rem; margin-right: 10rem; }
.margin-10-half-rem-lr { margin-left: 10.5rem; margin-right: 10.5rem; }

/* margin top bottom */
.margin-one-tb { margin-top:1%; margin-bottom:1%; }
.margin-two-tb { margin-top:2%; margin-bottom:2%; }
.margin-three-tb { margin-top:3%; margin-bottom:3%; }
.margin-four-tb { margin-top:4%; margin-bottom:4%; }
.margin-five-tb { margin-top:5%; margin-bottom:5%; }
.margin-six-tb { margin-top:6%; margin-bottom:6%; }
.margin-seven-tb { margin-top:7%; margin-bottom:7%; }
.margin-eight-tb { margin-top:8%; margin-bottom:8%; }
.margin-nine-tb { margin-top:9%; margin-bottom:9%; }
.margin-ten-tb { margin-top:10%; margin-bottom:10%; }
.margin-eleven-tb { margin-top:11%; margin-bottom:11%; }
.margin-twelve-tb { margin-top:12%; margin-bottom:12%; }
.margin-thirteen-tb { margin-top:13%; margin-bottom:13%; }
.margin-fourteen-tb { margin-top:14%; margin-bottom:14%; }
.margin-fifteen-tb { margin-top:15%; margin-bottom:15%; }
.margin-sixteen-tb { margin-top:16%; margin-bottom:16%; }
.margin-seventeen-tb { margin-top:17%; margin-bottom:17%; }
.margin-eighteen-tb { margin-top:18%; margin-bottom:18%; }
.margin-nineteen-tb { margin-top:19%; margin-bottom:19%; }
.margin-twenty-tb { margin-top:20%; margin-bottom:20%; }
.margin-5px-tb { margin-top:5px; margin-bottom:5px; }
.margin-10px-tb { margin-top:10px; margin-bottom:10px; }
.margin-15px-tb { margin-top:15px; margin-bottom:15px; }
.margin-20px-tb { margin-top:20px; margin-bottom:20px; }
.margin-25px-tb { margin-top:25px; margin-bottom:25px; }
.margin-30px-tb { margin-top:30px; margin-bottom:30px; }
.margin-35px-tb { margin-top:35px; margin-bottom:35px; }
.margin-40px-tb { margin-top:40px; margin-bottom:40px; }
.margin-45px-tb { margin-top:45px; margin-bottom:45px; }
.margin-50px-tb { margin-top:50px; margin-bottom:50px; }
.margin-55px-tb { margin-top:55px; margin-bottom:55px; }
.margin-60px-tb { margin-top:60px; margin-bottom:60px; }
.margin-65px-tb { margin-top:65px; margin-bottom:65px; }
.margin-70px-tb { margin-top:70px; margin-bottom:70px; }
.margin-75px-tb { margin-top:75px; margin-bottom:75px; }
.margin-80px-tb { margin-top:80px; margin-bottom:80px; }
.margin-85px-tb { margin-top:85px; margin-bottom:85px; }
.margin-90px-tb { margin-top:90px; margin-bottom:90px; }
.margin-95px-tb { margin-top:95px; margin-bottom:95px; }
.margin-100px-tb { margin-top:100px; margin-bottom:100px; }
.margin-1-rem-tb { margin-top: 1rem; margin-bottom: 1rem; }
.margin-1-half-rem-tb { margin-top: 1.5rem; margin-bottom: 1.5rem; }
.margin-2-rem-tb { margin-top: 2rem; margin-bottom: 2rem; }
.margin-2-half-rem-tb { margin-top: 2.5rem; margin-bottom: 2.5rem; }
.margin-3-rem-tb { margin-top: 3rem; margin-bottom: 3rem; }
.margin-3-half-rem-tb { margin-top: 3.5rem; margin-bottom: 3.5rem; }
.margin-4-rem-tb { margin-top: 4rem; margin-bottom: 4rem; }
.margin-4-half-rem-tb { margin-top: 4.5rem; margin-bottom: 4.5rem; }
.margin-5-rem-tb { margin-top: 5rem; margin-bottom: 5rem; }
.margin-5-half-rem-tb { margin-top: 5.5rem; margin-bottom: 5.5rem; }
.margin-6-rem-tb { margin-top: 6rem; margin-bottom: 6rem; }
.margin-6-half-rem-tb { margin-top: 6.5rem; margin-bottom: 6.5rem; }
.margin-7-rem-tb { margin-top: 7rem; margin-bottom: 7rem; }
.margin-7-half-rem-tb { margin-top: 7.5rem; margin-bottom: 7.5rem; }
.margin-8-rem-tb { margin-top: 8rem; margin-bottom: 8rem; }
.margin-8-half-rem-tb { margin-top: 8.5rem; margin-bottom: 8.5rem; }
.margin-9-rem-tb { margin-top: 9rem; margin-bottom: 9rem; }
.margin-9-half-rem-tb { margin-top: 9.5rem; margin-bottom: 9.5rem; }
.margin-10-rem-tb { margin-top: 10rem; margin-bottom: 10rem; }
.margin-10-half-rem-tb { margin-top: 10.5rem; margin-bottom: 10.5rem; }

.margin-auto-lr { margin: 0 auto; }
.margin-auto { margin: auto; }
.no-margin { margin: 0 !important; }
.no-margin-top { margin-top: 0 !important; }
.no-margin-bottom { margin-bottom: 0 !important; }
.no-margin-left { margin-left: 0 !important; }
.no-margin-right { margin-right: 0 !important; }
.no-margin-lr { margin-right: 0 !important; margin-left: 0 !important; }

/* ===================================
    11. Padding
====================================== */

.padding-one-all { padding:1%; }
.padding-two-all { padding:2%; }
.padding-three-all { padding:3%; }
.padding-four-all { padding:4%; }
.padding-five-all { padding:5%; }
.padding-six-all { padding:6%; }
.padding-seven-all { padding:7%; }
.padding-eight-all { padding:8%; }
.padding-nine-all { padding:9%; }
.padding-ten-all { padding:10%; }
.padding-eleven-all { padding:11%; }
.padding-twelve-all { padding:12%; }
.padding-thirteen-all { padding:13%; }
.padding-fourteen-all { padding:14%; }
.padding-fifteen-all { padding:15%; }
.padding-sixteen-all { padding:16%; }
.padding-seventeen-all { padding:17%; }
.padding-eighteen-all { padding:18%; }
.padding-nineteen-all { padding:19%; }
.padding-twenty-all { padding:20%; }
.padding-5px-all { padding:5px; }
.padding-10px-all { padding:10px; }
.padding-15px-all { padding:15px; }
.padding-20px-all { padding:20px; }
.padding-25px-all { padding:25px; }
.padding-30px-all { padding:30px; }
.padding-35px-all { padding:35px; }
.padding-40px-all { padding:40px; }
.padding-45px-all { padding:45px; }
.padding-50px-all { padding:50px; }
.padding-55px-all { padding:55px; }
.padding-60px-all { padding:60px; }
.padding-65px-all { padding:65px; }
.padding-70px-all { padding:70px; }
.padding-75px-all { padding:75px; }
.padding-80px-all { padding:80px; }
.padding-85px-all { padding:85px; }
.padding-90px-all { padding:90px; }
.padding-95px-all { padding:95px; }
.padding-100px-all { padding:100px; }
.padding-1-rem-all { padding: 1rem; }
.padding-1-half-rem-all { padding: 1.5rem; }
.padding-2-rem-all { padding: 2rem; }
.padding-2-half-rem-all { padding: 2.5rem; }
.padding-3-rem-all { padding: 3rem; }
.padding-3-half-rem-all { padding: 3.5rem; }
.padding-4-rem-all { padding: 4rem; }
.padding-4-half-rem-all { padding: 4.5rem; }
.padding-5-rem-all { padding: 5rem; }
.padding-5-half-rem-all { padding: 5.5rem; }
.padding-6-rem-all { padding: 6rem; }
.padding-6-half-rem-all { padding: 6.5rem; }
.padding-7-rem-all { padding: 7rem; }
.padding-7-half-rem-all { padding: 7.5rem; }
.padding-8-rem-all { padding: 8rem; }
.padding-8-half-rem-all { padding: 8.5rem; }
.padding-9-rem-all { padding: 9rem; }
.padding-9-half-rem-all { padding: 9.5rem; }
.padding-10-rem-all { padding: 10rem; }
.padding-10-half-rem-all { padding: 10.5rem; }

/* padding top */
.padding-one-top { padding-top:1%; }
.padding-two-top { padding-top:2%; }
.padding-three-top { padding-top:3%; }
.padding-four-top { padding-top:4%; }
.padding-five-top { padding-top:5%; }
.padding-six-top { padding-top:6%; }
.padding-seven-top { padding-top:7%; }
.padding-eight-top { padding-top:8%; }
.padding-nine-top { padding-top:9%; }
.padding-ten-top { padding-top:10%; }
.padding-eleven-top { padding-top:11%; }
.padding-twelve-top { padding-top:12%; }
.padding-thirteen-top { padding-top:13%; }
.padding-fourteen-top { padding-top:14%; }
.padding-fifteen-top { padding-top:15%; }
.padding-sixteen-top { padding-top:16%; }
.padding-seventeen-top { padding-top:17%; }
.padding-eighteen-top { padding-top:18%; }
.padding-nineteen-top { padding-top:19%; }
.padding-twenty-top { padding-top:20%; }
.padding-5px-top { padding-top:5px; }
.padding-10px-top { padding-top:10px; }
.padding-15px-top { padding-top:15px; }
.padding-20px-top { padding-top:20px; }
.padding-25px-top { padding-top:25px; }
.padding-30px-top { padding-top:30px; }
.padding-35px-top { padding-top:35px; }
.padding-40px-top { padding-top:40px; }
.padding-45px-top { padding-top:45px; }
.padding-50px-top { padding-top:50px; }
.padding-55px-top { padding-top:55px; }
.padding-60px-top { padding-top:60px; }
.padding-65px-top { padding-top:65px; }
.padding-70px-top { padding-top:70px; }
.padding-75px-top { padding-top:75px; }
.padding-80px-top { padding-top:80px; }
.padding-85px-top { padding-top:85px; }
.padding-90px-top { padding-top:90px; }
.padding-95px-top { padding-top:95px; }
.padding-100px-top { padding-top:100px; }
.padding-1-rem-top { padding-top: 1rem; }
.padding-1-half-rem-top { padding-top: 1.5rem; }
.padding-2-rem-top { padding-top: 2rem; }
.padding-2-half-rem-top { padding-top: 2.5rem; }
.padding-3-rem-top { padding-top: 3rem; }
.padding-3-half-rem-top { padding-top: 3.5rem; }
.padding-4-rem-top { padding-top: 4rem; }
.padding-4-half-rem-top { padding-top: 4.5rem; }
.padding-5-rem-top { padding-top: 5rem; }
.padding-5-half-rem-top { padding-top: 5.5rem; }
.padding-6-rem-top { padding-top: 6rem; }
.padding-6-half-rem-top { padding-top: 6.5rem; }
.padding-7-rem-top { padding-top: 7rem; }
.padding-7-half-rem-top { padding-top: 7.5rem; }
.padding-8-rem-top { padding-top: 8rem; }
.padding-8-half-rem-top { padding-top: 8.5rem; }
.padding-9-rem-top { padding-top: 9rem; }
.padding-9-half-rem-top { padding-top: 9.5rem; }
.padding-10-rem-top { padding-top: 10rem; }
.padding-10-half-rem-top { padding-top: 10.5rem; }

/* padding bottom */
.padding-one-bottom { padding-bottom:1%; }
.padding-two-bottom { padding-bottom:2%; }
.padding-three-bottom { padding-bottom:3%; }
.padding-four-bottom { padding-bottom:4%; }
.padding-five-bottom { padding-bottom:5%; }
.padding-six-bottom { padding-bottom:6%; }
.padding-seven-bottom { padding-bottom:7%; }
.padding-eight-bottom { padding-bottom:8%; }
.padding-nine-bottom { padding-bottom:9%; }
.padding-ten-bottom { padding-bottom:10%; }
.padding-eleven-bottom { padding-bottom:11%; }
.padding-twelve-bottom { padding-bottom:12%; }
.padding-thirteen-bottom { padding-bottom:13%; }
.padding-fourteen-bottom { padding-bottom:14%; }
.padding-fifteen-bottom { padding-bottom:15%; }
.padding-sixteen-bottom { padding-bottom:16%; }
.padding-seventeen-bottom { padding-bottom:17%; }
.padding-eighteen-bottom { padding-bottom:18%; }
.padding-nineteen-bottom { padding-bottom:19%; }
.padding-twenty-bottom { padding-bottom:20%; }
.padding-5px-bottom { padding-bottom:5px; }
.padding-10px-bottom { padding-bottom:10px; }
.padding-15px-bottom { padding-bottom:15px; }
.padding-20px-bottom { padding-bottom:20px; }
.padding-25px-bottom { padding-bottom:25px; }
.padding-30px-bottom { padding-bottom:30px; }
.padding-35px-bottom { padding-bottom:35px; }
.padding-40px-bottom { padding-bottom:40px; }
.padding-45px-bottom { padding-bottom:45px; }
.padding-50px-bottom { padding-bottom:50px; }
.padding-55px-bottom { padding-bottom:55px; }
.padding-60px-bottom { padding-bottom:60px; }
.padding-65px-bottom { padding-bottom:65px; }
.padding-70px-bottom { padding-bottom:70px; }
.padding-75px-bottom { padding-bottom:75px; }
.padding-80px-bottom { padding-bottom:80px; }
.padding-85px-bottom { padding-bottom:85px; }
.padding-90px-bottom { padding-bottom:90px; }
.padding-95px-bottom { padding-bottom:95px; }
.padding-100px-bottom { padding-bottom:100px; }
.padding-1-rem-bottom { padding-bottom: 1rem; }
.padding-1-half-rem-bottom { padding-bottom: 1.5rem; }
.padding-2-rem-bottom { padding-bottom: 2rem; }
.padding-2-half-rem-bottom { padding-bottom: 2.5rem; }
.padding-3-rem-bottom { padding-bottom: 3rem; }
.padding-3-half-rem-bottom { padding-bottom: 3.5rem; }
.padding-4-rem-bottom { padding-bottom: 4rem; }
.padding-4-half-rem-bottom { padding-bottom: 4.5rem; }
.padding-5-rem-bottom { padding-bottom: 5rem; }
.padding-5-half-rem-bottom { padding-bottom: 5.5rem; }
.padding-6-rem-bottom { padding-bottom: 6rem; }
.padding-6-half-rem-bottom { padding-bottom: 6.5rem; }
.padding-7-rem-bottom { padding-bottom: 7rem; }
.padding-7-half-rem-bottom { padding-bottom: 7.5rem; }
.padding-8-rem-bottom { padding-bottom: 8rem; }
.padding-8-half-rem-bottom { padding-bottom: 8.5rem; }
.padding-9-rem-bottom { padding-bottom: 9rem; }
.padding-9-half-rem-bottom { padding-bottom: 9.5rem; }
.padding-10-rem-bottom { padding-bottom: 10rem; }
.padding-10-half-rem-bottom { padding-bottom: 10.5rem; }

/* padding right */
.padding-one-right { padding-right:1%; }
.padding-two-right { padding-right:2%; }
.padding-three-right { padding-right:3%; }
.padding-four-right { padding-right:4% }
.padding-five-right { padding-right:5%; }
.padding-six-right { padding-right:6%; }
.padding-seven-right { padding-right:7%; }
.padding-eight-right { padding-right:8%; }
.padding-nine-right { padding-right:9%; }
.padding-ten-right { padding-right:10%; }
.padding-eleven-right { padding-right:11%; }
.padding-twelve-right { padding-right:12%; }
.padding-thirteen-right { padding-right:13%; }
.padding-fourteen-right { padding-right:14%; }
.padding-fifteen-right { padding-right:15%; }
.padding-sixteen-right { padding-right:16%; }
.padding-seventeen-right { padding-right:17%; }
.padding-eighteen-right { padding-right:18%; }
.padding-nineteen-right { padding-right:19%; }
.padding-twenty-right { padding-right:20%; }
.padding-5px-right { padding-right:5px; }
.padding-10px-right { padding-right:10px; }
.padding-15px-right { padding-right:15px; }
.padding-20px-right { padding-right:20px; }
.padding-25px-right { padding-right:25px; }
.padding-30px-right { padding-right:30px; }
.padding-35px-right { padding-right:35px; }
.padding-40px-right { padding-right:40px; }
.padding-45px-right { padding-right:45px; }
.padding-50px-right { padding-right:50px; }
.padding-55px-right { padding-right:55px; }
.padding-60px-right { padding-right:60px; }
.padding-65px-right { padding-right:65px; }
.padding-70px-right { padding-right:70px; }
.padding-75px-right { padding-right:75px; }
.padding-80px-right { padding-right:80px; }
.padding-85px-right { padding-right:85px; }
.padding-90px-right { padding-right:90px; }
.padding-95px-right { padding-right:95px; }
.padding-100px-right { padding-right:100px; }
.padding-1-rem-right { padding-right: 1rem; }
.padding-1-half-rem-right { padding-right: 1.5rem; }
.padding-2-rem-right { padding-right: 2rem; }
.padding-2-half-rem-right { padding-right: 2.5rem; }
.padding-3-rem-right { padding-right: 3rem; }
.padding-3-half-rem-right { padding-right: 3.5rem; }
.padding-4-rem-right { padding-right: 4rem; }
.padding-4-half-rem-right { padding-right: 4.5rem; }
.padding-5-rem-right { padding-right: 5rem; }
.padding-5-half-rem-right { padding-right: 5.5rem; }
.padding-6-rem-right { padding-right: 6rem; }
.padding-6-half-rem-right { padding-right: 6.5rem; }
.padding-7-rem-right { padding-right: 7rem; }
.padding-7-half-rem-right { padding-right: 7.5rem; }
.padding-8-rem-right { padding-right: 8rem; }
.padding-8-half-rem-right { padding-right: 8.5rem; }
.padding-9-rem-right { padding-right: 9rem; }
.padding-9-half-rem-right { padding-right: 9.5rem; }
.padding-10-rem-right { padding-right: 10rem; }
.padding-10-half-rem-right { padding-right: 10.5rem; }

/* padding left */
.padding-one-left { padding-left:1%; }
.padding-two-left { padding-left:2%; }
.padding-three-left { padding-left:3%; }
.padding-four-left { padding-left:4%; }
.padding-five-left { padding-left:5%; }
.padding-six-left { padding-left:6%; }
.padding-seven-left { padding-left:7%; }
.padding-eight-left { padding-left:8%; }
.padding-nine-left { padding-left:9%; }
.padding-ten-left { padding-left:10%; }
.padding-eleven-left { padding-left:11%; }
.padding-twelve-left { padding-left:12%; }
.padding-thirteen-left { padding-left:13%; }
.padding-fourteen-left { padding-left:14%; }
.padding-fifteen-left { padding-left:15%; }
.padding-sixteen-left { padding-left:16%; }
.padding-seventeen-left { padding-left:17%; }
.padding-eighteen-left { padding-left:18%; }
.padding-nineteen-left { padding-left:19%; }
.padding-twenty-left { padding-left:20%; }
.padding-5px-left { padding-left:5px; }
.padding-10px-left { padding-left:10px; }
.padding-15px-left { padding-left:15px; }
.padding-20px-left { padding-left:20px; }
.padding-25px-left { padding-left:25px; }
.padding-30px-left { padding-left:30px; }
.padding-35px-left { padding-left:35px; }
.padding-40px-left { padding-left:40px; }
.padding-45px-left { padding-left:45px; }
.padding-50px-left { padding-left:50px; }
.padding-55px-left { padding-left:55px; }
.padding-60px-left { padding-left:60px; }
.padding-65px-left { padding-left:65px; }
.padding-70px-left { padding-left:70px; }
.padding-75px-left { padding-left:75px; }
.padding-80px-left { padding-left:80px; }
.padding-85px-left { padding-left:85px; }
.padding-90px-left { padding-left:90px; }
.padding-95px-left { padding-left:95px; }
.padding-100px-left { padding-left:100px; }
.padding-1-rem-left { padding-left: 1rem; }
.padding-1-half-rem-left { padding-left: 1.5rem; }
.padding-2-rem-left { padding-left: 2rem; }
.padding-2-half-rem-left { padding-left: 2.5rem; }
.padding-3-rem-left { padding-left: 3rem; }
.padding-3-half-rem-left { padding-left: 3.5rem; }
.padding-4-rem-left { padding-left: 4rem; }
.padding-4-half-rem-left { padding-left: 4.5rem; }
.padding-5-rem-left { padding-left: 5rem; }
.padding-5-half-rem-left { padding-left: 5.5rem; }
.padding-6-rem-left { padding-left: 6rem; }
.padding-6-half-rem-left { padding-left: 6.5rem; }
.padding-7-rem-left { padding-left: 7rem; }
.padding-7-half-rem-left { padding-left: 7.5rem; }
.padding-8-rem-left { padding-left: 8rem; }
.padding-8-half-rem-left { padding-left: 8.5rem; }
.padding-9-rem-left { padding-left: 9rem; }
.padding-9-half-rem-left { padding-left: 9.5rem; }
.padding-10-rem-left { padding-left: 10rem; }
.padding-10-half-rem-left { padding-left: 10.5rem; }

/* padding top bottom */
.padding-one-tb { padding-top:1%; padding-bottom:1%; }
.padding-two-tb { padding-top:2%; padding-bottom:2%; }
.padding-three-tb { padding-top:3%; padding-bottom:3%; }
.padding-four-tb { padding-top:4%; padding-bottom:4%; }
.padding-five-tb { padding-top:5%; padding-bottom:5%; }
.padding-six-tb { padding-top:6%; padding-bottom:6%; }
.padding-seven-tb { padding-top:7%; padding-bottom:7%; }
.padding-eight-tb { padding-top:8%; padding-bottom:8%; }
.padding-nine-tb { padding-top:9%; padding-bottom:9%; }
.padding-ten-tb { padding-top:10%; padding-bottom:10%; }
.padding-eleven-tb { padding-top:11%; padding-bottom:11%; }
.padding-twelve-tb { padding-top:12%; padding-bottom:12%; }
.padding-thirteen-tb { padding-top:13%; padding-bottom:13%; }
.padding-fourteen-tb { padding-top:14%; padding-bottom:14%; }
.padding-fifteen-tb { padding-top:15%; padding-bottom:15%; }
.padding-sixteen-tb { padding-top:16%; padding-bottom:16%; }
.padding-seventeen-tb { padding-top:17%; padding-bottom:17%; }
.padding-eighteen-tb { padding-top:18%; padding-bottom:18%; }
.padding-nineteen-tb { padding-top:19%; padding-bottom:19%; }
.padding-twenty-tb { padding-top:20%; padding-bottom:20%; }
.padding-5px-tb { padding-top:5px; padding-bottom:5px; }
.padding-10px-tb { padding-top:10px; padding-bottom:10px; }
.padding-15px-tb { padding-top:15px; padding-bottom:15px; }
.padding-20px-tb { padding-top:20px; padding-bottom:20px; }
.padding-25px-tb { padding-top:25px; padding-bottom:25px; }
.padding-30px-tb { padding-top:30px; padding-bottom:30px; }
.padding-35px-tb { padding-top:35px; padding-bottom:35px; }
.padding-40px-tb { padding-top:40px; padding-bottom:40px; }
.padding-45px-tb { padding-top:45px; padding-bottom:45px; }
.padding-50px-tb { padding-top:50px; padding-bottom:50px; }
.padding-55px-tb { padding-top:55px; padding-bottom:55px; }
.padding-60px-tb { padding-top:60px; padding-bottom:60px; }
.padding-65px-tb { padding-top:65px; padding-bottom:65px; }
.padding-70px-tb { padding-top:70px; padding-bottom:70px; }
.padding-75px-tb { padding-top:75px; padding-bottom:75px; }
.padding-80px-tb { padding-top:80px; padding-bottom:80px; }
.padding-85px-tb { padding-top:85px; padding-bottom:85px; }
.padding-90px-tb { padding-top:90px; padding-bottom:90px; }
.padding-95px-tb { padding-top:95px; padding-bottom:95px; }
.padding-100px-tb { padding-top:100px; padding-bottom:100px; }
.padding-1-rem-tb { padding-top: 1rem; padding-bottom: 1rem; }
.padding-1-half-rem-tb { padding-top: 1.5rem; padding-bottom: 1.5rem; }
.padding-2-rem-tb { padding-top: 2rem; padding-bottom: 2rem; }
.padding-2-half-rem-tb { padding-top: 2.5rem; padding-bottom: 2.5rem; }
.padding-3-rem-tb { padding-top: 3rem; padding-bottom: 3rem; }
.padding-3-half-rem-tb { padding-top: 3.5rem; padding-bottom: 3.5rem; }
.padding-4-rem-tb { padding-top: 4rem; padding-bottom: 4rem; }
.padding-4-half-rem-tb { padding-top: 4.5rem; padding-bottom: 4.5rem; }
.padding-5-rem-tb { padding-top: 5rem; padding-bottom: 5rem; }
.padding-5-half-rem-tb { padding-top: 5.5rem; padding-bottom: 5.5rem; }
.padding-6-rem-tb { padding-top: 6rem; padding-bottom: 6rem; }
.padding-6-half-rem-tb { padding-top: 6.5rem; padding-bottom: 6.5rem; }
.padding-7-rem-tb { padding-top: 7rem; padding-bottom: 7rem; }
.padding-7-half-rem-tb { padding-top: 7.5rem; padding-bottom: 7.5rem; }
.padding-8-rem-tb { padding-top: 8rem; padding-bottom: 8rem; }
.padding-8-half-rem-tb { padding-top: 8.5rem; padding-bottom: 8.5rem; }
.padding-9-rem-tb { padding-top: 9rem; padding-bottom: 9rem; }
.padding-9-half-rem-tb { padding-top: 9.5rem; padding-bottom: 9.5rem; }
.padding-10-rem-tb { padding-top: 10rem; padding-bottom: 10rem; }
.padding-10-half-rem-tb { padding-top: 10.5rem; padding-bottom: 10.5rem; }

/* padding left right */
.padding-one-lr { padding-left:1%; padding-right:1%; }
.padding-two-lr { padding-left:2%; padding-right:2%; }
.padding-three-lr { padding-left:3%; padding-right:3%; }
.padding-four-lr { padding-left:4%; padding-right:4%; }
.padding-five-lr { padding-left:5%; padding-right:5%; }
.padding-six-lr { padding-left:6%; padding-right:6%; }
.padding-seven-lr { padding-left:7%; padding-right:7%; }
.padding-eight-lr { padding-left:8%; padding-right:8%; }
.padding-nine-lr { padding-left:9%; padding-right:9%; }
.padding-ten-lr { padding-left:10%; padding-right:10%; }
.padding-eleven-lr { padding-left:11%; padding-right:11%; }
.padding-twelve-lr { padding-left:12%; padding-right:12%; }
.padding-thirteen-lr { padding-left:13%; padding-right:13%; }
.padding-fourteen-lr { padding-left:14%; padding-right:14%; }
.padding-fifteen-lr { padding-left:15%; padding-right:15%; }
.padding-sixteen-lr { padding-left:16%; padding-right:16%; }
.padding-seventeen-lr { padding-left:17%; padding-right:17%; }
.padding-eighteen-lr { padding-left:18%; padding-right:18%; }
.padding-nineteen-lr { padding-left:19%; padding-right:19%; }
.padding-twenty-lr { padding-left:20%; padding-right:20%; }
.padding-5px-lr { padding-left:5px; padding-right:5px; }
.padding-10px-lr { padding-left:10px; padding-right:10px; }
.padding-15px-lr { padding-left:15px; padding-right:15px; }
.padding-20px-lr { padding-left:20px; padding-right:20px; }
.padding-25px-lr { padding-left:25px; padding-right:25px; }
.padding-30px-lr { padding-left:30px; padding-right:30px; }
.padding-35px-lr { padding-left:35px; padding-right:35px; }
.padding-40px-lr { padding-left:40px; padding-right:40px; }
.padding-45px-lr { padding-left:45px; padding-right:45px; }
.padding-50px-lr { padding-left:50px; padding-right:50px; }
.padding-55px-lr { padding-left:55px; padding-right:55px; }
.padding-60px-lr { padding-left:60px; padding-right:60px; }
.padding-65px-lr { padding-left:65px; padding-right:65px; }
.padding-70px-lr { padding-left:70px; padding-right:70px; }
.padding-75px-lr { padding-left:75px; padding-right:75px; }
.padding-80px-lr { padding-left:80px; padding-right:80px; }
.padding-85px-lr { padding-left:85px; padding-right:85px; }
.padding-90px-lr { padding-left:90px; padding-right:90px; }
.padding-95px-lr { padding-left:95px; padding-right:95px; }
.padding-100px-lr { padding-left:100px; padding-right:100px; }
.padding-1-rem-lr { padding-left: 1rem; padding-right: 1rem; }
.padding-1-half-rem-lr { padding-left: 1.5rem; padding-right: 1.5rem; }
.padding-2-rem-lr { padding-left: 2rem; padding-right: 2rem; }
.padding-2-half-rem-lr { padding-left: 2.5rem; padding-right: 2.5rem; }
.padding-3-rem-lr { padding-left: 3rem; padding-right: 3rem; }
.padding-3-half-rem-lr { padding-left: 3.5rem; padding-right: 3.5rem; }
.padding-4-rem-lr { padding-left: 4rem; padding-right: 4rem; }
.padding-4-half-rem-lr { padding-left: 4.5rem; padding-right: 4.5rem; }
.padding-5-rem-lr { padding-left: 5rem; padding-right: 5rem; }
.padding-5-half-rem-lr { padding-left: 5.5rem; padding-right: 5.5rem; }
.padding-6-rem-lr { padding-left: 6rem; padding-right: 6rem; }
.padding-6-half-rem-lr { padding-left: 6.5rem; padding-right: 6.5rem; }
.padding-7-rem-lr { padding-left: 7rem; padding-right: 7rem; }
.padding-7-half-rem-lr { padding-left: 7.5rem; padding-right: 7.5rem; }
.padding-8-rem-lr { padding-left: 8rem; padding-right: 8rem; }
.padding-8-half-rem-lr { padding-left: 8.5rem; padding-right: 8.5rem; }
.padding-9-rem-lr { padding-left: 9rem; padding-right: 9rem; }
.padding-9-half-rem-lr { padding-left: 9.5rem; padding-right: 9.5rem; }
.padding-10-rem-lr { padding-left: 10rem; padding-right: 10rem; }
.padding-10-half-rem-lr { padding-left: 10.5rem; padding-right: 10.5rem; }

.no-padding { padding:0 !important; }
.no-padding-lr { padding-left: 0 !important; padding-right: 0 !important; }
.no-padding-tb { padding-top: 0 !important; padding-bottom: 0 !important; }
.no-padding-top { padding-top:0 !important; }
.no-padding-bottom { padding-bottom:0 !important; }
.no-padding-left { padding-left:0 !important; }
.no-padding-right { padding-right:0 !important; }

/* ===================================
    12. Display and overflow
====================================== */

.d-initial { display: initial !important; }
.overflow-hidden { overflow:hidden !important; }
.overflow-visible { overflow:visible !important; }
.overflow-auto { overflow:auto !important; }

/* ===================================
    13. Position
====================================== */

.position-inherit { position: inherit !important; }

/* top */
.top-0px { top: 0; }
.top-1px { top: 1px; }
.top-2px { top: 2px; }
.top-3px { top: 3px; }
.top-4px { top: 4px; }
.top-5px { top: 5px; }
.top-6px { top: 6px; }
.top-7px { top: 7px; }
.top-8px { top: 8px; }
.top-9px { top: 9px; }
.top-10px { top: 10px; }
.top-15px { top: 15px; }
.top-20px { top: 20px; }
.top-25px { top: 25px; }
.top-30px { top: 30px; }
.top-35px { top: 35px; }
.top-40px { top: 40px; }
.top-45px { top: 45px; }
.top-50px { top: 50px; }
.top-60px { top: 60px; }
.top-70px { top: 70px; }
.top-80px { top: 80px; }
.top-90px { top: 90px; }
.top-100px { top: 100px; }
.top-auto { top:auto; }
.top-inherit { top:inherit; }

/* top minus */
.top-minus-1px { top: -1px; }
.top-minus-2px { top: -2px; }
.top-minus-3px { top: -3px; }
.top-minus-4px { top: -4px; }
.top-minus-5px { top: -5px; }
.top-minus-6px { top: -6px; }
.top-minus-7px { top: -7px; }
.top-minus-8px { top: -8px; }
.top-minus-9px { top: -9px; }
.top-minus-10px { top: -10px; }
.top-minus-15px { top: -15px; }
.top-minus-20px { top: -20px; }
.top-minus-25px { top: -25px; }
.top-minus-30px { top: -30px; }
.top-minus-35px { top: -35px; }
.top-minus-40px { top: -40px; }
.top-minus-45px { top: -45px; }
.top-minus-50px { top: -50px; }
.top-minus-60px { top: -60px; }
.top-minus-70px { top: -70px; }

/* bottom */
.bottom-0px { bottom:0; }
.bottom-1px { bottom:1px; }
.bottom-2px { bottom:2px; }
.bottom-3px { bottom:3px; }
.bottom-4px { bottom:4px; }
.bottom-5px { bottom:5px; }
.bottom-6px { bottom:6px; }
.bottom-7px { bottom:7px; }
.bottom-8px { bottom:8px; }
.bottom-9px { bottom:9px; }
.bottom-10px { bottom:10px; }
.bottom-15px { bottom:15px; }
.bottom-20px { bottom:20px; }
.bottom-25px { bottom:25px; }
.bottom-30px { bottom:30px; }
.bottom-35px { bottom:35px; }
.bottom-40px { bottom:40px; }
.bottom-45px { bottom:45px; }
.bottom-50px { bottom:50px; }
.bottom-55px { bottom:55px; }
.bottom-60px { bottom:60px; }
.bottom-65px { bottom:65px; }
.bottom-70px { bottom:70px; }
.bottom-75px { bottom:75px; }
.bottom-80px { bottom:80px; }
.bottom-85px { bottom:85px; }
.bottom-90px { bottom:90px; }
.bottom-95px { bottom:95px; }
.bottom-100px { bottom:100px; }
.bottom-auto { bottom: auto; }
.bottom-inherit { bottom: inherit; }

/* bottom minus */
.bottom-minus-1px { bottom: -1px; }
.bottom-minus-2px { bottom: -2px; }
.bottom-minus-3px { bottom: -3px; }
.bottom-minus-4px { bottom: -4px; }
.bottom-minus-5px { bottom: -5px; }
.bottom-minus-6px { bottom: -6px; }
.bottom-minus-7px { bottom: -7px; }
.bottom-minus-8px { bottom: -8px; }
.bottom-minus-9px { bottom: -9px; }
.bottom-minus-10px { bottom: -10px; }
.bottom-minus-15px { bottom: -15px; }
.bottom-minus-20px { bottom: -20px; }
.bottom-minus-25px { bottom: -25px; }
.bottom-minus-30px { bottom: -30px; }
.bottom-minus-35px { bottom: -35px; }
.bottom-minus-40px { bottom: -40px; }
.bottom-minus-45px { bottom: -45px; }
.bottom-minus-50px { bottom: -50px; }

/* right */
.right-0px { right: 0; }
.right-1px { right: 1px; }
.right-2px { right: 2px; }
.right-3px { right: 3px; }
.right-4px { right: 4px; }
.right-5px { right: 5px; }
.right-6px { right: 6px; }
.right-7px { right: 7px; }
.right-8px { right: 8px; }
.right-9px { right: 9px; }
.right-10px { right: 10px; }
.right-15px { right: 15px; }
.right-20px { right: 20px; }
.right-25px { right: 25px; }
.right-30px { right: 30px; }
.right-35px { right: 35px; }
.right-40px { right: 40px; }
.right-45px { right: 45px; }
.right-50px { right: 50px; }
.right-55px { right: 55px; }
.right-60px { right: 60px; }
.right-auto { right: auto; }
.right-inherit { right: inherit; }

/* right minus */
.right-minus-1px { right: -1px; }
.right-minus-2px { right: -2px; }
.right-minus-3px { right: -3px; }
.right-minus-4px { right: -4px; }
.right-minus-5px { right: -5px; }
.right-minus-6px { right: -6px; }
.right-minus-7px { right: -7px; }
.right-minus-8px { right: -8px; }
.right-minus-9px { right: -9px; }
.right-minus-10px { right: -10px; }
.right-minus-15px { right: -15px; }
.right-minus-20px { right: -20px; }
.right-minus-25px { right: -25px; }
.right-minus-30px { right: -30px; }
.right-minus-35px { right: -35px; }
.right-minus-40px { right: -40px; }
.right-minus-45px { right: -45px; }
.right-minus-50px { right: -50px; }
.right-minus-60px { right: -60px; }
.right-minus-70px { right: -70px; }

/* left */
.left-0px { left: 0; }
.left-1px { left: 1px; }
.left-2px { left: 2px; }
.left-3px { left: 3px; }
.left-4px { left: 4px; }
.left-5px { left: 5px; }
.left-6px { left: 6px; }
.left-7px { left: 7px; }
.left-8px { left: 8px; }
.left-9px { left: 9px; }
.left-10px { left: 10px; }
.left-15px { left: 15px; }
.left-20px { left: 20px; }
.left-25px { left: 25px; }
.left-30px { left: 30px; }
.left-35px { left: 35px; }
.left-40px { left: 40px; }
.left-45px { left: 45px; }
.left-50px { left: 50px; }
.left-auto { left: auto; }
.left-inherit { left: inherit; }

/* left minus */
.left-0px { left: 0; }
.left-minus-1px { left: -1px; }
.left-minus-2px { left: -2px; }
.left-minus-3px { left: -3px; }
.left-minus-4px { left: -4px; }
.left-minus-5px { left: -5px; }
.left-minus-6px { left: -6px; }
.left-minus-7px { left: -7px; }
.left-minus-8px { left: -8px; }
.left-minus-9px { left: -9px; }
.left-minus-10px { left: -10px; }
.left-minus-15px { left: -15px; }
.left-minus-20px { left: -20px; }
.left-minus-25px { left: -25px; }
.left-minus-30px { left: -30px; }
.left-minus-35px { left: -35px; }
.left-minus-40px { left: -40px; }
.left-minus-45px { left: -45px; }
.left-minus-50px { left: -50px; }

/* ===================================
    14. Width
====================================== */

.w-1px { width:1px; }
.w-2px { width:2px; }
.w-3px { width:3px; }
.w-4px { width:4px; }
.w-5px { width:5px; }
.w-6px { width:6px; }
.w-7px { width:7px; }
.w-8px { width:8px; }
.w-9px { width:9px; }
.w-10px { width:10px; }
.w-15px { width:15px; }
.w-20px { width:20px; }
.w-25px { width:25px; }
.w-30px { width:30px; }
.w-35px { width:35px; }
.w-40px { width:40px; }
.w-45px { width:45px; }
.w-50px { width:50px; }
.w-55px { width:55px; }
.w-60px { width:60px; }
.w-65px { width:65px; }
.w-70px { width:70px; }
.w-75px { width:75px; }
.w-80px { width:80px; }
.w-85px { width:85px; }
.w-90px { width:90px; }
.w-95px { width:95px; }
.w-100px { width:100px; }
.w-110px { width:110px; }
.w-120px { width:120px; }
.w-130px { width:130px; }
.w-140px { width:140px; }
.w-150px { width:150px; }
.w-160px { width:160px; }
.w-170px { width:170px; }
.w-180px { width:180px; }
.w-190px { width:190px; }
.w-200px { width:200px; }
.w-250px { width:250px; }
.w-300px { width:300px; }
.w-350px { width:350px; }
.w-400px { width:400px; }
.w-450px { width:450px; }
.w-500px { width:500px; }
.w-550px { width:550px; }
.w-600px { width:600px; }
.w-650px { width:650px; }
.w-700px { width:700px; }
.w-750px { width:750px; }
.w-800px { width:800px; }
.w-850px { width:850px; }
.w-900px { width:900px; }
.w-950px { width:950px; }
.w-1000px { width:1000px; }

.w-10 { width: 10%; }
.w-15 { width: 15%; }
.w-20 { width: 20%; }
.w-25 { width: 25%; }
.w-30 { width: 30%; }
.w-35 { width: 35%; }
.w-40 { width: 40%; }
.w-45 { width: 45%; }
.w-50 { width: 50%; }
.w-55 { width: 55%; }
.w-57 { width: 57%; }
.w-60 { width: 60%; }
.w-65 { width: 65%; }
.w-70 { width: 70%; }
.w-75 { width: 75%; }
.w-80 { width: 80%; }
.w-85 { width: 85%; }
.w-90 { width: 90%; }
.w-95 { width: 95%; }
.w-100 { width: 100%; }
.w-auto { width:auto; }

/* ===================================
    15. Height
====================================== */

.swiper-container { width: 100%; height: 100%; }
.h-1px { height: 1px; }
.h-2px { height: 2px; }
.h-3px { height: 3px; }
.h-4px { height: 4px; }
.h-5px { height: 5px; }
.h-6px { height: 6px; }
.h-7px { height: 7px; }
.h-8px { height: 8px; }
.h-9px { height: 9px; }
.h-10px { height: 10px; }
.h-15px { height: 15px; }
.h-20px { height: 20px; }
.h-25px { height: 25px; }
.h-30px { height: 30px; }
.h-35px { height: 35px; }
.h-40px { height: 40px; }
.h-42px { height: 42px; }
.h-45px { height: 45px; }
.h-50px { height: 50px; }
.h-55px { height: 55px; }
.h-60px { height: 60px; }
.h-65px { height: 65px; }
.h-70px { height: 70px; }
.h-75px { height: 75px; }
.h-80px { height: 80px; }
.h-85px { height: 85px; }
.h-90px { height: 90px; }
.h-95px { height: 95px; }
.h-100px { height: 100px; }
.h-110px { height: 110px; }
.h-120px { height: 120px; }
.h-130px { height: 130px; }
.h-140px { height: 140px; }
.h-150px { height: 150px; }
.h-160px { height: 160px; }
.h-170px { height: 170px; }
.h-180px { height: 180px; }
.h-190px { height: 190px; }
.h-200px { height: 200px; }
.h-250px { height: 250px; }
.h-300px { height: 300px; }
.h-350px { height: 350px; }
.h-400px { height: 400px; }
.h-450px { height: 450px; }
.h-480px { height: 490px; }
.h-500px { height: 500px; }
.h-520px { height: 520px; }
.h-550px { height: 550px; }
.h-580px { height: 580px; }
.h-600px { height: 600px; }
.h-650px { height: 650px; }
.h-700px { height: 700px; }
.h-720px { height: 720px; }
.h-750px { height: 750px; }
.h-800px { height: 800px; }
.h-820px { height: 820px; }
.h-830px { height: 830px; }
.h-850px { height: 850px; }

.h-50 { height: 50%; }
.h-100 { height: 100% !important; }
.h-auto { height:auto !important; }
.max-h-100 { max-height: 100%; }
.max-h-inherit { max-height: inherit !important; }
.full-screen, .mobileoff-fullscreen-top-space, .fullscreenabanner { height: 980px; }

/* min height */
.min-h-100px { min-height: 100px; }
.min-h-200px { min-height: 200px; }
.min-h-300px { min-height: 300px; }
.min-h-400px { min-height: 400px; }
.min-h-500px { min-height: 500px; }
.min-h-600px { min-height: 600px; }
.min-h-700px { min-height: 700px; }
.min-h-100vh { min-height: 100vh; }

/* screen height */
.extra-small-screen { height:300px; }
.small-screen { height:400px; }
.one-half-screen { height:600px; }
.one-third-screen { height:700px; }
.one-fourth-screen { height:800px; }
.one-fifth-screen { height:900px; }

/* content box image */
.content-box-image { width: 100%; height: -webkit-calc(100% / 3); height: -moz-calc(100% / 3); height: calc(100% / 3); }

/* ===================================
    16. Icon
====================================== */

[class^="ti-"], [class*=" ti-"], [class^="line-icon"], [class*=" line-icon"], [class^="solid-icon"], [class*=" solid-icon"] { display: inline-block; }

/* video icons */
.video-icon-box { z-index: 1; }
.video-icon-box > span { display: -webkit-inline-box; display: -ms-inline-flexbox; display: inline-flex; -webkit-box-orient: horizontal; -webkit-box-direction: normal; flex-flow: row wrap; -ms-flex-direction: row wrap; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; }
.video-icon-box .video-icon { position: relative; display: -webkit-inline-box; display: -ms-inline-flexbox; display: inline-flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; border-radius: 50%; }
.video-icon-box .video-icon i { margin-left: 5px; line-height: normal; }
.video-icon-large.video-icon-box .video-icon i { margin-left: 8px;}
.video-icon-double-large.video-icon-box .video-icon i { margin-left: 10px;}
.video-icon .video-icon-sonar { position: absolute; width: 100%; height: 100%; border-radius: 50%; top: 0; left: 0; z-index: -10; -webkit-animation: video-icon-sonar 2s linear infinite; animation: video-icon-sonar 2s linear infinite; }
.video-icon .video-icon-sonar .video-icon-sonar-bfr { position: absolute; border-radius: 50%; content: ''; top: -25px; left: -25px; z-index: -100; }
.video-icon .video-icon-sonar .video-icon-sonar-afr { position: absolute; border-radius: 50%; content: ''; top: -10px; left: -10px; z-index: -50; }
.video-icon-small .video-icon .video-icon-sonar .video-icon-sonar-bfr { top: -15px; left: -15px; }
.video-icon-small .video-icon .video-icon-sonar .video-icon-sonar-afr { top: 0px; left: 0px; }

/* video icon size */
.video-icon-small .video-icon { width: 40px; height: 40px; font-size: 14px; }
.video-icon-small .video-icon .video-icon-sonar .video-icon-sonar-bfr { width: 70px; height: 70px; }
.video-icon-small .video-icon .video-icon-sonar .video-icon-sonar-afr { width: 40px; height: 40px; }
.video-icon-medium .video-icon { width: 50px; height: 50px; font-size: 18px; }
.video-icon-medium .video-icon .video-icon-sonar .video-icon-sonar-bfr { width: 100px; height: 100px; }
.video-icon-medium .video-icon .video-icon-sonar .video-icon-sonar-afr { width: 70px; height: 70px; }
.video-icon-large .video-icon { width: 75px; height: 75px; font-size: 26px; }
.video-icon-large .video-icon .video-icon-sonar .video-icon-sonar-bfr { width: 125px; height: 125px; }
.video-icon-large .video-icon .video-icon-sonar .video-icon-sonar-afr { width: 95px; height: 95px; }
.video-icon-extra-large .video-icon { width: 100px; height: 100px; font-size: 25px; }
.video-icon-extra-large .video-icon .video-icon-sonar .video-icon-sonar-bfr { width: 150px; height: 150px; }
.video-icon-extra-large .video-icon .video-icon-sonar .video-icon-sonar-afr { width: 120px; height: 120px; }
.video-icon-double-large .video-icon { width: 120px; height: 120px; font-size: 34px; }
.video-icon-double-large .video-icon .video-icon-sonar .video-icon-sonar-bfr { width: 170px; height: 170px; }
.video-icon-double-large .video-icon .video-icon-sonar .video-icon-sonar-afr { width: 140px; height: 140px; }

/* icon size */
.icon-extra-large { font-size:60px; }
.icon-large { font-size:50px; }
.icon-medium { font-size:40px; }
.icon-very-medium { font-size:35px; }
.icon-extra-medium { font-size:32px; }
.icon-small { font-size:28px; }
.icon-extra-small { font-size:18px; }
.icon-very-small { font-size:13px; }
.icon-country { width: 16px; height: 16px; display: inline-block; margin:0 10px 0 5px; }

/* social media icon */
.extra-small-icon, .small-icon, .medium-icon, .large-icon, .extra-large-icon { list-style: none; padding: 0; }
.extra-small-icon li, .small-icon li, .medium-icon li, .large-icon li, .extra-large-icon li { display: inline-block; }
.extra-small-icon li:first-child, .small-icon li:first-child, .medium-icon li:first-child, .large-icon li:first-child, .extra-large-icon li:first-child { margin-left: 0; }
.extra-small-icon li:last-child, .small-icon li:last-child, .medium-icon li:last-child, .large-icon li:last-child, .extra-large-icon li:last-child { margin-right: 0; }
.extra-small-icon li { margin: 0 2px; }
.small-icon li { margin: 0 5px; }
.medium-icon li { margin: 0 8px; }
.large-icon li { margin: 0 10px; }
.extra-large-icon li { margin: 0 12px; }
.extra-small-icon a { font-size: 14px; width:30px; height:30px;line-height:30px; }
.small-icon a { font-size: 16px; width: 40px; height:40px;line-height: 40px; }
.medium-icon a { font-size: 20px; width: 50px; height:50px;line-height: 50px; }
.large-icon a { font-size: 24px; width: 60px; height: 60px;line-height: 60px; }
.extra-large-icon a { font-size: 30px; width: 70px; height: 70px;line-height: 70px; }
.social-icon a { margin: 0 15px; }
.social-icon a:first-child { margin-left: 0; }
.social-icon a:last-child { margin-right: 0; }
[class^="social-icon-"] ul, [class*=" social-icon-"] ul { list-style: none; }

/* icon like counter */
a.likes-count { width: auto; border: 1px solid #e4e4e4; font-size: 12px; border-radius: 4px; padding:5px 18px 6px; line-height: 20px; margin: 0 0 10px 0; display: inline-block }
a.likes-count i { margin-right: 8px; color: #fa5c47 }
a.likes-count:hover {box-shadow: 0 0 10px rgba(23,23,23,.1); color: #232323}

/* social icon style 01 */
.social-icon-style-01 ul li a { text-align: center; display: inline-block }
.social-icon-style-01 a:hover { color: #0038e3; }

/* social icon style 02 */
.social-icon-style-02 ul li a { text-align: center; display: inline-block; color: #232323}
.social-icon-style-02.icon-with-animation a { display: inline-block;  }
.social-icon-style-02.icon-with-animation a:hover { transform: translateY(-8px); }
.social-icon-style-02 ul.light li a {color: #fff}
.social-icon-style-02 ul.dark li a {color: #232323}

.social-icon-style-02 a.facebook:hover, .social-icon-style-04 a.facebook:hover, .social-icon-style-07 a.facebook, .social-icon-style-08 a.facebook, .social-icon-style-10 a.facebook, .social-icon-style-11 a.facebook .social-front i, .menu-sidebar a .facebook { color: #3b5998; }
.social-icon-style-02 a.twitter:hover, .social-icon-style-04 a.twitter:hover, .social-icon-style-07 a.twitter, .social-icon-style-08 a.twitter, .social-icon-style-10 a.twitter, .social-icon-style-11 a.twitter .social-front i, .menu-sidebar a .twitter { color: #00aced; }
.social-icon-style-02 a.google:hover, .social-icon-style-04 a.google:hover, .social-icon-style-07 a.google, .social-icon-style-08 a.google, .social-icon-style-10 a.google, .social-icon-style-11 a.google .social-front i, .menu-sidebar a .google { color: #dc4a38; }
.social-icon-style-02 a.dribbble:hover, .social-icon-style-04 a.dribbble:hover, .social-icon-style-07 a.dribbble, .social-icon-style-08 a.dribbble, .social-icon-style-10 a.dribbble, .social-icon-style-11 a.dribbble .social-front i, .menu-sidebar a .dribbble { color: #ea4c89; }
.social-icon-style-02 a.youtube:hover, .social-icon-style-04 a.youtube:hover, .social-icon-style-07 a.youtube, .social-icon-style-08 a.youtube, .social-icon-style-10 a.youtube, .social-icon-style-11 a.youtube .social-front i, .menu-sidebar a .youtube { color: #bb0000; }
.social-icon-style-02 a.linkedin:hover, .social-icon-style-04 a.linkedin:hover, .social-icon-style-07 a.linkedin, .social-icon-style-08 a.linkedin, .social-icon-style-10 a.linkedin, .social-icon-style-11 a.linkedin .social-front i, .menu-sidebar a .linkedin { color: #0077b5; }
.social-icon-style-02 a.instagram:hover, .social-icon-style-04 a.instagram:hover, .social-icon-style-07 a.instagram, .social-icon-style-08 a.instagram, .social-icon-style-10 a.instagram, .social-icon-style-11 a.instagram .social-front i, .menu-sidebar a .instagram { color: #FE1F49; }
.social-icon-style-02 a.pinterest:hover, .social-icon-style-04 a.pinterest:hover, .social-icon-style-07 a.pinterest, .social-icon-style-08 a.pinterest, .social-icon-style-10 a.pinterest, .social-icon-style-11 a.pinterest .social-front i, .menu-sidebar a .pinterest { color: #CB2027; }
.social-icon-style-02 a.flickr:hover, .social-icon-style-04 a.flickr:hover, .social-icon-style-07 a.flickr, .social-icon-style-08 a.flickr, .social-icon-style-10 a.flickr, .social-icon-style-11 a.flickr .social-front i, .menu-sidebar a .flickr { color: #ff0084; }
.social-icon-style-02 a.rss:hover, .social-icon-style-04 a.rss:hover, .social-icon-style-07 a.rss, .social-icon-style-08 a.rss, .social-icon-style-10 a.rss, .social-icon-style-11 a.rss .social-front i, .menu-sidebar a .rss { color: #f2890a; }
.social-icon-style-02 a.behance:hover, .social-icon-style-04 a.behance:hover, .social-icon-style-07 a.behance, .social-icon-style-08 a.behance, .social-icon-style-10 a.behance, .social-icon-style-11 a.behance .social-front i, .menu-sidebar a .behance { color: #1769ff; }
.social-icon-style-02 a.vine:hover, .social-icon-style-04 a.vine:hover, .social-icon-style-07 a.vine, .social-icon-style-08 a.vine, .social-icon-style-10 a.vine, .social-icon-style-11 a.vine .social-front i, .menu-sidebar a .vine { color: #00bf8f; }
.social-icon-style-02 a.email:hover, .social-icon-style-04 a.email:hover, .social-icon-style-07 a.email, .social-icon-style-08 a.email, .social-icon-style-10 a.email, .social-icon-style-11 a.email .social-front i, .menu-sidebar a .email { color: #7d7d7d; }
.social-icon-style-02 a.sms:hover, .social-icon-style-04 a.sms:hover, .social-icon-style-07 a.sms, .social-icon-style-08 a.sms, .social-icon-style-10 a.sms, .social-icon-style-11 a.sms .social-front i, .menu-sidebar a .sms { color: #ffbd00; }
.social-icon-style-02 a.sharethis:hover, .social-icon-style-04 a.sharethis:hover, .social-icon-style-07 a.sharethis, .social-icon-style-08 a.sharethis, .social-icon-style-10 a.sharethis, .social-icon-style-11 a.sharethis .social-front i, .menu-sidebar a .sharethis { color: #95D03A; }
.social-icon-style-02 a.reddit:hover, .social-icon-style-04 a.reddit:hover, .social-icon-style-07 a.reddit, .social-icon-style-08 a.reddit, .social-icon-style-10 a.reddit, .social-icon-style-11 a.reddit .social-front i, .menu-sidebar a .reddit { color: #ff4500; }
.social-icon-style-02 a.tumblr:hover, .social-icon-style-04 a.tumblr:hover, .social-icon-style-07 a.tumblr, .social-icon-style-08 a.tumblr, .social-icon-style-10 a.tumblr, .social-icon-style-11 a.tumblr .social-front i, .menu-sidebar a .tumblr { color: #32506d; }
.social-icon-style-02 a.digg:hover, .social-icon-style-04 a.digg:hover, .social-icon-style-07 a.digg, .social-icon-style-08 a.digg, .social-icon-style-10 a.digg, .social-icon-style-11 a.digg .social-front i, .menu-sidebar a .digg { color: #262626; }
.social-icon-style-02 a.stumbleupon:hover, .social-icon-style-04 a.stumbleupon:hover, .social-icon-style-07 a.stumbleupon, .social-icon-style-08 a.stumbleupon, .social-icon-style-10 a.stumbleupon, .social-icon-style-11 a.stumbleupon .social-front i, .menu-sidebar a .stumbleupon { color: #eb4924; }
.social-icon-style-02 a.whatsapp:hover, .social-icon-style-04 a.whatsapp:hover, .social-icon-style-07 a.whatsapp, .social-icon-style-08 a.whatsapp, .social-icon-style-10 a.whatsapp, .social-icon-style-11 a.whatsapp .social-front i, .menu-sidebar a .whatsapp { color: #25d366; }
.social-icon-style-02 a.vk:hover, .social-icon-style-04 a.vk:hover, .social-icon-style-07 a.vk, .social-icon-style-08 a.vk, .social-icon-style-10 a.vk, .social-icon-style-11 a.vk .social-front i, .menu-sidebar a .vk { color: #4c6c91; }
.social-icon-style-02 a.weibo:hover, .social-icon-style-04 a.weibo:hover, .social-icon-style-07 a.weibo, .social-icon-style-08 a.weibo, .social-icon-style-10 a.weibo, .social-icon-style-11 a.weibo .social-front i, .menu-sidebar a .weibo { color: #ff9933; }
.social-icon-style-02 a.odnoklassniki:hover, .social-icon-style-04 a.odnoklassniki:hover, .social-icon-style-07 a.odnoklassniki, .social-icon-style-08 a.odnoklassniki, .social-icon-style-10 a.odnoklassniki, .social-icon-style-11 a.odnoklassniki .social-front i, .menu-sidebar a .odnoklassniki { color: #d7772d; }
.social-icon-style-02 a.xing:hover, .social-icon-style-04 a.xing:hover, .social-icon-style-07 a.xing, .social-icon-style-08 a.xing, .social-icon-style-10 a.xing, .social-icon-style-11 a.xing .social-front i, .menu-sidebar a .xing { color: #1a7576; }
.social-icon-style-02 a.print:hover, .social-icon-style-04 a.print:hover, .social-icon-style-07 a.print, .social-icon-style-08 a.print, .social-icon-style-10 a.print, .social-icon-style-11 a.print .social-front i, .menu-sidebar a .print { color: #222222; }
.social-icon-style-02 a.blogger:hover, .social-icon-style-04 a.blogger:hover, .social-icon-style-07 a.blogger, .social-icon-style-08 a.blogger, .social-icon-style-10 a.blogger, .social-icon-style-11 a.blogger .social-front i, .menu-sidebar a .blogger { color: #ff8000; }
.social-icon-style-02 a.flipboard:hover, .social-icon-style-04 a.flipboard:hover, .social-icon-style-07 a.flipboard, .social-icon-style-08 a.flipboard, .social-icon-style-10 a.flipboard, .social-icon-style-11 a.flipboard .social-front i, .menu-sidebar a .flipboard { color: #e12828; }
.social-icon-style-02 a.meneame:hover, .social-icon-style-04 a.meneame:hover, .social-icon-style-07 a.meneame, .social-icon-style-08 a.meneame, .social-icon-style-10 a.meneame, .social-icon-style-11 a.meneame .social-front i, .menu-sidebar a .meneame { color: #ff6400; }
.social-icon-style-02 a.mailru:hover, .social-icon-style-04 a.mailru:hover, .social-icon-style-07 a.mailru, .social-icon-style-08 a.mailru, .social-icon-style-10 a.mailru, .social-icon-style-11 a.mailru .social-front i, .menu-sidebar a .mailru { color: #168de2; }
.social-icon-style-02 a.delicious:hover, .social-icon-style-04 a.delicious:hover, .social-icon-style-07 a.delicious, .social-icon-style-08 a.delicious, .social-icon-style-10 a.delicious, .social-icon-style-11 a.delicious .social-front i, .menu-sidebar a .delicious { color: #205cc0; }
.social-icon-style-02 a.livejournal:hover, .social-icon-style-04 a.livejournal:hover, .social-icon-style-07 a.livejournal, .social-icon-style-08 a.livejournal, .social-icon-style-10 a.livejournal, .social-icon-style-11 a.livejournal .social-front i, .menu-sidebar a .livejournal { color: #00b0ea; }

/* social icon style 03 */
.social-icon-style-03 a { text-align: center; display:inline-block; transition: all 400ms; -webkit-transition: all 400ms; -moz-transition: all 400ms; -ms-transition: all 400ms; -o-transition: all 400ms; color: #fff; }
.social-icon-style-03 a:hover { border-radius: 100%; -webkit-transform: scale(0.9); -moz-transform: scale(0.9); -ms-transform: scale(0.9); -o-transform: scale(0.9); transform: scale(0.9); }
.social-icon-style-03 a.facebook,.social-icon-style-05 a.facebook span,.social-icon-style-04 a.facebook,.social-icon-style-09 a.facebook:hover, .social-icon-style-10 a.facebook span, .social-icon-style-11 a.facebook .social-back { background-color: #3b5998; }
.social-icon-style-03 a.twitter,.social-icon-style-05 a.twitter span,.social-icon-style-04 a.twitter,.social-icon-style-09 a.twitter:hover, .social-icon-style-10 a.twitter span, .social-icon-style-11 a.twitter .social-back { background-color: #00aced; }
.social-icon-style-03 a.google,.social-icon-style-05 a.google span,.social-icon-style-04 a.google,.social-icon-style-09 a.google:hover, .social-icon-style-10 a.google span, .social-icon-style-11 a.google .social-back { background-color: #dc4a38; }
.social-icon-style-03 a.dribbble,.social-icon-style-05 a.dribbble span,.social-icon-style-04 a.dribbble,.social-icon-style-09 a.dribbble:hover, .social-icon-style-10 a.dribbble span, .social-icon-style-11 a.dribbble .social-back { background-color: #ea4c89; }
.social-icon-style-03 a.youtube,.social-icon-style-05 a.youtube span,.social-icon-style-04 a.youtube,.social-icon-style-09 a.youtube:hover, .social-icon-style-10 a.youtube span, .social-icon-style-11 a.youtube .social-back { background-color: #bb0000; }
.social-icon-style-03 a.linkedin,.social-icon-style-05 a.linkedin span,.social-icon-style-04 a.linkedin,.social-icon-style-09 a.linkedin:hover, .social-icon-style-10 a.linkedin span, .social-icon-style-11 a.linkedin .social-back { background-color: #007bb6; }
.social-icon-style-03 a.instagram,.social-icon-style-05 a.instagram span,.social-icon-style-04 a.instagram,.social-icon-style-09 a.instagram:hover, .social-icon-style-10 a.instagram span, .social-icon-style-11 a.instagram .social-back { background-color: #FE1F49; }
.social-icon-style-03 a.pinterest,.social-icon-style-05 a.pinterest span,.social-icon-style-04 a.pinterest,.social-icon-style-09 a.pinterest:hover, .social-icon-style-10 a.pinterest span, .social-icon-style-11 a.pinterest .social-back { background-color: #CB2027; }
.social-icon-style-03 a.flickr,.social-icon-style-05 a.flickr span,.social-icon-style-04 a.flickr,.social-icon-style-09 a.flickr:hover, .social-icon-style-10 a.flickr span, .social-icon-style-11 a.flickr .social-back { background-color: #ff0084; }
.social-icon-style-03 a.rss,.social-icon-style-05 a.rss span,.social-icon-style-04 a.rss,.social-icon-style-09 a.rss:hover, .social-icon-style-10 a.rss span, .social-icon-style-11 a.rss .social-back { background-color: #f2890a; }
.social-icon-style-03 a.behance,.social-icon-style-05 a.behance span,.social-icon-style-04 a.behance,.social-icon-style-09 a.behance:hover, .social-icon-style-10 a.behance span, .social-icon-style-11 a.behance .social-back { background-color: #1769ff; }
.social-icon-style-03 a.vine,.social-icon-style-05 a.vine span,.social-icon-style-04 a.vine,.social-icon-style-09 a.vine:hover, .social-icon-style-10 a.vine span, .social-icon-style-11 a.vine .social-back { background-color: #00bf8f; }
.social-icon-style-03 a.email,.social-icon-style-05 a.email span,.social-icon-style-04 a.email,.social-icon-style-09 a.email:hover, .social-icon-style-10 a.email span, .social-icon-style-11 a.email .social-back { background-color: #7d7d7d; }
.social-icon-style-03 a.sms,.social-icon-style-05 a.sms span,.social-icon-style-04 a.sms,.social-icon-style-09 a.sms:hover, .social-icon-style-10 a.sms span, .social-icon-style-11 a.sms .social-back { background-color: #ffbd00; }
.social-icon-style-03 a.sharethis,.social-icon-style-05 a.sharethis span,.social-icon-style-04 a.sharethis,.social-icon-style-09 a.sharethis:hover, .social-icon-style-10 a.sharethis span, .social-icon-style-11 a.sharethis .social-back { background-color: #95D03A; }
.social-icon-style-03 a.reddit,.social-icon-style-05 a.reddit span,.social-icon-style-04 a.reddit,.social-icon-style-09 a.reddit:hover, .social-icon-style-10 a.reddit span, .social-icon-style-11 a.reddit .social-back { background-color: #ff4500; }
.social-icon-style-03 a.tumblr,.social-icon-style-05 a.tumblr span,.social-icon-style-04 a.tumblr,.social-icon-style-09 a.tumblr:hover, .social-icon-style-10 a.tumblr span, .social-icon-style-11 a.tumblr .social-back { background-color: #32506d; }
.social-icon-style-03 a.digg,.social-icon-style-05 a.digg span,.social-icon-style-04 a.digg,.social-icon-style-09 a.digg:hover, .social-icon-style-10 a.digg span, .social-icon-style-11 a.digg .social-back { background-color: #262626; }
.social-icon-style-03 a.stumbleupon,.social-icon-style-05 a.stumbleupon span,.social-icon-style-04 a.stumbleupon,.social-icon-style-09 a.stumbleupon:hover, .social-icon-style-10 a.stumbleupon span, .social-icon-style-11 a.stumbleupon .social-back { background-color: #eb4924; }
.social-icon-style-03 a.whatsapp,.social-icon-style-05 a.whatsapp span,.social-icon-style-04 a.whatsapp,.social-icon-style-09 a.whatsapp:hover, .social-icon-style-10 a.whatsapp span, .social-icon-style-11 a.whatsapp .social-back { background-color: #25d366; }
.social-icon-style-03 a.vk,.social-icon-style-05 a.vk span,.social-icon-style-04 a.vk,.social-icon-style-09 a.vk:hover, .social-icon-style-10 a.vk span, .social-icon-style-11 a.vk .social-back { background-color: #4c6c91; }
.social-icon-style-03 a.weibo,.social-icon-style-05 a.weibo span,.social-icon-style-04 a.weibo,.social-icon-style-09 a.weibo:hover, .social-icon-style-10 a.weibo span, .social-icon-style-11 a.weibo .social-back { background-color: #ff9933; }
.social-icon-style-03 a.odnoklassniki,.social-icon-style-05 a.odnoklassniki span,.social-icon-style-04 a.odnoklassniki,.social-icon-style-09 a.odnoklassniki:hover, .social-icon-style-10 a.odnoklassniki span, .social-icon-style-11 a.odnoklassniki .social-back { background-color: #d7772d; }
.social-icon-style-03 a.xing,.social-icon-style-05 a.xing span,.social-icon-style-04 a.xing,.social-icon-style-09 a.xing:hover, .social-icon-style-10 a.xing span, .social-icon-style-11 a.xing .social-back { background-color: #1a7576; }
.social-icon-style-03 a.print,.social-icon-style-05 a.print span,.social-icon-style-04 a.print,.social-icon-style-09 a.print:hover, .social-icon-style-10 a.print span, .social-icon-style-11 a.print .social-back { background-color: #222222; }
.social-icon-style-03 a.blogger,.social-icon-style-05 a.blogger span,.social-icon-style-04 a.blogger,.social-icon-style-09 a.blogger:hover, .social-icon-style-10 a.blogger span, .social-icon-style-11 a.blogger .social-back { background-color: #ff8000; }
.social-icon-style-03 a.flipboard,.social-icon-style-05 a.flipboard span,.social-icon-style-04 a.flipboard,.social-icon-style-09 a.flipboard:hover, .social-icon-style-10 a.flipboard span, .social-icon-style-11 a.flipboard .social-back { background-color: #e12828; }
.social-icon-style-03 a.meneame,.social-icon-style-05 a.meneame span,.social-icon-style-04 a.meneame,.social-icon-style-09 a.meneame:hover, .social-icon-style-10 a.meneame span, .social-icon-style-11 a.meneame .social-back { background-color: #ff6400; }
.social-icon-style-03 a.mailru,.social-icon-style-05 a.mailru span,.social-icon-style-04 a.mailru,.social-icon-style-09 a.mailru:hover, .social-icon-style-10 a.mailru span, .social-icon-style-11 a.mailru .social-back { background-color: #168de2; }
.social-icon-style-03 a.delicious,.social-icon-style-05 a.delicious span,.social-icon-style-04 a.delicious,.social-icon-style-09 a.delicious:hover, .social-icon-style-10 a.delicious span, .social-icon-style-11 a.delicious .social-back { background-color: #205cc0; }
.social-icon-style-03 a.livejournal,.social-icon-style-05 a.livejournal span,.social-icon-style-04 a.livejournal,.social-icon-style-09 a.livejournal:hover, .social-icon-style-10 a.livejournal span, .social-icon-style-11 a.livejournal .social-back { background-color: #00b0ea; }

/* social icon style 04 */
.social-icon-style-04 a { text-align: center; display:inline-block; transition: all 400ms; -webkit-transition: all 400ms; -moz-transition: all 400ms; -ms-transition: all 400ms; -o-transition: all 400ms; border-radius: 100%; color: #fff; }
.social-icon-style-04 a:hover { background: #fff; box-shadow: 0 0 30px rgba(23,23,23,.25); -moz-transform: translate3d(0, -3px, 0); -o-transform: translate3d(0, -3px, 0); -ms-transform: translate3d(0, -3px, 0); -webkit-transform: translate3d(0, -3px, 0); transform: translate3d(0, -3px, 0); }

/* social icon style 05 */
.social-icon-style-05 a { text-align: center; display:inline-block; border-radius: 100%; position: relative; color: #232323; text-align: center; z-index: 1; }
.social-icon-style-05 a:hover { color: #fff; }
.social-icon-style-05 a span { border-radius: 0; display: block; height: 0; left: 50%; margin: 0; position: absolute; top: 50%; transition: all 0.3s; -webkit-transition: all 0.3s; -moz-transition: all 0.3s; -ms-transition: all 0.3s; -o-transition: all 0.3s; width: 0;z-index: -1; }
.social-icon-style-05 a:hover span { width:100%; height:100%; border-radius: 100%; top: 0; left: 0; }

/* social icon style 06 */
.social-icon-style-06 a { text-align: center; display:inline-block; color: #232323; }
.social-icon-style-06 a:hover { color: #fff; border-radius: 100%; background-color: #0038e3; }

/* social icon style 07 */
.social-icon-style-07 { position: relative; }
.social-icon-style-07 a { text-align: center; border-radius: 100%; position: relative; display: inline-block; }
.social-icon-style-07 a:hover { color: #fff; }
.social-icon-style-07.dark a:hover, .social-icon-style-07 .dark a:hover { color: #000; }

/* social icon style 08 */
.social-icon-style-08 a { color: #fff; display: inline-block; height: auto; width: auto; padding: 0 24px; position: relative; overflow: hidden; }
.social-icon-style-08 a .brand-icon { position: absolute; height: 100%; width: 100%; left: 0; top: 100%; display: flex; justify-content: center; align-items: center; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }
.social-icon-style-08 a:hover .brand-icon { top: 0; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }
.social-icon-style-08 a > span {line-height: inherit; font-weight: 500; top: 0px; position: relative; -webkit-transition: 350ms cubic-bezier(0.175, 0.885, 0.32, 1.275) all; -moz-transition: 350ms cubic-bezier(0.175, 0.885, 0.32, 1.275) all; -ms-transition: 350ms cubic-bezier(0.175, 0.885, 0.32, 1.275) all; -o-transition: 350ms cubic-bezier(0.175, 0.885, 0.32, 1.275) all; transition: 350ms cubic-bezier(0.175, 0.885, 0.32, 1.275) all; }
.social-icon-style-08 a:hover > span.brand-label { top: -50px; -webkit-transition: 350ms cubic-bezier(0.175, 0.885, 0.32, 1.275) all; -moz-transition: 350ms cubic-bezier(0.175, 0.885, 0.32, 1.275) all; -ms-transition: 350ms cubic-bezier(0.175, 0.885, 0.32, 1.275) all; -o-transition: 350ms cubic-bezier(0.175, 0.885, 0.32, 1.275) all; transition: 350ms cubic-bezier(0.175, 0.885, 0.32, 1.275) all; }

/* social icon style 09 */
.social-icon-style-09 a { text-align: center; display:inline-block; border-radius: 100%; color: #232323; position: relative; text-align: center; }
.social-icon-style-09 a:hover { color: #fff;  }
.social-icon-style-09 a:after { content: ''; display: block; position: absolute; top: 0; left: 0; height: 100%; width: 100%; -webkit-transform: scale(0.8); -moz-transform: scale(0.8); -ms-transform: scale(0.8); -o-transform: scale(0.8); transform: scale(0.8); border: 1px solid; border-radius: 100%; opacity: 0; -webkit-transition: all .5s cubic-bezier(0,0,.2,1); transition: all .5s cubic-bezier(0,0,.2,1); }
.social-icon-style-09 a:hover:after { opacity: .4; -webkit-transform: scale(1.3); -moz-transform: scale(1.3); -ms-transform: scale(1.3); -o-transform: scale(1.3); transform: scale(1.3);}

.social-icon-style-09 a.facebook:after { border-color: #3b5998; color: #3b5998; }
.social-icon-style-09 a.twitter:after { border-color: #00aced; color: #00aced; }
.social-icon-style-09 a.google:after { border-color: #dc4a38; color: #dc4a38; }
.social-icon-style-09 a.dribbble:after { border-color: #ea4c89; color: #ea4c89; }
.social-icon-style-09 a.youtube:after { border-color: #bb0000; color: #bb0000; }
.social-icon-style-09 a.linkedin:after { border-color: #0077b5; color: #0077b5; }
.social-icon-style-09 a.instagram:after { border-color: #FE1F49; color: #FE1F49; }
.social-icon-style-09 a.pinterest:after { border-color: #CB2027; color: #CB2027; }
.social-icon-style-09 a.flickr:after { border-color: #ff0084; color: #ff0084; }
.social-icon-style-09 a.rss:after { border-color: #f2890a; color: #f2890a; }
.social-icon-style-09 a.behance:after { border-color: #1769ff; color: #1769ff; }
.social-icon-style-09 a.vine:after { border-color: #00bf8f; color: #00bf8f; }
.social-icon-style-09 a.email:after { border-color: #7d7d7d; color: #7d7d7d; }
.social-icon-style-09 a.sms:after { border-color: #ffbd00; color: #ffbd00; }
.social-icon-style-09 a.sharethis:after { border-color: #95D03A; color: #95D03A; }
.social-icon-style-09 a.reddit:after { border-color: #ff4500; color: #ff4500; }
.social-icon-style-09 a.tumblr:after { border-color: #32506d; color: #32506d; }
.social-icon-style-09 a.digg:after { border-color: #262626; color: #262626; }
.social-icon-style-09 a.stumbleupon:after { border-color: #eb4924; color: #eb4924; }
.social-icon-style-09 a.whatsapp:after { border-color: #25d366; color: #25d366; }
.social-icon-style-09 a.vk:after { border-color: #4c6c91; color: #4c6c91; }
.social-icon-style-09 a.weibo:after { border-color: #ff9933; color: #ff9933; }
.social-icon-style-09 a.odnoklassniki:after { border-color: #d7772d; color: #d7772d; }
.social-icon-style-09 a.xing:after { border-color: #1a7576; color: #1a7576; }
.social-icon-style-09 a.print:after { border-color: #222222; color: #222222; }
.social-icon-style-09 a.blogger:after { border-color: #ff8000; color: #ff8000; }
.social-icon-style-09 a.flipboard:after { border-color: #e12828; color: #e12828; }
.social-icon-style-09 a.meneame:after { border-color: #ff6400; color: #ff6400; }
.social-icon-style-09 a.mailru:after { border-color: #168de2; color: #168de2; }
.social-icon-style-09 a.delicious:after { border-color: #205cc0; color: #205cc0; }
.social-icon-style-09 a.livejournal:after { border-color: #00b0ea; color: #00b0ea; }

/* social icon style 10 */
.social-icon-style-10 a { display: inline-block; position: relative; z-index: 3; overflow: hidden; text-align: center; }
.social-icon-style-10 a > span { position: absolute; top: -100%; left: 0; height: 100%; width: 100%; z-index: -1; -webkit-transition: all .5s cubic-bezier(0,0,.2,1); transition: all .5s cubic-bezier(0,0,.2,1); }
.social-icon-style-10 a:hover > span { top: 0; z-index: -1; }
.social-icon-style-10 a:hover { color: #fff; }

/* social icon style 11 */
.social-icon-style-11 a { display: inline-block; vertical-align: middle; height: 160px; width: 160px; position: relative; margin: 0 15px; }
.social-icon-style-11 a .social-front, .social-icon-style-11 a .social-back { height: 140px; width: 140px; padding: 30px; position: absolute; top: 30px; left: 30px; }
.social-icon-style-11 a .social-front i { display: block; margin:0 0 20px 0; font-size: 28px; }
.social-icon-style-11 a .social-front .social-count { display: block;}
.social-icon-style-11 a .social-front { background-color: #fff; text-align: center; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s;transition-duration: 0.3s; -webkit-box-shadow: 0 0 35px rgba(23,23,23,0.06); -moz-box-shadow: 0 0 35px rgba(23,23,23,0.06); box-shadow: 0 0 35px rgba(23,23,23,0.06); display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; -webkit-box-orient: vertical; -webkit-box-direction: normal; -ms-flex-direction: column; flex-direction: column;  }
.social-icon-style-11 a .social-back { background-color: #ddd; position: relative; }
.social-icon-style-11 a .social-back span { position: absolute; bottom: 3px; right: 30px; text-transform: uppercase; color: #fff; font-size: 11px; }
.social-icon-style-11 a.move-up .social-back span, .social-icon-style-11.small-box a.move-up .social-back span { width: 100%; right: 0; text-align: center; }
.social-icon-style-11 a.move-right .social-back span { left: 30px; right: auto; }
.social-icon-style-11.small-box a { height: 100px; width: 100px; }
.social-icon-style-11.small-box a .social-front, .social-icon-style-11.small-box a .social-back { height: 85px; width: 85px; }
.social-icon-style-11.small-box a .social-front i { margin: 6px 0; font-size: 22px; }
.social-icon-style-11.small-box a .social-front, .social-icon-style-11.small-box a .social-back { padding: 15px; top: 18px; left: 18px; }
.social-icon-style-11.small-box a .social-back span { bottom: 2px; right: 5px; line-height: normal; font-size: 10px; }
.social-icon-style-11.small-box a.move-right .social-back span { left: 18px; right: auto; }
.social-icon-style-11 a:hover .social-front { top: 0; left: 0; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s;transition-duration: 0.3s; }
.social-icon-style-11 a.move-up:hover .social-front { top: 0; left: 30px; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s;transition-duration: 0.3s; }
.social-icon-style-11.small-box a.move-up:hover .social-front { top: 0; left: 18px; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s;transition-duration: 0.3s; }
.social-icon-style-11 a.move-right:hover .social-front { top: 0; left: 60px; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s;transition-duration: 0.3s; }

/* social icon style 12 */
.social-icon-style-12 li a { color: #232323; display: inline-block; }
.social-icon-style-12 .light li a { color: #fff; display: inline-block; }
.social-icon-style-12 li a:hover { opacity: .5; }

/* icon box */
.icon-box i { margin:25px; }
.icon-box-circled { border-radius: 50%; }
.icon-box-large { width:100px; height:100px; line-height:98px; text-align:center; font-size:40px; transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out; }
.icon-box-medium { width:80px; height:80px; line-height:80px; text-align:center; font-size:30px; transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out; }
.icon-box-small { width:60px; height:60px; line-height:60px; text-align:center; font-size:22px; }
.icon-box-extra-small { width:40px; height:40px; line-height:40px; text-align:center; font-size:15px; }
.icon-box-very-small { width:30px; height:30px; line-height:28px; text-align:center; font-size:18px; }

/* ===================================
    17. Magnific popup
====================================== */

.mfp-bg { background: #232323; opacity: 0.9; }
.mfp-bg, .mfp-wrap { z-index: 10007; }
#popup-form { cursor:default; }
.mfp-image-holder .mfp-close, .mfp-iframe-holder .mfp-close { color:#fff; background: transparent; }
.mfp-close,.mfp-close:active {opacity: 1; background-color:transparent; right: 30px; top: 30px;}
.modal-popup-main .mfp-close, .modal-popup-main .mfp-close:active { right: 0; top: 0;}
.mfp-close-btn-in .modal-popup-main .mfp-close { position: absolute; }
.mfp-removing .mfp-close { display: none;}

.subscribe-popup .modal-popup-main .mfp-close, .subscribe-popup .modal-popup-main .mfp-close:active { position: absolute; color: #fff}

.lightbox-gallery a{position: relative; display:block}
.lightbox-gallery .gallary-over-layer{position: absolute; top: 0; left:0;}

.mfp-arrow-right:after, .mfp-arrow-left:after { display: none; }
.mfp-arrow-right:before { border-left: none; content: "\e628"; font-size: 18px; font-family: 'themify'; color: #fff; line-height: 60px; background: #232323; width: 100%; height: 100%; border-radius: 100%; border: none}
.mfp-arrow-left:before { border-right: none; content: "\e629"; font-size: 18px; font-family: 'themify'; color: #fff; line-height: 60px; background: #232323; width: 100%; height: 100%; border-radius: 100%; border: none}
.mfp-bottom-bar { margin-top: -24px; font-size: 12px }
.mfp-close { font-size: 30px; font-family: 'Poppins', sans-serif; font-weight: 300; }
.mfp-figure { -webkit-animation-name: fadeIn; -moz-animation-name: fadeIn; -o-animation-name: fadeIn; -ms-animation-name: fadeIn; animation-name: fadeIn; -webkit-animation-duration: .4s; -moz-animation-duration: .4s; -ms-animation-duration: .4s; -o-animation-duration: .4s; animation-duration: .4s; -webkit-animation-fill-mode: both; -moz-animation-fill-mode: both; -ms-animation-fill-mode: both; -o-animation-fill-mode: both; animation-fill-mode: both; webkit-backface-visibility: hidden; -moz-backface-visibility:hidden; -ms-backface-visibility:hidden; }
.mfp-ajax-holder { width: 70%; height: calc(100% - 100px); margin: 0 auto; position: relative; top: 50%; transform: translateY(-50%); -webkit-transform: translateY(-50%); -moz-transform: translateY(-50%); -ms-transform: translateY(-50%); }
button.mfp-close,.mfp-image-holder button.mfp-close, .mfp-iframe-holder button.mfp-close { position: fixed; top: 30px; right: 30px; }

.my-mfp-zoom-in .zoom-anim-dialog { opacity: 0; -webkit-transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; transition: all 0.3s ease-in-out; -webkit-transform: scale(0.8); -moz-transform: scale(0.8); -ms-transform: scale(0.8); -o-transform: scale(0.8); transform: scale(0.8); }
.my-mfp-zoom-in.mfp-ready .zoom-anim-dialog { opacity: 1; -webkit-transform: scale(1); -moz-transform: scale(1); -ms-transform: scale(1); -o-transform: scale(1); transform: scale(1); }
.my-mfp-zoom-in.mfp-removing .zoom-anim-dialog { -webkit-transform: scale(0.8); -moz-transform: scale(0.8); -ms-transform: scale(0.8); -o-transform: scale(0.8); transform: scale(0.8); opacity: 0; }

.my-mfp-zoom-in.mfp-bg { opacity: 0; -webkit-transition: opacity 0.3s ease-out; -moz-transition: opacity 0.3s ease-out; -o-transition: opacity 0.3s ease-out; -ms-transition: opacity 0.3s ease-out; transition: opacity 0.3s ease-out; }
.my-mfp-zoom-in.mfp-ready.mfp-bg { opacity: 0.93; }
.my-mfp-zoom-in.mfp-removing.mfp-bg { opacity: 0; }

.my-mfp-slide-bottom .zoom-anim-dialog { opacity: 0; -webkit-transition: all 0.3s ease-out; -moz-transition: all 0.3s ease-out; -o-transition: all 0.3s ease-out; transition: all 0.3s ease-out; -webkit-transform: translateY(-20px) perspective(600px) rotateX(10deg); -moz-transform: translateY(-20px) perspective(600px) rotateX(10deg); -ms-transform: translateY(-20px) perspective(600px) rotateX(10deg); -o-transform: translateY(-20px) perspective(600px) rotateX(10deg); -ms-transform: translateY(-20px) perspective(600px) rotateX(10deg); transform: translateY(-20px) perspective(600px) rotateX(10deg); }
.my-mfp-slide-bottom.mfp-ready .zoom-anim-dialog { opacity: 1; -webkit-transform: translateY(0) perspective(600px) rotateX(0); -moz-transform: translateY(0) perspective(600px) rotateX(0); -ms-transform: translateY(0) perspective(600px) rotateX(0); -o-transform: translateY(0) perspective(600px) rotateX(0); transform: translateY(0) perspective(600px) rotateX(0); }
.my-mfp-slide-bottom.mfp-removing .zoom-anim-dialog { opacity: 0; -webkit-transform: translateY(-10px) perspective(600px) rotateX(10deg); -moz-transform: translateY(-10px) perspective(600px) rotateX(10deg); -ms-transform: translateY(-10px) perspective(600px) rotateX(10deg); -o-transform: translateY(-10px) perspective(600px) rotateX(10deg); transform: translateY(-10px) perspective(600px) rotateX(10deg); }

.my-mfp-slide-bottom.mfp-bg { opacity: 0;-webkit-transition: opacity 0.3s ease-out; -moz-transition: opacity 0.3s ease-out; -o-transition: opacity 0.3s ease-out; -ms-transition: opacity 0.3s ease-out; transition: opacity 0.3s ease-out; }
.my-mfp-slide-bottom.mfp-ready.mfp-bg { opacity: 0.93; }
.my-mfp-slide-bottom.mfp-removing.mfp-bg { opacity: 0; }

.mfp-fade.mfp-bg { opacity: 0; -webkit-transition: all 0.3s ease-out; -moz-transition: all 0.3s ease-out; -ms-transition: all 0.3s ease-out; -o-transition: all 0.3s ease-out; transition: all 0.3s ease-out; }
.mfp-fade.mfp-bg.mfp-ready { opacity: 0.93; }
.mfp-fade.mfp-bg.mfp-removing { opacity: 0; }
.mfp-fade.mfp-wrap .mfp-content { opacity: 0; -webkit-transition: all 0.3s ease-out; -moz-transition: all 0.3s ease-out; -ms-transition: all 0.3s ease-out; transition: all 0.3s ease-out; }
.mfp-fade.mfp-wrap.mfp-ready .mfp-content { opacity: 1; }
.mfp-fade.mfp-wrap.mfp-removing .mfp-content { opacity: 0; }
.mfp-iframe-holder .mfp-content { max-width: 1020px; }

/* notify popup */
.show-notify-popup > section {filter: grayscale(100%);}
.show-notify-popup .mfp-notify.mfp-bg.mfp-ready { opacity: 0.8; background-color: #000000;}
.show-notify-popup .mfp-close, .show-notify-popup .mfp-close:active { color: #fff; }
.show-notify-popup .notify-form .notify-input { border-bottom: 1px solid rgba(0, 0, 0, 0.1); }
.show-notify-popup .mfp-container { position: fixed; padding-top: 100px; height: auto; background-color: #fff; animation-name: comingsoonpopup; -webkit-animation-name: comingsoonpopup; -webkit-animation-duration: 0.2s; -moz-animation-duration: 0.2s; -o-animation-duration: 0.2s; animation-duration: 0.2s; }
.show-notify-popup .mfp-removing .mfp-container { animation-name: comingsoonpopupclose; -webkit-animation-name: comingsoonpopupclose; -webkit-animation-duration: 0.2s; -moz-animation-duration: 0.2s; -o-animation-duration: 0.2s; animation-duration: 0.2s; }
.mfp-notify .mfp-container { top: auto; bottom: 0;}

/* ===================================
    18. Header
====================================== */

.navbar { padding: 0; }
.navbar.navbar-boxed { padding-left: 35px; padding-right: 35px; }
.navbar-brand { margin-right: 0; font-size: 0; }
.dropdown.open>.dropdown-menu { display: block; }
.header-social-icon a > i { padding: 0px 10px; font-size: 14px; vertical-align: middle; }
.header-cart-icon a > i, .header-cart-icon a > i, .header-language a > i, .header-search-icon a > i, .header-push-button a > i { padding: 30px 0; display: inline-block; }
.top-bar .header-cart-icon a > i, .top-bar .header-cart-icon a > i, .top-bar .header-language a > i, .top-bar .header-search-icon a > i, .top-bar .header-push-button a > i { padding: 14px 0; display: inline-block;}
.header-social-icon {line-height: 12px; padding-left: 10px; }
.navbar.navbar-dark .header-social-icon, .sticky .navbar.header-dark .header-social-icon { border-color: rgba(255,255,255,.45); }
.sticky .navbar.header-light .header-social-icon { border-color: rgba(0,0,0,.22); }
.header-search-icon, .header-cart-icon, .header-language, .header-push-button, .header-button { display: inline-block; vertical-align: middle; padding-left:17px; font-size: 17px; }
.header-cart-icon { margin-right: 10px; }
.nav-item > .dropdown-toggle { display: none; }
.dropdown-toggle:after {visibility: hidden; width: 0; height: 0;}
.mCustomScrollbar .mCSB_container { padding-right: 20px !important; margin-right: 0; }
.mCustomScrollbar.mCS_no_scrollbar .mCSB_container, .mCSB_container.mCS_no_scrollbar_y.mCS_y_hidden { padding-right: 0 !important; }

/* menu label */
.menu-lable { color: #fff; font-size: 10px; margin-left: 8px; padding: 1px 7px 1px 6px; line-height: 18px; text-transform: uppercase; vertical-align: middle; }
.menu-lable.new {background-color: #27ae60; }
.menu-lable.hot {background-color: #d74a3a; }

/* header static overlay */
header nav.navbar.navbar-static-overlay { position: absolute; width: 100%; }

/* center logo */
.menu-logo-center .menu-logo { position: absolute; left: 0; right: 0; margin: 0 auto; text-align: center; }
.navbar-left, .navbar-right { width: 40%; }
.menu-logo-center .navbar-brand { position: relative; z-index: 1; padding: 22px 0;}

/* header with topbar */
header .top-bar { position: fixed; top: 0; width: 100%; z-index: 1031; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }
header .top-bar + .navbar.fixed-top { top: 48px; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }
header.sticky .top-bar + .navbar.fixed-top { top: 0px; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }

/* header with top logo */
.top-logo .header-cart-icon a > i, .top-logo .header-cart-icon a > i, .top-logo .header-language a > i, .top-logo .header-search-icon a > i, .top-logo .header-push-button a > i { padding: 26px 0; }

/* header responsive sticky */
header.sticky .navbar.responsive-sticky .navbar-brand img { max-height: 32px; }
.sticky .navbar.responsive-sticky .navbar-nav .nav-link { padding: 18px 0; }
.sticky .navbar.responsive-sticky .header-cart-icon a > i, .sticky .navbar.responsive-sticky .header-cart-icon a > i, .sticky .navbar.responsive-sticky .header-language a > i, .sticky .navbar.responsive-sticky .header-search-icon a > i, .sticky .navbar.responsive-sticky .header-push-button a > i{ padding: 20px 0; }
.navbar.responsive-sticky .header-cart-icon a, .navbar.responsive-sticky .header-cart-icon a, .navbar.responsive-sticky .header-language a, .navbar.responsive-sticky .header-search-icon a, .navbar.responsive-sticky .header-push-button a { transition: none; -moz-transition: none; -webkit-transition: none; -ms-transition: none; -o-transition: none; }
header .navbar.responsive-sticky .navbar-brand img, .navbar.responsive-sticky .header-cart-icon a > i, .navbar.responsive-sticky .header-cart-icon a > i, .navbar.responsive-sticky .header-language a > i, .navbar.responsive-sticky .header-search-icon a > i, .navbar.responsive-sticky .header-push-button a > i{ transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out; }

/* header sidebar */
.sidebar-nav-action { position: fixed; top: 0; left: 0; width: 65px; height: 100%; z-index: 10006; padding: 30px 0; }
.page-wrapper { padding-left: 65px; }
.sticky-nav-aaa { padding-left: 290px; }
.sidebar-nav-action-main { display: flex; flex-flow: column; justify-content: space-between; text-align: center; height: 100%; }

/* sidebar nav menu */
.sidebar-nav-menu { position: absolute; height: 100%; width: 290px; border-right: 1px solid #f3f3f3; overflow-y: auto; z-index: 10005; top: 0; left: 0; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-ransition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }
.menu-style-2 .sidebar-nav-menu { position: fixed; height: 100%; width: 380px; border-right:none;  overflow-y: auto; z-index: 10005; top: 0; left: 0; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-ransition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }
.menu-style-2 .sidebar-nav-menu { left: -100%; }
.show-menu { overflow: hidden; }
.show-menu.left-classic-mobile-menu { overflow: visible; }
.show-menu .menu-style-2 .sidebar-nav-menu { left: 65px; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-ransition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }
.menu-style-2 .sidebar-nav-menu .hover-background-color {position: absolute; width: 100%; height: 100%; opacity: 0; -webkit-transition-duration: 0.6s; -moz-transition-duration: 0.6s; -ms-ransition-duration: 0.6s; -o-transition-duration: 0.6s; transition-duration: 0.6s;}
.show-menu .menu-style-2 .sidebar-nav-menu:hover .hover-background-color, .menu-style-2 .sidebar-nav-menu .hover-background-color:hover {opacity: 1; }
.show-menu .menu-style-2 .sidebar-nav-menu:hover .sub-menu-item .sub-menu-item li a {color: rgba(255,255,255,.6)}
.side-menu .sidebar-nav-menu .menu-list-wrapper { height: 100%; overflow-y: hidden; padding: 75px 0; }
.side-menu .sidebar-nav-menu .menu-list-wrapper .mCustomScrollBox { display: -webkit-box; display: -ms-flexbox; display: flex; }
.side-menu .sidebar-nav-menu .menu-list-wrapper .mCustomScrollBox .mCSB_container.mCS_y_hidden.mCS_no_scrollbar_y { -ms-flex-item-align: center; align-self: center; }
.side-menu .sidebar-nav-menu .menu-list-wrapper .mCustomScrollBox .mCSB_container { width: 100%; -ms-flex-item-align: start; align-self: flex-start; }
.side-menu .sidebar-nav-menu .menu-list-wrapper .mCSB_inside>.mCSB_container { margin: 0 !important; padding: 0 15px !important; }
.side-menu-header .side-menu-button { display: none; }
.sidebar-nav-menu.left-sidebar-nav { position: fixed; width: 290px; overflow: hidden; height: inherit; }
.left-sidebar-nav .menu-list li {list-style: none;}
.left-sidebar-nav .menu-list .menu-list-item .sub-menu-item li:last-child {margin-bottom: 10px}
.left-sidebar-nav .menu-list li a {font-size:14px; padding: 13px 0 14px; line-height: normal; }
.left-sidebar-nav .menu-list .menu-list-item .sub-menu-item li a {font-size:13px; padding:5px 0}
.left-sidebar-nav .menu-toggle:before, .left-sidebar-nav .menu-toggle:after { right: 7px; top: 23px; height: 1px; width: 9px;}
.left-sidebar-nav .sub-menu-item .menu-toggle { width: 30px; height: 30px; }
.left-sidebar-nav .sub-menu-item .menu-toggle:before, .left-sidebar-nav .sub-menu-item .menu-toggle:after { right: 8px; top: 15px; height: 1px; width: 7px;}
.left-sidebar-nav .menu-toggle {width: 48px; height: 48px; top: 24px; line-height: 48px; }
.sidebar-nav-menu.left-sidebar-nav .footer-holder .newsletter-email input { min-height: 44px;}

/* sidebar nav */
.sidebar-nav { position: fixed; height: 100%; width: 290px; border-right: 1px solid #f3f3f3; z-index: 10005; top: 0; left: 0; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-ransition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }
.sidebar-nav .footer-holder { text-align: center; }
.side-menu-header .side-menu-button { display: none; }
header.side-menu-nav { position: absolute; left: 0; width: 290px; top: -100%; min-height: 100vh; bottom: 0; z-index: 999; border-right: 1px solid #f3f3f3; }
.left-sidebar-wrapper { position: relative; min-height: 300px; width: 100%; padding-left: 290px; }

/* header toggle button */
.navbar-toggler { font-size: 24px; width: 22px; height: 14px; display: inline-block; position: relative; padding: 0; border: none; vertical-align: middle; border-radius: 0; }
.navbar-toggler-line { background: #232323; height: 2px; width: 22px; content: ""; display: block; border-radius: 0; position: absolute; left: 0; right: 0; margin-left: auto; margin-right: auto; -webkit-transform: rotate(0deg); -moz-transform: rotate(0deg); -o-transform: rotate(0deg); transform: rotate(0deg); -webkit-transition: .25s ease-in-out; -moz-transition: .25s ease-in-out; -o-transition: .25s ease-in-out; transition: .25s ease-in-out;  }
.navbar-dark .navbar-toggler-line { background: #fff; }
header.sticky .header-light .navbar-toggler-line { background: #232323; }

header.sticky .navbar.header-dark .navbar-toggler-line { background: #fff; }
.navbar-toggler-line:nth-child(1) { top: 0px; width: 14px; }
.navbar-toggler-line:nth-child(2),.navbar-toggler-line:nth-child(3) { top: 6px; }
.navbar-toggler-line:nth-child(4) { top: 12px; width: 14px; }
.navbar-collapse-show .navbar-toggler-line:nth-child(1) { top: 7px; width: 0%; }
.navbar-collapse-show .navbar-toggler-line:nth-child(2) { -webkit-transform: rotate(45deg); -moz-transform: rotate(45deg); -o-transform: rotate(45deg); -ms-transform: rotate(45deg); transform: rotate(45deg);  }
.navbar-collapse-show .navbar-toggler-line:nth-child(3) { -webkit-transform: rotate(-45deg); -moz-transform: rotate(-45deg); -o-transform: rotate(-45deg); -ms-transform: rotate(-45deg); transform: rotate(-45deg); }
.navbar-collapse-show .navbar-toggler-line:nth-child(4) { top: 7px; width: 0%; }

/* header background color on scroll */
.sticky.header-appear .header-light, .sticky .header-light.header-always-fixed-scroll { background-color: #fff !important; box-shadow: 0 0 35px rgba(0,0,0,0.1); border-bottom: none !important }
.sticky.header-appear .header-dark, .sticky .header-dark.header-always-fixed-scroll { background-color: #000 !important; box-shadow: 0 0 35px rgba(0,0,0,0.1); border-bottom: none !important; transition-duration: 0.5s; -webkit-transition-duration: 0.5s; -moz-transition-duration: 0.5s; -ms-transition-duration: 0.5s; -o-transition-duration: 0.5s; }
.sticky .header-light, .sticky .header-light.header-always-fixed-scroll { box-shadow: 0 0 35px rgba(0,0,0,0.1); }

.sticky.header-appear .header-light.transparent, .sticky .header-light.transparent.header-always-fixed-scroll { background-color: rgba(255,255,255,0.6) !important; }
.sticky.header-appear .header-dark.transparent, .sticky .header-dark.transparent.header-always-fixed-scroll { background-color: rgba(0,0,0,0.6) !important; }

.sticky .header-reverse-scroll { -webkit-transform: translateY(-100%); -moz-transform: translateY(-100%); -ms-transform: translateY(-100%); -o-transform: translateY(-100%); transform: translateY(-100%); -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; box-shadow: none; }
.sticky.header-appear .header-reverse-scroll { -webkit-transform: inherit; -moz-transform: inherit; -ms-transform: inherit; -o-transform: inherit; transform: inherit; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }

.sticky .navbar.header-light .navbar-nav .nav-link, .sticky .navbar.header-light .header-search-icon > a, .sticky .navbar.header-light .header-social-icon > a, .sticky .navbar.header-light .header-cart-icon > a, .sticky .navbar.header-light .header-language > a, .sticky .navbar.header-light .nav-bar-contact { color: #232323 !important; }
.sticky .navbar.header-light .header-push-button .push-button > span {background: #232323}
.sticky .navbar.header-dark .navbar-nav .nav-link, .sticky .navbar.header-dark .header-search-icon > a, .sticky .navbar.header-dark .header-social-icon > a, .sticky .navbar.header-dark .header-cart-icon > a, .sticky .navbar.header-dark .header-language > a { color: #fff !important; }
.sticky .navbar.header-light .btn.btn-white { background-color: #232323; border-color: #232323; color: #fff; }

/* header navigation white */
.navbar.navbar-dark .navbar-nav .nav-link,
.navbar.navbar-dark .header-search-icon > a,
.navbar.navbar-dark .header-social-icon > a,
.navbar.navbar-dark .header-cart-icon > a,
.navbar.navbar-dark .header-language > a,
.navbar.navbar-dark .nav-bar-contact,
.top-bar.navbar-light,
.top-bar.navbar-light .header-search-icon > a,
.top-bar.navbar-light .header-social-icon > a, 
.top-bar.navbar-light .header-cart-icon > a, 
.top-bar.navbar-light .header-language > a,
.top-bar.navbar-light .top-bar-contact-list > a,
.top-bar.navbar-light .header-language.dropdown-style-01 .btn { color: #fff; }
.navbar.navbar-light .header-search-icon > a,
.navbar.navbar-light .header-social-icon > a,
.navbar.navbar-light .header-cart-icon > a,
.navbar.navbar-light .header-language > a,
.top-bar.navbar-dark ,
.top-bar.navbar-dark .header-search-icon > a, 
.top-bar.navbar-dark .header-social-icon > a, 
.top-bar.navbar-dark .header-cart-icon > a, 
.top-bar.navbar-dark .header-language > a, 
.top-bar.navbar-dark .header-language.dropdown-style-01 .btn { color: #232323; }

.navbar.navbar-dark .navbar-nav .nav-link.active,
.navbar-dark .navbar-nav .nav-link:hover,
.navbar-dark .navbar-nav .nav-link:focus, 
.navbar.navbar-dark .header-search-icon > a:hover, 
.navbar.navbar-dark .header-social-icon > a:hover, 
.navbar.navbar-dark .header-cart-icon > a:hover, 
.navbar.navbar-dark .header-language > a:hover, 
.navbar.navbar-dark .header-language a > i > a:hover,
.sticky .navbar-light.header-dark .navbar-nav .nav-link.active,
.sticky .navbar-light.header-dark .navbar-nav .nav-link:hover,
.sticky .navbar-light.header-dark .navbar-nav .nav-link:focus,
.sticky .navbar-light.header-dark .header-search-icon > a:hover,
.sticky .navbar-light.header-dark .header-social-icon > a:hover,
.sticky .navbar-light.header-dark .header-cart-icon > a:hover,
.sticky .navbar-light.header-dark .header-language > a:hover,
.sticky .navbar-light.header-dark .header-language > a:hover,
.top-bar.navbar-light .header-search-icon > a:hover,
.top-bar.navbar-light .header-social-icon > a:hover, 
.top-bar.navbar-light .header-cart-icon > a:hover,
.top-bar.navbar-light .header-language > a:hover, 
.top-bar.navbar-light .top-bar-contact-list > a:hover, 
.top-bar.navbar-light .header-language.dropdown-style-01 .btn:hover { color: rgba(255,255,255,.6) !important; cursor: pointer; }

/* header navigation dark */
.navbar.navbar-light .navbar-nav .nav-link.active,
.navbar-light .navbar-nav .nav-link:hover,
.navbar-light .navbar-nav .nav-link:focus,
.navbar.navbar-light .header-search-icon > a:hover,
.navbar.navbar-light .header-social-icon > a:hover,
.navbar.navbar-light .header-cart-icon > a:hover,
.navbar.navbar-light .header-language > a:hover,
.navbar.navbar-light .header-language > a:hover,
.sticky .navbar-dark.header-light .navbar-nav .nav-link.active,
.sticky .navbar-dark.header-light .navbar-nav .nav-link:hover,
.sticky .navbar-dark.header-light .navbar-nav .nav-link:focus,
.sticky .navbar-dark.header-light .header-search-icon > a:hover,
.sticky .navbar-dark.header-light .header-social-icon > a:hover,
.sticky .navbar-dark.header-light .header-cart-icon > a:hover,
.sticky .navbar-dark.header-light .header-language > a:hover,
.sticky .navbar-dark.header-light .header-language > a:hover,
.top-bar.navbar-dark .header-search-icon > a:hover,
.top-bar.navbar-dark .header-social-icon > a:hover, 
.top-bar.navbar-dark .header-cart-icon > a:hover,
.top-bar.navbar-dark .header-language > a:hover, 
.top-bar.navbar-dark .header-language.dropdown-style-01 .btn:hover { color: rgba(0,0,0,.6) !important; cursor: pointer; }
.navbar.navbar-light .navbar-nav .nav-link { color: #232323; }
.navbar.navbar-light .navbar-nav .nav-link.active,
.navbar-light .navbar-nav .nav-link:hover,
.navbar-light .navbar-nav .nav-link:focus { color: rgba(0,0,0,.5); cursor: pointer; }

/* header navigation active color */
.navbar.navbar-dark .navbar-nav > .dropdown.active > a,
.sticky .navbar.header-dark .navbar-nav > .dropdown.active > a, 
.navbar.navbar-dark .navbar-nav > .nav-item.dropdown.megamenu:hover > a, 
.navbar.navbar-dark .navbar-nav > .nav-item.dropdown.simple-dropdown:hover > a,
.sticky .navbar.navbar-light.header-dark .navbar-nav > .nav-item.dropdown.megamenu:hover > a, 
.sticky .navbar.navbar-light.header-dark .navbar-nav > .nav-item.dropdown.simple-dropdown:hover > a{ color: rgba(255,255,255,0.6) !important; }
.navbar.navbar-light .navbar-nav > .dropdown.active > a, 
.sticky .navbar.header-light .navbar-nav > .dropdown.active > a, 
.navbar.navbar-light .navbar-nav > .nav-item.dropdown.megamenu:hover > a, 
.navbar.navbar-light .navbar-nav > .nav-item.dropdown.simple-dropdown:hover > a,
.sticky .navbar.navbar-dark.header-light .navbar-nav > .nav-item.dropdown.megamenu:hover > a, 
.sticky .navbar.navbar-dark.header-light .navbar-nav > .nav-item.dropdown.simple-dropdown:hover > a { color: rgba(0,0,0,0.6) !important; }

/* header logo */
header .navbar .navbar-brand img, header.side-menu .logo img, header .sidebar-nav-menu .navbar-brand img { max-height: 36px; }
header .navbar .navbar-brand .default-logo, header.sticky .navbar-brand .alt-logo, header .sidebar-nav-menu .navbar-brand .default-logo { visibility: visible; opacity: 1; width: auto; transition-duration:0.5s; -webkit-transition-duration: 0.5s; -moz-transition-duration: 0.5s; -ms-transition-duration: 0.5s; -o-transition-duration: 0.5s; }
header.sticky .navbar .navbar-brand .default-logo, header .navbar-brand .alt-logo, header .navbar .navbar-brand .mobile-logo, header .sidebar-nav-menu .navbar-brand .mobile-logo  { visibility: hidden; opacity: 0; width: 0; transition-duration:0.5s; -webkit-transition-duration: 0.5s; -moz-transition-duration: 0.5s; -ms-transition-duration: 0.5s; -o-transition-duration: 0.5s; }

header .navbar.always-fixed .navbar-brand .default-logo, header.sticky .always-fixed .navbar-brand .alt-logo, 
header.sticky .navbar.always-fixed .navbar-brand .default-logo, header .always-fixed .navbar-brand .alt-logo, 
header .header-always-fixed-scroll .navbar-brand .default-logo, header .header-always-fixed-scroll .navbar-brand .alt-logo,
header .navbar.always-fixed .navbar-brand .mobile-logo, header .sidebar-nav-menu .navbar-brand .mobile-logo, 
header .header-always-fixed-scroll .navbar-brand .mobile-logo { transition-duration:0s; -webkit-transition-duration: 0s; -moz-transition-duration: 0s; -ms-transition-duration: 0s; -o-transition-duration: 0s; }

/* header navigation spacing */
.navbar .navbar-nav .nav-link { padding: 25px 0; font-weight: 500; font-size: 14px; margin: 0 18px; word-break: normal; }
.navbar.top-logo .navbar-nav .nav-link { padding: 20px 0; }
.navbar .justify-content-end .navbar-nav li:last-child .nav-link { margin-right: 0; }
.navbar .justify-content-start .navbar-nav li:first-child .nav-link { margin-left: 0; }

/* header cart icon */
.header-cart-icon > a { position: relative; }
header .header-cart-icon .cart-count { position: absolute; top: -6px; right: -10px; width: 16px; height: 16px; text-align: center; font-size: 9px; line-height: 16px; border-radius: 100%; }

/* header push button */
.navbar.navbar-dark .push-button > span, .sticky .navbar.header-dark .push-button > span { background: #fff; }
.navbar.navbar-dark .push-button:hover, .sticky .navbar.header-dark .push-button:hover { opacity: 0.6; }
.navbar.navbar-light .push-button > span, .sticky .navbar.header-light .push-button > span { background: #232323; }
.navbar.navbar-light .push-button:hover, .sticky .navbar.header-light .push-button:hover { opacity: 0.6; }
.push-button { position: relative; width: 22px; height: 14px; display: block; top: 1px; }
.push-button > span { display: block; margin-left: auto; margin-right: auto; width: 22px; height: 2px; background-color: #fff; border-radius: 0; opacity: 1; position: absolute; left: 0; right: 0; -webkit-transform: rotate(0deg); -moz-transform: rotate(0deg); -o-transform: rotate(0deg); transform: rotate(0deg); -webkit-transition: .25s ease-in-out; -moz-transition: .25s ease-in-out; -o-transition: .25s ease-in-out; transition: .25s ease-in-out; }
.push-button > span:nth-child(1) { top: 0px; width: 14px} 
.push-button > span:nth-child(2), .push-button > span:nth-child(3) { top: 6px; }
.push-button > span:nth-child(4) { top: 12px; width: 14px }
.show-menu .push-button > span:nth-child(1) { top: 7px; width: 0%; }
.show-menu .push-button > span:nth-child(2) { -webkit-transform: rotate(45deg); -moz-transform: rotate(45deg); -o-transform: rotate(45deg); -ms-transform: rotate(45deg); transform: rotate(45deg); }
.show-menu .push-button > span:nth-child(3) { -webkit-transform: rotate(-45deg); -moz-transform: rotate(-45deg); -o-transform: rotate(-45deg); -ms-transform: rotate(-45deg); transform: rotate(-45deg); }
.show-menu .push-button > span:nth-child(4) { top: 7px; width: 0%; }
.push-button.alt-push-button-left > span:nth-child(2) { width: 22px; }
.side-menu-button { position: absolute; top: 50%; left: 50%; -webkit-transform: translate(-50%,-50%); -moz-transform: translate(-50%,-50%); -ms-transform: translate(-50%,-50%); -o-transform: translate(-50%,-50%); transform: translate(-50%,-50%); }
.left-sidebar-nav .side-menu-button { left: auto; right:15px; text-align: right; -webkit-transform: translate(0,-50%); -moz-transform: translate(0,-50%); -ms-transform: translate(0,-50%); -o-transform: translate(0,-50%); transform: translate(0,-50%); }
.sidebar-social-icon li { margin: 15px 0; }

/* sidebar action button */
.side-menu-button a { width: 22px; height: 14px; display: inline-block; position: relative; }
.nav-icon span { display: block; margin-left: auto; margin-right: auto; width: 22px; height: 2px; background-color: #fff; border-radius: 0; opacity: 1; position: absolute; left: 0; right: 0; -webkit-transform: rotate(0deg); -moz-transform: rotate(0deg); -o-transform: rotate(0deg); transform: rotate(0deg); -webkit-transition: .25s ease-in-out; -moz-transition: .25s ease-in-out; -o-transition: .25s ease-in-out; transition: .25s ease-in-out; }
.nav-icon.dark span { background-color: #232323; }
.nav-icon span:nth-child(1) { top: 0px; width: 14px }
.nav-icon span:nth-child(2),.nav-icon span:nth-child(3) { top: 6px; }
.nav-icon span:nth-child(4) { top: 12px; width: 14px }
.show-menu .nav-icon span:nth-child(1) { top: 7px; width: 0%; }
.show-menu .nav-icon span:nth-child(2) { -webkit-transform: rotate(45deg); -moz-transform: rotate(45deg); -o-transform: rotate(45deg); -ms-transform: rotate(45deg); transform: rotate(45deg); }
.show-menu .nav-icon span:nth-child(3) { -webkit-transform: rotate(-45deg); -moz-transform: rotate(-45deg); -o-transform: rotate(-45deg); -ms-transform: rotate(-45deg); transform: rotate(-45deg); }
.show-menu .nav-icon span:nth-child(4) { top: 7px; width: 0%; }

/* header push menu close button */
.close-menu { height: 50px; width: 50px; text-align: center; line-height: 50px; font-size: 16px; position: absolute; right: 20px; top: 20px; z-index: 1; }
.close-menu:hover {color:#232323}

/* header push menu */
.push-menu { position: fixed; bottom: 0; right: -100%; width: 500px; height: 100vh; z-index: 1060; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }
.show-menu .push-menu { right: 0; box-shadow: 0 0 20px rgba(0, 0, 0, .3); -moz-box-shadow: 0 0 20px rgba(0, 0, 0, .3); -webkit-box-shadow: 0 0 20px rgba(0, 0, 0, .3); -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }
.push-menu .instagram-follow-api ul { display: block;}
.push-menu .btn i {top:0}
.push-menu .push-menu-wrapper { height: 100%; overflow-y: hidden; padding: 70px 0; -webkit-overflow-scrolling: touch; }
.push-menu .push-menu-wrapper .mCustomScrollBox { display: -webkit-box; display: -ms-flexbox; display: flex; }
.push-menu .push-menu-wrapper .mCustomScrollBox .mCSB_container.mCS_y_hidden.mCS_no_scrollbar_y { -ms-flex-item-align: center; align-self: center; }
.push-menu .push-menu-wrapper .mCustomScrollBox .mCSB_container { width: 100%; -ms-flex-item-align: start; align-self: flex-start; }
.push-menu .push-menu-wrapper .mCSB_inside>.mCSB_container { margin: 0 !important; padding: 0 15px !important; }

/* hamburger menu */
.hamburger-menu { position: fixed; top: 0; right: -100%; width: 50%; height: 100%; z-index: 1031; overflow-y: auto; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }
.hamburger-menu.full-width { width: 100%; }
.show-menu .hamburger-menu { right: 0; }
.menu-list-wrapper { overflow: auto; height: calc(100vh - 150px); }
.menu-list-wrapper.menu-list-wrapper-small { max-height: calc(65vh - 100px); }
.menu-list-wrapper.small-wrapper { max-height: calc(70% - 50px); }
.menu-list-wrapper .mCSB_inside>.mCSB_container { margin: 0 !important; }
.menu-list-wrapper .mCustomScrollBox { display: -webkit-box; display: -ms-flexbox; display: flex; width: 100%; }
.menu-list-wrapper .mCustomScrollBox .mCSB_container.mCS_y_hidden.mCS_no_scrollbar_y { -ms-flex-item-align: center; align-self: center; }
.menu-list-wrapper .mCustomScrollBox .mCSB_container { width: 100%; -ms-flex-item-align: start; align-self: flex-start; }
.menu-list li { clear: both; position: relative; list-style: none; }
.menu-list li a { font-size: 20px; line-height: 28px; position: relative; color: #fff; padding: 14px 0; display: inline-block;  }
.menu-list li a i {margin-right: 10px; vertical-align: middle}
.menu-list .menu-list-item .sub-menu-item li a { font-size: 13px; line-height: normal; color: #fff; padding: 0; }
.menu-list .menu-list-item .sub-menu-item { position: relative; width: 100%; background-color: transparent; }
.menu-list .menu-list-item .sub-menu-item .sub-menu-item { padding-left: 15px; }
.menu-list .menu-list-item .sub-menu-item .sub-menu-item li:first-child  { margin-top: 8px;}
.menu-list .menu-list-item .sub-menu-item .sub-menu-item li:last-child { margin-bottom: 10px;}
.menu-list .menu-list-item .sub-menu-item .sub-menu-item li a {color:#888888}
.menu-sidebar a { padding: 10px 0; display: block; position: relative; }

/* hamburger menu dark text */
.menu-list.dark li a, .menu-list.dark .menu-list-item .sub-menu-item li a { color: #232323; }
.menu-list.dark .menu-list-item .sub-menu-item .sub-menu-item li a {color:#656565}
.menu-toggle { position: absolute; right: 0; width: 30px; height: 30px; text-align: center; top: 30px; cursor: pointer; transform: translateY(-50%); -webkit-transform: translateY(-50%); -moz-transform: translateY(-50%); -ms-transform: translateY(-50%); -o-transform: translateY(-50%); z-index: 1; }
.sub-menu-item .menu-toggle { width: 20px; height: 20px; top: 15px; }
.menu-toggle:before, .menu-toggle:after { content: ""; display: block; position: absolute; right: 7px; top: 14px; height: 1px; width: 11px; background-color: #fff; }
.sub-menu-item .menu-toggle:before, .sub-menu-item .menu-toggle:after { right: 9px; top: 10px; height: 1px; width: 9px; background: rgba(255,255,255,.5); }
.dark .menu-toggle:before, .dark .menu-toggle:after { background-color: #232323; }
.menu-toggle.collapsed:after { transform: rotate(90deg); -moz-transform: rotate(90deg); -webkit-transform: rotate(90deg); -ms-transform: rotate(90deg); transition-duration: 0.3s; }
.menu-toggle:after { transform: rotate(0deg); -moz-transform: rotate(0deg); -webkit-transform: rotate(0deg); -ms-transform: rotate(0deg); transition-duration: 0.3s; }

.menu-list.dark li.active > a,
.menu-list.dark > li:hover > a,
.menu-list.dark > li:focus > a,
.menu-list.dark > .menu-list-item .sub-menu-item li.active a, 
.menu-list.dark > .menu-list-item .sub-menu-item > li:hover a,
.menu-list.dark > .menu-list-item .sub-menu-item > li:focus a  { color: rgba(0,0,0,.6) !important; }
.menu-list.dark > .menu-list-item .sub-menu-item li.active > .sub-menu-item li.active a,
.menu-list.dark > .menu-list-item .sub-menu-item li.active > .sub-menu-item li a:hover,
.menu-list.dark > .menu-list-item .sub-menu-item li.active > .sub-menu-item li a:focus { color: rgba(0,0,0,.3) !important; }
.menu-list li.active > a,
.menu-list .menu-list-item:hover >  a,
.menu-list .menu-list-item:focus > a,
.menu-list .menu-list-item .sub-menu-item li.active a, 
.menu-list .menu-list-item .sub-menu-item > li:hover a,
.menu-list .menu-list-item .sub-menu-item > li:focus a  { color: rgba(255,255,255,.5) !important; }
.menu-list .menu-list-item .sub-menu-item li.active > .sub-menu-item li.active a,
.menu-list .menu-list-item .sub-menu-item li.active > .sub-menu-item li a:hover,
.menu-list .menu-list-item .sub-menu-item li.active > .sub-menu-item li a:focus { color: rgba(255,255,255,.3) !important }

/* hamburger menu big text */
.hamburger-menu-big-font .menu-list li { padding:  13px 0; }
.hamburger-menu-big-font .menu-list li a { font-size: 44px; line-height: 50px; position: relative; padding: 0; }
.hamburger-menu-big-font .menu-list li a:before {content: ""; position: absolute; width: 0; height: 2px; bottom: 0; left: 50%; background-color: rgba(255,255,255,.5); visibility: hidden; transform: translateX(-50%); -webkit-transform: translateX(-50%); -moz-transform: translateX(-50%); -ms-transform: translateX(-50%); -webkit-transition: all 0.3s ease-in-out 0s; transition: all 0.3s ease-in-out 0s;}
.hamburger-menu-big-font .menu-list.dark li a:before {background-color: rgba(23,23,23,.5); }
.hamburger-menu-big-font .menu-list li > a:hover:before { visibility: visible; width: 100%; }
.hamburger-menu-big-font .menu-list li .menu-toggle { top: 41px; }
.hamburger-menu-big-font .menu-list li .sub-menu-item { padding: 25px 0 0 20px;}
.hamburger-menu-big-font .menu-list li .sub-menu-item li{ padding: 0 0 4px 0; }
.hamburger-menu-big-font .menu-list li .sub-menu-item li a:before { display: none}
.hamburger-menu-big-font .menu-list li .sub-menu-item li a:hover { text-decoration: underline; }
.hamburger-menu-big-font .menu-list li .sub-menu-item li .menu-toggle { top: 15px; }
.hamburger-menu-big-font .menu-list li .sub-menu-item .sub-menu-item { padding: 0;}

/* mega menu */
.dropdown-menu.megamenu-content { padding: 25px; margin: 0; border-radius: 0; }
.dropdown-menu.megamenu-content li a { text-align: left; font-size: 13px; color: #828282 ; padding: 0; margin: 0 0 5px 0; display: block; position: relative; overflow: hidden; white-space: nowrap; font-weight: 500 }
.dropdown-menu.megamenu-content li.active a, .dropdown-menu.megamenu-content li a:hover { color:#232323; }
.dropdown-menu.megamenu-content li.dropdown-header { color: #232323; display: inline-block; padding: 0; font-size: 14px; font-weight: 500; margin: 15px 0 10px 0; border-bottom: none;}
.dropdown-menu.megamenu-content li.dropdown-header:first-child { margin-top: 0;}
.nav-item.dropdown.megamenu { position: inherit; }
.nav-item.dropdown.megamenu .menu-back-div { box-sizing: border-box; background: #fff; padding: 60px 80px; box-shadow: 0 0 35px rgba(0,0,0,0.1); border-radius: 4px; border: 0; left: auto; }
.nav-item.dropdown.megamenu .menu-back-div .col, .nav-item.dropdown.megamenu .menu-back-div [class *="col-"] { padding-left: 0; padding-right: 90px; list-style: none; }
.nav-item.dropdown.megamenu .menu-back-div .col:last-child, .nav-item.dropdown.megamenu .menu-back-div [class *="col-"]:last-child { padding-right: 0; }
.dropdown-menu.megamenu-content li a > i { font-size: 14px; vertical-align: middle; width: 26px; }
.nav-item.dropdown.megamenu .menu-back-div .col, .nav-item.dropdown.megamenu .menu-back-div ul[class *="d-"] { padding-left: 0; padding-right: 90px; list-style: none; }
.nav-item.dropdown.megamenu .menu-back-div ul.small-gap[class *="d-"] { padding-left: 0; padding-right: 30px; }
.nav-item.dropdown.megamenu .menu-back-div .col:last-child, .nav-item.dropdown.megamenu .menu-back-div  ul[class *="d-"]:last-child { padding-right: 50px; }
.nav-item.dropdown.megamenu img { max-width: 210px}

/* simple dropdown */
.simple-dropdown .dropdown-menu { position: absolute; left: 0; width: 240px; padding: 25px 0; box-shadow: 0 0 35px rgba(0,0,0,0.1); margin: 0; border: none; border-radius: 4px; }
.simple-dropdown .dropdown-menu .dropdown .dropdown-menu { left: 241px; margin-top:0; top: -25px; }
.simple-dropdown.menu-left .dropdown-menu { position: absolute; left: auto; min-width: 240px; padding: 25px 0 20px;; }
.simple-dropdown.menu-left .dropdown-menu .dropdown .dropdown-menu,
.simple-dropdown .dropdown.menu-left .dropdown-menu { left: auto; right: 241px; }
.simple-dropdown .dropdown-menu li { padding: 9px 45px 9px 35px; display: block; }
.simple-dropdown .dropdown-menu .dropdown a { display: block; color: #828282; font-size: 13px; line-height: normal; font-weight: 500 }
.simple-dropdown .dropdown-menu .dropdown:hover > a, .simple-dropdown .dropdown-menu .dropdown a:hover,.simple-dropdown .dropdown-menu .dropdown li.active > a, .simple-dropdown .dropdown-menu .dropdown a.active,.simple-dropdown .dropdown-menu .dropdown a:focus, .simple-dropdown .dropdown-menu .dropdown.active > a { color: #232323; }
.simple-dropdown .dropdown-menu .dropdown a .dropdown-toggle { position: absolute; right: 23px; font-size: 12px; margin-top: 0; height: 20px; width: 20px; line-height: 20px; text-align: center; top: 50%; transform: translateY(-50%); -webkit-transform: translateY(-50%); -moz-transform: translateY(-50%); -o-transform: translateY(-50%); -ms-transform: translateY(-50%); }

/* shipping cart */
.cart-item-list { left: auto; right:0; box-shadow: 0 0 35px rgba(0,0,0,0.1); border-radius: 4px; border: 0; padding: 0; min-width: 250px; margin: 0; }
.cart-item-list .cart-item { display: flex; padding: 17px; border-bottom: 1px solid #ededed; position: relative; }
.cart-item-list .cart-item.cart-total { background-color: #f7f7f7; }
.cart-item-list .cart-item a.close { font-size: 16px; line-height: 15px; position: absolute; right: 15px; top: 15px; font-weight: 400; }
.cart-item-list .cart-item a.close:hover { color: #000; }
.cart-item-list .cart-item .product-detail a { color: #232323; font-size: 12px; display: inline-block; line-height: 16px }
.cart-item-list .cart-item .product-detail a:hover { color: #000; }
.cart-item-list .cart-item:last-child { border-bottom: none; border-radius: 0 0 6px 6px; }
.cart-item-list .cart-item.cart-total { -ms-flex-direction: column!important; flex-direction: column!important; }
.cart-item-list .cart-item.cart-total .btn { width: 100%; display: block; margin: 4px 0}
.cart-item-list .cart-item.cart-total .btn:last-child {margin-bottom: 0}
.cart-item-list .cart-item .product-image { max-width: 50px; width: 30%; display: inline-block }
.cart-item-list .cart-item .product-detail { padding-left: 15px; width: 70%; display: inline-block }
.cart-item-list .cart-item .product-detail .item-ammount { display: block; font-size: 11px; color: #828282; line-height: normal }

/* language dropdown */
.header-language .dropdown-menu { left: auto !important; right: 0; padding: 15px; box-shadow: 0 0 35px rgba(0,0,0,0.1); border-radius: 6px; border: 0; margin: 0; min-width: 140px; }
.header-language .dropdown-menu a { color: #828282; display: block; padding: 2px 0; font-size: 12px }
.header-language .dropdown-menu a:hover { color: #000; }

/* language dropdown style 01 */
.header-language.dropdown-style-01 .btn { color: #828282; border: 1px solid rgba(0,0,0,.1); border-left: 0; border-top: 0 !important; border-bottom: 0 !important; font-size: 12px; line-height: 11px; padding: 18px 18px 14px 18px; margin: 0; background: transparent; font-weight: normal; }
.top-bar .header-language.dropdown-style-01 { padding-left: 0;}
.header-language.dropdown-style-01 .btn:hover { color: #232323; }
.header-language.dropdown-style-01 .btn i { margin-left: 2px; top:-1px }
.header-language.dropdown-style-01 .dropdown-menu { z-index: 99999; margin-top: 1px; border: 0; }

/* megamenu and dropdown dark background color */
.megamenu-dropdown-dark .nav-item.dropdown.megamenu .menu-back-div, .megamenu-dropdown-dark .dropdown-menu, .megamenu-dropdown-dark .nav-item.dropdown.simple-dropdown .dropdown-menu { background: #232323; }
.megamenu-dropdown-dark .dropdown-menu.megamenu-content li.dropdown-header { color: #fff; }
.megamenu-dropdown-dark .dropdown-menu.megamenu-content li.active a, .megamenu-dropdown-dark .dropdown-menu.megamenu-content li a:hover { color: #fff; }
.megamenu-dropdown-dark .simple-dropdown .dropdown-menu .dropdown:hover > a, 
.megamenu-dropdown-dark .simple-dropdown .dropdown-menu .dropdown a:hover, 
.megamenu-dropdown-dark .simple-dropdown .dropdown-menu .dropdown li.active > a,
.megamenu-dropdown-dark .simple-dropdown .dropdown-menu .dropdown a.active, 
.megamenu-dropdown-dark .simple-dropdown .dropdown-menu .dropdown a:focus, 
.megamenu-dropdown-dark .simple-dropdown .dropdown-menu .dropdown.active > a { color: #fff; }
.megamenu-dropdown-dark .header-language .dropdown-menu a:hover { color: #fff; }
.megamenu-dropdown-dark .cart-item-list .cart-item a { color: #828282; }
.megamenu-dropdown-dark .cart-item-list .cart-item a:hover { color: #fff; }
.megamenu-dropdown-dark .cart-item-list .cart-item.cart-total { background-color: #333333; }
.megamenu-dropdown-dark .cart-item-list .cart-item { border-color: rgba(255,255,255,.1); }
.megamenu-dropdown-dark .cart-item-list .cart-item .product-detail { color: #fff; }
.megamenu-dropdown-dark .cart-item-list .cart-item.cart-total span { color: #fff; }

/* top bar */
.top-bar-contact { display: inline-block; vertical-align: middle; }
.top-bar-contact .top-bar-contact-list { border-right: 1px solid rgba(0,0,0,.1); padding: 9px 18px; font-size: 13px; display: inline-block; float: left; }
.top-bar-contact .top-bar-contact-list:first-child { border-left: 1px solid rgba(0,0,0,.1); }
.top-bar-contact .top-bar-contact-list i {margin-right: 6px; font-size: 14px; position: relative; top: 1px; }
.top-bar .social-icon li {font-size: 13px; padding: 0 9px; display: inline-block}

/* header search form */
.form-wrapper { background: rgba(35,35,35,.9); width: 100%; left: 0; top: 0; overflow: hidden !important; transition: all .2s ease-in-out; -moz-transition: all .2s ease-in-out; -webkit-transition: all .2s ease-in-out; -ms-transition: all .2s ease-in-out; -o-transition: all .2s ease-in-out; display: block !important; height: 100%; position: fixed; visibility: hidden; opacity: 0; z-index: 999; }
.active-form .form-wrapper { opacity: 1; visibility: visible; cursor: url(../images/mfg-close.png),auto; }
.form-wrapper .search-form { height: 0; background-color: #fff; width: 100%; left: 0; top: 0; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; position: absolute; transition: all .2s ease-in-out; -moz-transition: all .2s ease-in-out; -webkit-transition: all .2s ease-in-out; -ms-transition: all .2s ease-in-out; -o-transition: all .2s ease-in-out; }
.active-form .form-wrapper .search-form { height: 450px; cursor: default; }
.form-wrapper .search-close { color: #000; position: fixed; top: 30px; right: 35px; overflow: visible; cursor: pointer; background: transparent; border: 0; -webkit-appearance: none; display: block; outline: none; padding: 0; z-index: 1046; box-shadow: none; font-size: 30px; font-weight: 300; width: 44px; height: 44px; line-height: 44px; }
.form-wrapper .search-form .search-input { color: #232323; border-bottom: 1px solid #232323; width: 100%; }
.form-wrapper .search-form-box { position: relative; width: 45%; }
.form-wrapper .search-form .search-button { color: #232323; bottom: 33px; position: absolute; }
.form-wrapper .search-form .search-input::-webkit-input-placeholder { color: #232323; opacity: 1; }
.form-wrapper .search-form .search-input::-moz-placeholder { color: #232323; opacity: 1; }
.form-wrapper .search-form .search-input::-ms-input-placeholder { color: #232323; opacity: 1; }
.show-search-popup { overflow: hidden; }
.show-search-popup header .top-bar { z-index: 9;}
.show-search-popup .fixed-top { z-index: 1032; }
.show-search-popup.show-search-popup-mini-header .top-bar { z-index: 1033; }
.search-form .search-input { border: none; border-radius: 0; border-bottom: 2px solid rgba(255,255,255,0.5); font-size: 20px; font-weight: 300; padding: 10px 38px 10px 2px; background: transparent; line-height: 40px; color: #fff; }
.search-form .search-input:focus { outline: none; }
.search-input::-moz-placeholder, .search-input [placeholder] { color: #787878; }
.search-form .search-button { font-size: 16px; color: #fff; bottom: 37px; right: 0; line-height: normal; outline: none; cursor: pointer; z-index: 0; margin-right: 0; border: none; background: none; top: inherit; }
.search-button { cursor: pointer; z-index: 0; margin-right: 0; border: none; background: none; }

/* menu modern */
.navbar-modern-inner, .navbar-full-screen-menu-inner { display: none}

/* ===================================
    19. Page title
====================================== */

.page-title-extra-small h1 { font-size: 16px; line-height: 20px; }
.page-title-small h1 { font-size: 18px; line-height: 20px; }
.page-title-medium h1 { font-size: 28px; line-height: 32px; }
.page-title-large h1 { font-size: 42px; line-height: 49px; }
.page-title-extra-large h1 { font-size: 55px; line-height: 62px; }

.page-title-small span { padding-left: 25px; margin-left: 25px; position: relative; top: -1px; }
.page-title-small span:before { position: absolute; border-left: 2px solid #232323; content: ""; height: 12px; top: 4px; left: 0 }

.page-title-separator-line { height: 1px; position: absolute; top: 9px; left: 0; }
.page-title-separator-line:before { display: none; }

/* colorfull background page title */
.page-title-colorful { -webkit-transition-duration: 3s; -moz-transition-duration: 3s; -ms-transition-duration: 3s; -o-transition-duration: 3s; transition-duration: 3s;  -webkit-transition-property: background-color; -o-transition-property: background-color; transition-property: background-color; }

/* breadcrumb */
.breadcrumb { background-color: transparent; padding-top: 0; padding-bottom: 0; margin: 0;}
.breadcrumb ul li { display: inline-block; }
.breadcrumb ul li:last-child { margin-right: 0; }
.breadcrumb ul > li:after { content: "\f111"; font-family: "Font Awesome 5 Free"; font-weight: 900; padding:0 10px 0 11px; display: inline-block; vertical-align: middle; font-size: 4px; opacity: .6; }
.breadcrumb ul > li:last-child:after { border-right: 0; content: ""; padding: 0 }

/* down scroll */
.down-section { position: absolute; width: 100%; bottom:50px; left: 0; right: 0; }
.down-section-arrow { border-radius: 100%; height: 45px; width: 45px; line-height: 50px; display: inline-block; vertical-align: middle; position: relative; }
.scroll-down-bottom { position: absolute; bottom: 30px; width: auto; left: 0; right: 0; z-index: 99;}
.scroll-down-bottom .scroll-down-text { position: absolute; bottom: 25px; transform: rotate(-90deg); width: 100px; height: auto; transform-origin: 0; }
.scroll-down { position: absolute; bottom: 60px; text-align: center; width: auto; left: 0; right: 0; z-index: 99; }

/* ===================================
    20. Swiper carousel
====================================== */
.swiper-container-horizontal > .swiper-pagination-bullets, .swiper-pagination-fraction { bottom: 45px; }
.swiper-pagination-bullet { background: transparent; vertical-align: middle; border-radius: 100%; display: inline-block; height: 12px; margin: 0 7px; width: 12px; border: 2px solid #232323; outline: none; }
.swiper-pagination-bullet.swiper-pagination-bullet-active { background: #232323; }
.swiper-pagination-bullets { width: 100%; left: 0; }
.swiper-pagination-custom { bottom: 0; }

/* slides auto slide */
.swiper-container.swiper-auto-slide { width: 100%; height: 100%; }
.swiper-container.swiper-auto-slide .swiper-slide { width: auto; }
.swiper-container.swiper-auto-slide .swiper-slide:nth-child(2n) { width: auto !important; }
.swiper-container.swiper-auto-slide .swiper-slide:nth-child(3n) { width: auto !important; }

/* swiper vertical */
.slider-vertical { width: 100%; height: 100%; position: absolute; }
.slider-vertical .swiper-slide { text-align: center; background: #fff; }
.slider-vertical >.swiper-pagination-bullets { right: 60px; }
.slider-vertical .swiper-pagination { width: auto; left: inherit; bottom: inherit !important; }
.slider-vertical .swiper-slide .swiper-bottom-content { opacity: 0;  transition: all 0.3s ease-in-out 0s; -webkit-transition: all 0.3s ease-in-out 0s; -moz-transition: all 0.3s ease-in-out 0s; -ms-transition: all 0.3s ease-in-out 0s; -o-transition: all 0.3s ease-in-out 0s; }
.slider-vertical .swiper-slide-active .swiper-bottom-content { opacity: 1; transition: all 0.3s ease-in-out 0.3s; -webkit-transition: all 0.3s ease-in-out 0.3s; -moz-transition: all 0.3s ease-in-out 0.3s; -ms-transition: all 0.3s ease-in-out 0.3s; -o-transition: all 0.3s ease-in-out 0.3s; }
.slider-vertical .swiper-slide .swiper-right-content { opacity: 0; -webkit-transition: all 0.3s ease-in-out 0s; -moz-transition: all 0.3s ease-in-out 0s; -ms-transition: all 0.3s ease-in-out 0s; -o-transition: all 0.3s ease-in-out 0s; }
.slider-vertical .swiper-slide-active .swiper-right-content { opacity: 1; transition: all 0.3s ease-in-out 0.3s; -webkit-transition: all 0.3s ease-in-out 0.3s; -moz-transition: all 0.3s ease-in-out 0.3s; -ms-transition: all 0.3s ease-in-out 0.3s; -o-transition: all 0.3s ease-in-out 0.3s; }
.slider-vertical .swiper-number-pagination { position: absolute; display: -webkit-box;display: -ms-flexbox;display: flex; z-index: 1; font-family: 'Poppins', sans-serif; color: #fff; font-size: 18px; line-height: 22px; left: 105px; bottom: 125px; }
.slider-vertical .swiper-number-pagination .swiper-pagination-current:after { content: '/'; margin: 0 5px; }

/* swiper thumb */
.swiper-thumb-next-prev { position: absolute; left: 0; bottom: 0; width: 100%; padding: 0 15px; z-index: 1; }
.swiper-thumb-next-prev .swiper-thumb-prev, .swiper-thumb-next-prev .swiper-thumb-next { position: relative; top: inherit; left: inherit; transform: translate(0); width: 48%; display: inline-block; background: #232323; color: #fff; }
.single-product-thumb .swiper-slide { height: auto !important; cursor: pointer; }

/* swiper zoom slide */
.slider-zoom-slide { position: relative; }
.slider-zoom-slide .swiper-slide { opacity : 0.3; transform: scale(1); }
.slider-zoom-slide .swiper-slide.swiper-slide-active { opacity : 1; transform: scale(1); }
.slider-zoom-slide .swiper-slide .slider-zoom-content { opacity: 0}
.slider-zoom-slide .swiper-slide.swiper-slide-active .slider-zoom-content { opacity: 1}
.slider-zoom-content .slider-zoom-label { margin-top: -70px; margin-right: -25px; width: 75px; text-align: center; }
.slider-zoom-slide.slider-zoom-slide-dark .swiper-slide.swiper-slide-prev,
.slider-zoom-slide.slider-zoom-slide-dark .swiper-slide.swiper-slide-next { opacity: 1; }
.slider-zoom-slide.slider-zoom-slide-dark .swiper-slide.swiper-slide-prev .slider-zoom-slide-dark-overlay,
.slider-zoom-slide.slider-zoom-slide-dark .swiper-slide.swiper-slide-next .slider-zoom-slide-dark-overlay { opacity: .6; }

/* caption slider */
.slider-caption-blog-caption { position: absolute; bottom: 0; height: auto; width: 100%; left: 0;}
.slider-caption-blog-caption .swiper-slide { padding: 55px 60px; }
.slider-caption-blog-caption .swiper-slide h6 {font-size: 24px}
.caption-blog-next, .caption-blog-prev { background-color: #000; color: #fff; position: absolute; bottom: 0; border: 0; right: 0; font-size: 20px; width: 50px; display: flex; height: 50%; text-align: center; justify-content: center; align-items: center; z-index: 1; cursor: pointer; }
.caption-blog-next { top: 0; height: calc(50% - 2px); }
.caption-blog-next:focus,.caption-blog-prev:focus  { outline: none; }

/* product and portfolio slider */
.product-slider .swiper-slide .portfolio-box:hover .portfolio-image img {opacity: 0.5;}

/* horizontal portfolio slider */
.horizontal-portfolio-slider .swiper-slide { display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; -webkit-box-align: center; -ms-flex-align: center; align-items: center; }
.horizontal-portfolio-slider .swiper-slide .slider-title { position: relative; display: inline-block;}
.horizontal-portfolio-slider .swiper-slide .slider-title .slider-title-hover { position: absolute; top: 52%; left: 0; width: 100%; height: 4px; z-index: 1; opacity: 0; transform: scaleX(0); -webkit-transform: scaleX(0); -moz-transform: scaleX(0); -ms-transform: scaleX(0); -webkit-transition-duration: 0.5s; -moz-transition-duration: 0.5s; -ms-transition-duration: 0.5s; -o-transition-duration: 0.5s; transition-duration: 0.5s; }
.horizontal-portfolio-slider .swiper-slide:hover .slider-title-hover { opacity: 1; transform: scaleX(1); -webkit-transform: scaleX(1); -moz-transform: scaleX(1); -ms-transform: scaleX(1); -webkit-transition-duration: 0.5s; -moz-transition-duration: 0.5s; -ms-transition-duration: 0.5s; -o-transition-duration: 0.5s; transition-duration: 0.5s; }

/* swiper horizontal 3d */
.swiper-horizontal-3d.swiper-container { margin-left: auto; margin-right: auto; position: relative; overflow: hidden; z-index: 1; }
.swiper-horizontal-3d .swiper-wrapper { position: relative; width: 100%; height: 100%; z-index: 1; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-transition-property: -webkit-transform; transition-property: -webkit-transform; transition-property: transform; transition-property: transform, -webkit-transform; box-sizing: content-box; }
.swiper-horizontal-3d.swiper-pagination-bottom .swiper-wrapper { margin-bottom: 0; }
.swiper-horizontal-3d .swiper-slide { filter: grayscale(100%);  opacity: 0; -webkit-flex-shrink: 0; -ms-flex: 0 0 auto; -ms-flex-negative: 0; flex-shrink: 0; width: 100%; height: 100%; position: relative; }
.swiper-horizontal-3d .swiper-slide.swiper-slide-prev, .swiper-horizontal-3d .swiper-slide.swiper-slide-next {opacity: .7}
.swiper-horizontal-3d .swiper-slide.swiper-slide-active { opacity: 1; filter: grayscale(0); box-shadow: 0 0 60px rgba(0,0,0,.17); }
.swiper-horizontal-3d.swiper-container { width: 100%; padding-bottom: 60px; }
.swiper-horizontal-3d .swiper-slide.swiper-slide-active .slider-image:hover .preview-icon { opacity: 1; -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)"; z-index: 1; }
.swiper-horizontal-3d.swiper-container-3d .swiper-slide-shadow-left, .swiper-horizontal-3d.swiper-container-3d .swiper-slide-shadow-right { background-image: none; }
.swiper-horizontal-3d .swiper-slide img { opacity: 0; }
.swiper-horizontal-3d .swiper-slide-active img { opacity: 1; }

/* tilt box style */
.tilt-box { -webkit-transform-style: preserve-3d; -moz-transform-style: preserve-3d; -ms-transform-style: preserve-3d; transform-style: preserve-3d; position: relative; }
.tilt-box .tilt-box-content { -webkit-transform: translateZ(150px); -moz-transform: translateZ(150px); -ms-transform: translateZ(150px); -o-transform: translateZ(150px); transform: translateZ(150px); position: absolute; top: 0; left: 0; height: 100%; width: 100%; display: flex; align-items: center; justify-content: center; font-size: 34px; }

/* split slider */
.home-split-portfolio .swiper-slide .swiper-slide-l { -webkit-transition: 0.5s ease;-o-transition: 0.5s ease;transition: 0.5s ease; -webkit-transform: translateY(100%);-ms-transform: translateY(100%);transform: translateY(100%); z-index: 1; min-height: 600px; }
.home-split-portfolio .swiper-slide .swiper-slide-r { -webkit-transform: translateY(-100%);-ms-transform: translateY(-100%);transform: translateY(-100%); -webkit-transition: 0.5s ease;-o-transition: 0.5s ease;transition: 0.5s ease; min-height: 600px; }
.home-split-portfolio .swiper-slide-active .swiper-slide-l, .home-split-portfolio .swiper-slide-active .swiper-slide-r, .home-split-portfolio .swiper-slide-duplicate-active .swiper-slide-l, .home-split-portfolio .swiper-slide-duplicate-active .swiper-slide-r { -webkit-transform: translateY(0); -ms-transform: translateY(0); transform: translateY(0); }
.home-split-portfolio .slider-split-scroll > .swiper-pagination-bullets { right: 60px; width: auto; left: auto; }

/* blog banner slider */
.slider-blog-banner .swiper-slide { opacity: 0.6; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }
.slider-blog-banner .swiper-slide.swiper-slide-active { opacity: 1; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }
.slider-blog-banner .swiper-bottom-content { opacity: 0; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; -webkit-transform: translateY(50px); -moz-transform: translateY(50px); -ms-transform: translateY(50px); -o-transform: translateY(50px); transform: translateY(50px); -webkit-transition-delay: 0.1s; -moz-transition-delay: 0.1s; -ms-transition-delay: 0.1s; -o-transition-delay: 0.1s; transition-delay: 0.1s; }
.slider-blog-banner .swiper-slide-active .swiper-bottom-content { opacity: 1; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; -webkit-transform: translateY(0); -moz-transform: translateY(0); -ms-transform: translateY(0); -o-transform: translateY(0); transform: translateY(0); -webkit-transition-delay: 0.3s; -moz-transition-delay: 0.3s; -ms-transition-delay: 0.3s; -o-transition-delay: 0.3s; transition-delay: 0.3s; }
.slider-blog-banner .swiper-slide .btn-fancy {padding: 6px 16px; font-size: 12px}

/* swiper custom image bullets */
.slider-custom-image-pagination .swiper-pagination-bullet { background-size: cover; border: 0; -webkit-filter: grayscale(1); filter: grayscale(1); opacity: .8; width: 60px; height: 60px; margin: 0 12px !important; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }
.slider-custom-image-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active { opacity: 1; -webkit-filter: grayscale(0); filter: grayscale(0); -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s;}
.slider-custom-image-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active, .slider-custom-image-pagination .swiper-pagination-bullet:hover {transform: scale(1.1, 1.1); -moz-transform: scale(1.1, 1.1); -webkit-transform: scale(1.1, 1.1); -ms-transform: scale(1.1, 1.1); -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s;}

.white-move .swiper-slide, .white-move .swiper-wrapper { cursor: url("../images/icon-move-light.png"), move; }
.black-move .swiper-slide, .black-move .swiper-wrapper { cursor: url("../images/icon-move-black.png"), move; }

.vertical-white-move .swiper-slide, .vertical-white-move .swiper-wrapper { cursor: url("../images/vertical-icon-move-light.png"), move; }
.vertical-black-move .swiper-slide, .vertical-black-move .swiper-wrapper { cursor: url("../images/vertical-icon-move-black.png"), move; }

.swiper-pagination-medium .swiper-pagination-bullet{ width: 10px; height: 10px; }
.swiper-pagination-medium .swiper-pagination-bullet-active{ width: 16px; height: 16px; }
.swiper-pagination-big .swiper-pagination-bullet { width: 14px; height: 14px; }
.swiper-container-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet { margin: 0 6px; }
.slider-custom-image > .swiper-button-next, .slider-custom-image > .swiper-button-prev  { bottom: 0; top: inherit; left: inherit }

/* swiper vertical pagination */
.swiper-container-vertical>.swiper-pagination-bullets .swiper-pagination-bullet {margin: 10px 0}

/* swiper pagination bottom */
.swiper-pagination-bottom .swiper-wrapper { margin-bottom: 65px; }
.swiper-pagination-bottom.small-bottom .swiper-wrapper { margin-bottom: 50px; }
.swiper-pagination-bottom > .swiper-pagination-bullets { bottom: 0; }
.swiper-pagination-bottom > .swiper-button-next, .swiper-pagination-bottom > .swiper-button-prev  { bottom: 0; top: inherit; left: inherit }

/* swiper pagination light */
.swiper-light-pagination .swiper-pagination-bullet { border: 2px solid #fff; opacity: 0.5; }
.swiper-light-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active { background-color: #fff; opacity: 1; }
.swiper-pagination-medium.swiper-light-pagination .swiper-pagination-bullet { border: 2px solid #fff; background-color: #fff; opacity: 1; }
.swiper-pagination-medium.swiper-light-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active { border-color: #fff; background-color: transparent; }

/* dot pagination dark */
.swiper-pagination-dot .swiper-pagination-bullet { background:#000; border-color: #000; opacity: 0.4; }
.swiper-pagination-dot .swiper-pagination-bullet-active { background:#000; border-color: #000; opacity: 1; }

/* dot pagination light */
.swiper-pagination-dot.swiper-light-pagination .swiper-pagination-bullet { background:#fff; border-color: #fff; opacity: 0.5; }
.swiper-pagination-dot.swiper-light-pagination .swiper-pagination-bullet-active { background:#fff; border-color: #fff; opacity: 1; }

/* square pagination */
.swiper-pagination-square .swiper-pagination-bullet { height:3px; width:30px; border-radius: 0;opacity: 0.3; cursor: pointer; }
.swiper-pagination-square .swiper-pagination-bullet-active { opacity: 1; }

/* slider navigation */
.swiper-next.swiper-button-disabled, .swiper-prev.swiper-button-disabled { opacity: 0.6; }
.swiper-button-next, .swiper-button-prev { height: 40px; width: 40px; line-height: 40px; margin: 0; transform: translateY(-50%); -webkit-transform: translateY(-50%); -moz-transform: translateY(-50%); -ms-transform: translateY(-50%); -o-transform: translateY(-50%); -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; outline: none; }
.swiper-button-next.large, .swiper-button-prev.large { height: 62px; width: 62px; line-height: 62px; font-size: 18px; }
.swiper-button-next:after, .swiper-button-prev:after { display: none; }
.swiper-button-next:hover, .swiper-button-prev:hover { -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }

.swiper-button-next { right: 0; }
.swiper-button-prev { left: 0; }

/* slider navigation style 01 */
.slider-navigation-style-01.swiper-button-next, .slider-navigation-style-01.swiper-button-prev { background-color: rgba(0,0,0,.5); color: #fff; }
.slider-navigation-style-01.swiper-button-next:hover, .slider-navigation-style-01.swiper-button-prev:hover { background-color: #fff; color: #232323; }
.slider-navigation-style-01.swiper-button-next.light, .slider-navigation-style-01.swiper-button-prev.light { background-color: #fff; color: #232323; }
.slider-navigation-style-01.swiper-button-next.light:hover, .slider-navigation-style-01.swiper-button-prev.light:hover { background-color: #232323; color: #fff; }
.slider-navigation-style-01.swiper-button-next.dark, .slider-navigation-style-01.swiper-button-prev.dark { background-color: #232323; color: #fff; }
.slider-navigation-style-01.swiper-button-next.dark:hover, .slider-navigation-style-01.swiper-button-prev.dark:hover { background-color: #fff; color: #232323; }
.slider-navigation-style-01.swiper-button-next.rounded-circle { right: 40px;}
.slider-navigation-style-01.swiper-button-prev.rounded-circle { left: 40px;}

/* slider navigation style 02 */
.slider-navigation-style-02.swiper-button-next { right: -90px; }
.slider-navigation-style-02.swiper-button-prev { left: -90px; }
.slider-navigation-style-02.swiper-button-next, .slider-navigation-style-02.swiper-button-prev { border: 2px solid rgba(0,0,0,.15); font-size: 16px; color: #232323; height: 45px; width: 45px; line-height: 45px; transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out; }
.slider-navigation-style-02.swiper-button-prev:hover, .slider-navigation-style-02.swiper-button-next:hover { background: #232323; color: #fff; border: 1px solid #fff;}
.slider-navigation-style-02.swiper-button-next.light, .slider-navigation-style-02.swiper-button-prev.light { border-color: rgba(255,255,255,0.3); color: #fff; font-size: 12px; line-height: 42px }
.slider-navigation-style-02.swiper-button-next.light:hover, .slider-navigation-style-02.swiper-button-prev.light:hover { background-color: #fff; color: #232323;}

/* slider navigation style 03 */
.slider-navigation-style-03.swiper-button-next, .slider-navigation-style-03.swiper-button-prev { border: 1px solid #e4e4e4; font-size: 16px; color: #232323; line-height: 40px; transform: none; top: inherit; transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out; }
.slider-navigation-style-03.swiper-button-next:hover, .slider-navigation-style-03.swiper-button-prev:hover { box-shadow: 0 0 20px rgba(0,0,0,.15); border-color: #ffffff; background: #ffffff; }
.slider-navigation-style-03.swiper-button-next.light, .slider-navigation-style-03.swiper-button-prev.light { border: 2px solid #fff; font-size: 16px; color: #fff; line-height: 38px }
.slider-navigation-style-03.swiper-button-next.light:hover, .slider-navigation-style-03.swiper-button-prev.light:hover { background-color: #fff; color: #232323; }
.slider-navigation-style-03.swiper-button-next.white-transparent, .slider-navigation-style-03.swiper-button-prev.white-transparent { box-shadow: 0 0 10px rgba(0,0,0,.15); border: 0 solid #fff; background-color: rgba(255, 255, 255, 0.3); color: #828282; }
.slider-navigation-style-03.swiper-button-next.white-transparent:hover, .slider-navigation-style-03.swiper-button-prev.white-transparent:hover { background-color: #fff; color: #232323; }
.slider-navigation-style-03.swiper-button-next { right: inherit; left: 65px; }
.slider-navigation-style-03.swiper-button-prev { left: 15px; }
.slider-navigation-style-03.swiper-button-next.center-next { left: calc(50% + 5px); bottom: 15px; }
.slider-navigation-style-03.swiper-button-prev.center-prev { left: calc(50% - 45px); bottom: 15px; }
.slider-navigation-style-03.swiper-button-next.white, .slider-navigation-style-03.swiper-button-prev.white { box-shadow: 0 0 10px rgba(0,0,0,.07); border:none; background-color: #fff; color: #232323; }
.slider-navigation-style-03.swiper-button-next.white:hover, .slider-navigation-style-03.swiper-button-prev.white:hover { background-color: #fff; color: #232323;  box-shadow: 0 0 20px rgba(0,0,0,.15);}

/* slider navigation style 04 */
.slider-navigation-style-04.swiper-button-prev { left: -30%; }
.slider-navigation-style-04.swiper-button-next { right: -30%; }

/* slider navigation style 05 */
.slider-navigation-style-05.swiper-button-next, .slider-navigation-style-05.swiper-button-prev { font-size: 40px; color: #fff; height: 50px; width: 50px; line-height: 50px; }
.slider-navigation-style-05.swiper-button-next:hover, .slider-navigation-style-05.swiper-button-prev:hover { opacity: 0.5}
.slider-navigation-style-05.swiper-button-next { right: 40px;}
.slider-navigation-style-05.swiper-button-prev { left: 40px;}

/* slider navigation style 06 */
.slider-navigation-style-06.swiper-button-next, .slider-navigation-style-06.swiper-button-prev { font-size: 40px; color: #828282; line-height: 40px; transform: none; top: inherit; transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out; width: auto; height: 40px; }
.slider-navigation-style-06.swiper-button-next:hover, .slider-navigation-style-06.swiper-button-prev:hover { color: #232323;}
.slider-navigation-style-06.swiper-button-next { right: inherit; left: 80px; }
.slider-navigation-style-06.swiper-button-prev { left: 15px; }

/* slider navigation style 07 */
.slider-navigation-style-07.swiper-button-next, .slider-navigation-style-07.swiper-button-prev { background-color: rgba(23,23,23,.5); color: #fff; }
.slider-navigation-style-07.swiper-button-next:hover, .slider-navigation-style-07.swiper-button-prev:hover { background-color: #fff; color: #232323; }
.slider-navigation-style-07.swiper-button-next.light, .slider-navigation-style-07.swiper-button-prev.light { background-color: #fff; color: #232323; }
.slider-navigation-style-07.swiper-button-next.light:hover, .slider-navigation-style-07.swiper-button-prev.light:hover { background-color: #232323; color: #fff; }
.slider-navigation-style-07.swiper-button-next.light { right: -70px;}
.slider-navigation-style-07.swiper-button-prev.light { left: -70px;}
.slider-navigation-style-07.swiper-button-next { right: 40px;}
.slider-navigation-style-07.swiper-button-prev { left: 40px;}

/* slider navigation style 08 */
.slider-navigation-style-08.swiper-button-next, .slider-navigation-style-08.swiper-button-prev { background-color: rgba(0,0,0,.5); color: #fff; }
.slider-navigation-style-08.swiper-button-next:hover, .slider-navigation-style-08.swiper-button-prev:hover { background-color: #fff; color: #232323; }
.slider-navigation-style-08.swiper-button-next.light, .slider-navigation-style-08.swiper-button-prev.light { background-color: #fff; color: #232323; }
.slider-navigation-style-08.swiper-button-next.light:hover, .slider-navigation-style-08.swiper-button-prev.light:hover { background-color: #232323; color: #fff; }
.slider-navigation-style-08.swiper-button-next.dark, .slider-navigation-style-08.swiper-button-prev.dark { background-color: #232323; color: #fff; }
.slider-navigation-style-08.swiper-button-next.dark:hover, .slider-navigation-style-08.swiper-button-prev.dark:hover { background-color: #fff; color: #232323; }
.slider-navigation-style-08.swiper-button-next { width: 50px; height: 50px; right: 15px;}
.slider-navigation-style-08.swiper-button-prev { width: 50px; height: 50px; left: 15px;}
.slider-navigation-style-08.swiper-button-next.rounded-circle { right: 40px;}
.slider-navigation-style-08.swiper-button-prev.rounded-circle { left: 40px;}

/* slider arrow right bottom */
.slider-arrow-rb { position: absolute; bottom: 0; right: 100%; z-index: 1; }
.slider-arrow-rb .swiper-button-next, .slider-arrow-rb .swiper-button-prev { transform: none; position: static; margin-top: 1px; }

/* swiper custom text */
.slider-custom-text-next.swiper-button-next, .slider-custom-text-prev.swiper-button-prev { padding: 25px 0 25px 0; width: auto; height: auto;}
.slider-custom-text-next.swiper-button-next:hover, .slider-custom-text-prev.swiper-button-prev:hover {opacity: .7}
.slider-custom-text-next.swiper-button-next { border-left:  1px solid #E4E4E4; padding-left: 45px;}
.slider-custom-text-prev.swiper-button-prev { border-right:  1px solid #E4E4E4; padding-right: 45px;}

/* slider image thumbs */
.slider-review-image-thumbs { height: auto; }
.slider-review-image-thumbs .swiper-slide:not(.swiper-slide-prev):not(.swiper-slide-active):not(.swiper-slide-next) { visibility: hidden; opacity: 0; }
.slider-review-image-thumbs .swiper-slide:not(.swiper-slide-active) img { transform: scale(0.7); -webkit-transform: scale(0.7); -moz-transform: scale(0.7); -ms-transform: scale(0.7); }
.slider-review-image-thumbs img { border: 6px solid transparent; margin: 0 auto; transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out; }
.slider-review-image-thumbs .swiper-slide-active img { box-shadow: 0 0 20px rgba(0,0,0,.14); border: 6px solid #fff; }
.slider-review-image-thumbs .swiper-slide { cursor: pointer; }

/* ===================================
    21. Grid
====================================== */

.blog-post-image img, .portfolio-image img { width: 100%; }
.grid { list-style: none; }
.grid-sizer { padding: 0 !important; margin: 0 !important; }

/* gutter size */
.grid.gutter-very-small li { padding: 3px 3px; }
.grid.gutter-small li { padding:5px 5px; }
.grid.gutter-medium li { padding:7px 7px; }
.grid.gutter-large li { padding:10px 10px; }
.grid.gutter-extra-large li { padding:15px; }
.grid.gutter-double-extra-large li { padding:20px; }
.grid.gutter-triple-extra-large li { padding:30px; }

/* grid 6 column */
.grid.grid-6col li { width: 16.67%; list-style: none; }
.grid.grid-6col li.grid-item-double { width: 33.33%; }

/* grid 5 column */
.grid.grid-5col li { width: 20%; list-style: none; }
.grid.grid-5col li.grid-item-double { width: 40%; }

/* grid 4 column */
.grid.grid-4col li { width: 25%; list-style: none; }
.grid.grid-4col li.grid-item-double { width: 50%; }

/* grid 3 column */
.grid.grid-3col li { width: 33.33%; float: left; list-style: none; }
.grid.grid-3col li.grid-item-double { width: 66.67%; }

/* grid 2 column */
.grid.grid-2col li { width: 50%; list-style: none; }
.grid.grid-2col li.grid-item-double { width: 100%; }

/* grid 1 column */
.grid.grid-1col li { width: 100%;  list-style: none; }

/* grid filter */
.grid-filter li { display: inline-block; float: none; padding: 0 20px; border: 0; position: relative; }
.grid-filter li:first-child { padding-left: 0; }
.grid-filter li:last-child { padding-right: 0; }
.grid-filter li a { border: 0; border-bottom: 1px solid; border-color: transparent; color:#828282; padding-bottom: 2px; }
.grid-filter > li.active > a, .grid-filter > li.active > a:focus, .grid-filter > li.active > a:hover, .grid-filter > li > a:hover { border: 0; color: inherit; border-bottom: 1px solid; color: #232323 }

/* ===================================
    22. Portfolio
====================================== */

.portfolio-box .portfolio-image { position: relative; overflow: hidden; }
.portfolio-box .portfolio-hover { position: absolute; width:100%; height:100%; left: 0; top: 0; opacity: 0; }
.portfolio-box:hover .portfolio-hover { opacity: 1; }
.portfolio-box .portfolio-hover .portfolio-icon { line-height: 50px; display: inline-block; vertical-align: middle; text-align: center; margin: 0 5px; }
.portfolio-box *, .portfolio-box:hover * { transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; }
.portfolio-box .move-top-bottom { opacity: 0; transform: translateY(-15px); -moz-transform: translateY(-15px); -webkit-transform: translateY(-15px); -ms-transform: translateY(-15px); transition-property: transform, opacity; -moz-transition-property: transform, opacity; -webkit-transition-property: transform, opacity; -ms-transition-property: transform, opacity; }
.portfolio-box .move-bottom-top { opacity: 0; transform: translateY(15px); -moz-transform: translateY(15px); -webkit-transform: translateY(15px); -ms-transform: translateY(15px); transition-property: transform, opacity; -moz-transition-property: transform, opacity; -webkit-transition-property: transform, opacity; -ms-transition-property: transform, opacity; }
.portfolio-box:hover .move-top-bottom, .portfolio-box:hover .move-bottom-top { opacity: 1; transform: translateY(0px); -moz-transform: translateY(0px); -webkit-transform: translateY(0px); -ms-transform: translateY(0px); }
.portfolio-box .move-left-right { opacity: 0; transform: translateX(-15px); -moz-transform: translateX(-15px); -webkit-transform: translateX(-15px); -ms-transform: translateX(-15px); transition-property: transform, opacity; -moz-transition-property: transform, opacity; -webkit-transition-property: transform, opacity; -ms-transition-property: transform, opacity; }
.portfolio-box .move-right-left { opacity: 0; transform: translateX(15px); -moz-transform: translateX(15px); -webkit-transform: translateX(15px); -ms-transform: translateX(15px); transition-property: transform, opacity; -moz-transition-property: transform, opacity; -webkit-transition-property: transform, opacity; -ms-transition-property: transform, opacity; }
.portfolio-box:hover .move-left-right, .portfolio-box:hover .move-right-left { opacity: 1; transform: translateX(0px); -moz-transform: translateX(0px); -webkit-transform: translateX(0px); -ms-transform: translateX(0px); }
.portfolio-classic .portfolio-box:hover .portfolio-image img, .portfolio-overlay .portfolio-box:hover .portfolio-image img { transform: scale(1.1, 1.1); -moz-transform: scale(1.1, 1.1); -webkit-transform: scale(1.1, 1.1); -ms-transform: scale(1.1, 1.1); opacity: 0.15; }
.portfolio-box .move-bottom-top-self {overflow: hidden}
.portfolio-box .move-bottom-top-self span { opacity: 0; display: block; will-change: transform; -moz-transform: translateY(200%); -ms-transform: translateY(200%); -webkit-transform: translateY(200%); transform: translateY(200%); -moz-transition: all .5s cubic-bezier(0.37, 0.31, 0.2, 0.85); -o-transition: all .5s cubic-bezier(0.37, 0.31, 0.2, 0.85); -webkit-transition: all .5s cubic-bezier(0.37, 0.31, 0.2, 0.85); transition: all .5s cubic-bezier(0.37, 0.31, 0.2, 0.85);}
.portfolio-box:hover .move-bottom-top-self span {opacity: 1; -moz-transform: translateY(0); -ms-transform: translateY(0); -webkit-transform: translateY(0); transform: translateY(0);}
.portfolio-box .move-top-bottom-self {overflow: hidden}
.portfolio-box .move-top-bottom-self span { opacity: 0; display: block; will-change: transform; -moz-transform: translateY(-200%); -ms-transform: translateY(-200%); -webkit-transform: translateY(-200%); transform: translateY(-200%); -moz-transition: all .5s cubic-bezier(0.37, 0.31, 0.2, 0.85); -o-transition: all .5s cubic-bezier(0.37, 0.31, 0.2, 0.85); -webkit-transition: all .5s cubic-bezier(0.37, 0.31, 0.2, 0.85); transition: all .5s cubic-bezier(0.37, 0.31, 0.2, 0.85);}
.portfolio-box:hover .move-top-bottom-self span {opacity: 1; -moz-transform: translateY(0); -ms-transform: translateY(0); -webkit-transform: translateY(0); transform: translateY(0);}
.portfolio-box .scale {opacity: 0; -moz-transform: scale3d(0.8, 0.8, 1); -o-transform: scale3d(0.8, 0.8, 1); -ms-transform: scale3d(0.8, 0.8, 1); -webkit-transform: scale3d(0.8, 0.8, 1); transform: scale3d(0.8, 0.8, 1);}
.portfolio-box:hover .scale {opacity: 1; -moz-transform: scale3d(1, 1, 1); -o-transform: scale3d(1, 1, 1); -ms-transform: scale3d(1, 1, 1); -webkit-transform: scale3d(1, 1, 1); transform: scale3d(1, 1, 1); }

/* portfolio classic */
.portfolio-classic .portfolio-box .portfolio-image > a { position: relative; height: 100%; z-index: 1; }
.portfolio-classic .portfolio-icon a { width: 40px; height: 40px; display: inline-block; vertical-align: middle; line-height: 40px; text-align: center; margin: 0 3px; position: relative; z-index: 3;}
.portfolio-classic .portfolio-box {overflow: hidden; transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out;}
.portfolio-classic .portfolio-box:hover {transform: translateY(-5px); -moz-transform: translateY(-5px); -webkit-transform: translateY(-5px); -ms-transform: translateY(-5px); }
.portfolio-classic .portfolio-box:hover .portfolio-image img { transform: scale(1, 1); -moz-transform: scale(1, 1); -webkit-transform: scale(1, 1); -ms-transform: scale(1, 1); }
.portfolio-classic .portfolio-icon a:hover { box-shadow: 0 0 25px rgba(0,0,0,.3);}
.portfolio-classic .portfolio-box .portfolio-icon a { opacity: 0; transform: translateY(15px); -moz-transform: translateY(15px); -webkit-transform: translateY(15px); -ms-transform: translateY(15px);}
.portfolio-classic .portfolio-box:hover .portfolio-icon a {opacity: 1; transition-delay: .1s;  transform: translateY(0); -moz-transform: translateY(0); -webkit-transform: translateY(0); -ms-transform: translateY(0); }
.portfolio-classic .portfolio-box:hover .portfolio-icon a:last-child {transition-delay: .2s;}

/* portfolio bordered */
.portfolio-bordered .portfolio-box .portfolio-hover { opacity: 0; }
.portfolio-bordered .portfolio-box:hover .portfolio-hover { left: 20px; opacity: 1; top: 20px; width: calc(100% - 40px); height: calc(100% - 40px); }
.portfolio-bordered .portfolio-box:hover .portfolio-image img { opacity: .5; -webkit-filter: blur(5px); filter: blur(5px); transform: scale(1.1, 1.1); -moz-transform: scale(1.1, 1.1); -webkit-transform: scale(1.1, 1.1); -ms-transform: scale(1.1, 1.1); }

/* portfolio boxed */
.portfolio-boxed .portfolio-box .portfolio-hover { opacity: 0; left: 20px; bottom: 20px; width: calc(100% - 40px); height:auto; top: inherit; transform: translateY(100%); -moz-transform: translateY(100%); -webkit-transform: translateY(100%); -ms-transform: translateY(100%);  }
.portfolio-boxed .portfolio-box:hover .portfolio-hover { box-shadow: 0 0 15px rgba(0,0,0,.20); transform: translateY(0); -moz-transform: translateY(0); -webkit-transform: translateY(0); -ms-transform: translateY(0); opacity: 1;  }
.portfolio-boxed .portfolio-box .portfolio-hover:hover i {transform: translateX(5px); -moz-transform: translateX(5px); -webkit-transform: translateX(5px); -ms-transform: translateX(5px);}
.portfolio-boxed .portfolio-box:hover img { -webkit-transform: scale(1.1); -moz-transform: scale(1.1); transform: scale(1.1); -webkit-transition: -webkit-transform 3s cubic-bezier(.165,.84,.44,1); -moz-transition: -moz-transform 3s cubic-bezier(.165,.84,.44,1); transition: transform 3s cubic-bezier(.165,.84,.44,1); }

/* portfolio switch image */
.portfolio-switch-image .portfolio-image .portfolio-switch-image { position: absolute; top: 0; left: 0; width: 100%; height: 100%; opacity: 0; -moz-transform: scale3d(1.1, 1.1, 1.1); -o-transform: scale3d(1.1, 1.1, 1.1); -ms-transform: scale3d(1.1, 1.1, 1.1); -webkit-transform: scale3d(1.1, 1.1, 1.1); transform: scale3d(1.1, 1.1, 1.1); }
.portfolio-switch-image .portfolio-box:hover .portfolio-switch-image { opacity: 1; -moz-transform: scale3d(1, 1, 1); -o-transform: scale3d(1, 1, 1); -ms-transform: scale3d(1, 1, 1); -webkit-transform: scale3d(1, 1, 1); transform: scale3d(1, 1, 1);}
.portfolio-switch-image .portfolio-box:hover .portfolio-image{ -moz-transform: translate3d(0, -5px, 0); -o-transform: translate3d(0, -5px, 0); -ms-transform: translate3d(0, -5px, 0); -webkit-transform: translate3d(0, -5px, 0); transform: translate3d(0, -5px, 0);  }
.portfolio-switch-image .portfolio-box:hover .portfolio-caption {transform: translateY(-10px); -moz-transform: translateY(-10px); -webkit-transform: translateY(-10px); -ms-transform: translateY(-10px);}

/* portfolio scattered */
.portfolio-scattered .portfolio-image { -webkit-clip-path: inset(0 0 0 0); clip-path: inset(0 0 0 0); }
.portfolio-scattered .portfolio-box:hover .portfolio-image { -webkit-clip-path: inset(5% 5% 5% 5%); clip-path: inset(5% 5% 5% 5%); }
.portfolio-scattered.row-cols-lg-2 .col:nth-child(4n+0) .portfolio-box, .portfolio-scattered.row-cols-lg-2 .col:nth-child(4n+1) .portfolio-box { padding: 15% 0; }
.portfolio-scattered.row-cols-lg-2 .col:nth-child(4n+2) .portfolio-box, .portfolio-scattered.row-cols-lg-2 .col:nth-child(4n+3) .portfolio-box { padding: 0 15%; }
.portfolio-scattered.row-cols-lg-3 .col:nth-child(6n+0) .portfolio-box, .portfolio-scattered.row-cols-lg-3 .col:nth-child(6n+1) .portfolio-box, .portfolio-scattered.row-cols-lg-3 .col:nth-child(6n+3) .portfolio-box, .portfolio-scattered.row-cols-lg-3 .col:nth-child(6n+5) .portfolio-box { padding: 15% 0; }
.portfolio-scattered.row-cols-lg-3 .col:nth-child(6n+2) .portfolio-box, .portfolio-scattered.row-cols-lg-3 .col:nth-child(6n+4) .portfolio-box, .portfolio-scattered.row-cols-lg-3 .col:nth-child(6n+6) .portfolio-box { padding: 0 15%; }
.pagination-style-01 .page-link.portfolio-scattered.row-cols-xl-4 .col:nth-child(8n+0) .portfolio-box, .portfolio-scattered.row-cols-xl-4 .col:nth-child(8n+1) .portfolio-box, .portfolio-scattered.row-cols-xl-4 .col:nth-child(8n+3) .portfolio-box, .portfolio-scattered.row-cols-xl-4 .col:nth-child(8n+6) .portfolio-box{ padding: 25% 0; }
.portfolio-scattered.row-cols-xl-4 .col:nth-child(8n+2) .portfolio-box, .portfolio-scattered.row-cols-xl-4 .col:nth-child(8n+4) .portfolio-box, .portfolio-scattered.row-cols-xl-4 .col:nth-child(8n+5) .portfolio-box, .portfolio-scattered.row-cols-xl-4 .col:nth-child(8n+7) .portfolio-box { padding: 0 15%; }

/* portfolio overlay */
.portfolio-overlay .portfolio-box .portfolio-image img { opacity: 1;}

/* portfolio justified gallery */
.justified-gallery > a > .jg-caption.jg-caption-visible, .justified-gallery > div > .jg-caption.jg-caption-visible, .justified-gallery > figure > .jg-caption.jg-caption-visible { display: none; filter: "alpha(opacity=100)"; opacity: 1; -webkit-transition: opacity 500ms ease-in; -moz-transition: opacity 500ms ease-in; -o-transition: opacity 500ms ease-in; transition: opacity 500ms ease-in; }
.justified-gallery > a > .jg-caption, .justified-gallery > div > .jg-caption, .justified-gallery > figure > .jg-caption { display: none; position: absolute; left: 0; right: 0; bottom: 0; margin: 0; background-color: #fff; color: inherit; padding: 15px 25px 12px; box-shadow: 0 5px 20px rgba(23,23,23,.2); white-space: nowrap; z-index: 1; font-family: inherit; }

/* ===================================
    23. Blog
====================================== */

.blog-post { position: relative; overflow: hidden; }
.blog-post .blog-post-image img { -webkit-transition: all 1s cubic-bezier(0,0,.2,1); -moz-transition: all 1s cubic-bezier(0,0,.2,1); -ms-transition: all 1s cubic-bezier(0,0,.2,1); transition: all 1s cubic-bezier(0,0,.2,1); }
.blog-post .blog-post-image { overflow: hidden; position: relative; }
.blog-post:hover .blog-post-image img { opacity: 0.6; -webkit-transform: scale3d(1.1,1.1,1.1); -moz-transform: scale3d(1.1,1.1,1.1); -ms-transform: scale3d(1.1,1.1,1.1); transform: scale3d(1.1,1.1,1.1); }

/* blog grid */
.blog-grid .blog-post-image .blog-category { position: absolute; top: 23px; right: 23px; font-size: 11px; line-height: 14px; background: rgba(23,23,23,.75); color: #fff; text-transform: uppercase; border-radius: 2px; padding: 6px 13px; transition: all 0.5s ease-in-out; -webkit-transition: all 0.5s ease-in-out; -moz-transition: all 0.5s ease-in-out; -ms-transition: all 0.5s ease-in-out; -o-transition: all 0.5s ease-in-out; }
.blog-grid .blog-post:hover .blog-post-image .blog-category { background: #fff; color: #232323; transition: all 0.5s ease-in-out; -webkit-transition: all 0.5s ease-in-out; -moz-transition: all 0.5s ease-in-out; -ms-transition: all 0.5s ease-in-out; -o-transition: all 0.5s ease-in-out; box-shadow: 0 0 10px rgba(0,0,0,.2) }
.blog-grid .avtar-image {width: 30px; border-radius: 100%; margin-right: 10px;}
.blog-grid .blog-like {display: -ms-flexbox; display: flex; -ms-flex-pack: center; justify-content: center; -ms-flex-align: center; align-items: center;}
.blog-grid .blog-like i {margin-right: 4px;}

/* blog masonry */
.blog-masonry .blog-post { box-shadow: 0 0 15px rgba(0,0,0,.06); -webkit-transition: all 1.5s cubic-bezier(0,0,.2,1); -moz-transition: all 1.5s cubic-bezier(0,0,.2,1); -ms-transition: all 1.5s cubic-bezier(0,0,.2,1); transition: all 1.5s cubic-bezier(0,0,.2,1); }
.blog-masonry .blog-post:hover { box-shadow: 0 0 30px rgba(0,0,0,.15); -webkit-transform: translate3d(0, 0, 0); }
.blog-masonry .blog-post:hover .blog-post-image img {opacity: 1}
.blog-masonry .blog-post-image .blog-category {position: absolute; background: #fff; left: 0; bottom: 0; top: auto; right: auto; border-radius: 0px;  padding: 13px 25px; font-size: 13px; line-height: 13px; font-weight: 500}
.blog-masonry .blog-post:hover .blog-post-image .blog-category {box-shadow: none}
.blog-masonry .blog-post p {margin-bottom: 5px}
.blog-masonry .blog-like span, .blog-masonry .blog-comment span {position: relative; top:0}
.blog-masonry .blog-like, .blog-masonry .blog-comment {display: -ms-flexbox; display: flex; -ms-flex-pack: center; justify-content: center; -ms-flex-align: center; align-items: center;}
.blog-masonry .blog-like i, .blog-masonry .blog-comment i { margin-right: 4px; }
.blog-masonry .blog-comment { margin-left: 13px; }

/* blog classic */
.blog-classic .blog-post:hover { -webkit-transform: translate3d(0, 0, 0); transform: translate3d(0, 0, 0); }
.blog-classic .blog-post-image { transition: all 0.5s ease-in-out; -webkit-transition: all 0.5s ease-in-out; -moz-transition: all 0.5s ease-in-out; -ms-transition: all 0.5s ease-in-out; -o-transition: all 0.5s ease-in-out; }
.blog-classic .blog-post:hover .blog-post-image img { opacity: 1; -webkit-transform: scale3d(1,1,1); transform: scale3d(1,1,1);  }
.blog-classic .separator { width: 28px; height: 1px;display: inline-block; vertical-align: middle; margin-right: 8px; -webkit-transition: all 1s cubic-bezier(0,0,.2,1); transition: all 1s cubic-bezier(0,0,.2,1); }
.blog-classic .blog-post:hover .separator { width: 50px; }
.blog-classic .blog-post p { margin-bottom: 20px}

/* blog simple */
.blog-simple .blog-post { display: -webkit-box; display: -ms-flexbox; display: flex; }
.blog-simple .blog-post-image, .blog-simple .post-details { width: 50%; }
.blog-simple .post-details { display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; -webkit-box-orient: vertical; -webkit-box-direction: normal; -ms-flex-direction: column; flex-direction: column; }
.blog-simple .blog-post .blog-category {  font-size: 11px; line-height: 14px; box-shadow: 0 0 20px rgba(23,23,23,.08); border-radius: 20px; border: 1px solid; padding: 6px 16px; text-transform: uppercase; font-weight: 500}
.blog-simple .blog-post:hover .blog-category {box-shadow: 0 0 0 rgba(23,23,23,.08);}
.blog-simple .blog-post .blog-post-image .blog-post-image-overlay { opacity: 0; position: absolute; top: 0; left: 0; width: 100%; height: 100%; transition: all 0.5s ease-in-out; -webkit-transition: all 0.5s ease-in-out; -moz-transition: all 0.5s ease-in-out; -ms-transition: all 0.5s ease-in-out; -o-transition: all 0.5s ease-in-out; }
.blog-simple .blog-post .cover-background {background-position: right center !important; transition: all 0.5s ease-in-out; -webkit-transition: all 0.5s ease-in-out; -moz-transition: all 0.5s ease-in-out; -ms-transition: all 0.5s ease-in-out; -o-transition: all 0.5s ease-in-out;}
.blog-simple .blog-post:hover .cover-background {background-position: left center !important; }
.blog-simple .blog-post:hover .blog-post-image .blog-post-image-overlay { opacity: .3;}

/* blog side image */
.blog-side-image .blog-post { padding: 28px; }
.blog-side-image .blog-post:nth-child(odd) { padding-right: 0; }
.blog-side-image .blog-post:nth-child(even) { padding-left: 0; }
.blog-side-image .blog-post-image { width: 55%; }
.blog-side-image .post-details { width: 45%; }
.blog-side-image .post-details i {margin-right: 7px;}
.blog-side-image .post-details i:last-of-type {margin-left: 20px;}
.blog-side-image .blog-post:hover .blog-post-image img {opacity: 1}
.blog-side-image .blog-post:nth-child(even) .post-details { -ms-flex-order: 1; order: 1; }
.blog-side-image .blog-post:nth-child(even) .blog-post-image { -ms-flex-order: 2; order: 2; }
.blog-side-image .avtar-image { width: 25px; border-radius: 100%; margin-right: 10px; }
.blog-side-image .separator {margin-left: 15px; margin-right: 15px; width: 30px; display: inline-block; vertical-align: middle; height: 1px }

/* blog metro */
.blog-metro .blog-post:hover { -webkit-transform: translate3d(0, 0, 0); transform: translate3d(0, 0, 0); }
.blog-metro .blog-post:hover .blog-post-image img { opacity: .6; }
.blog-metro .blog-post .blog-post-image .blog-overlay { position: absolute; top: 0; left: 0; height: 100%; width: 100%; background: rgb(31,35,44); background: linear-gradient(0deg, rgba(23,23,23,0.95) 0%, rgba(23,23,23,0) 88%); transition: all 0.5s ease-in-out; -moz-transition: all 0.5s ease-in-out; -webkit-transition: all 0.5s ease-in-out; -ms-transition: all 0.5s ease-in-out; }
.blog-metro .blog-post .post-details { position: absolute; top: 0; left: 0; height: 100%; width: 100%; }
.blog-metro .post-details .blog-category { font-size: 12px; line-height: 20px; font-weight: 500; text-transform: uppercase; color: #fff; padding: 6px 13px; background: rgba(20,21,22,.7); border-radius: 2px; }
.blog-metro .post-details:hover .blog-category {background: #fff; color: #232323; box-shadow: 0 0 20px rgba(23,23,23,.2);}

/* blog overlay image */
.blog-overlay-image { z-index: 1; }
.blog-overlay-image .blog-post { z-index: 1; }
.blog-overlay-image .post-details { transition: all 0.5s ease-in-out; -moz-transition: all 0.5s ease-in-out; -webkit-transition: all 0.5s ease-in-out; -ms-transition: all 0.5s ease-in-out; position: relative; }
.blog-overlay-image .blog-post .blog-post-image, .blog-overlay-image .blog-post .blog-post-image .blog-overlay-image { transform: scale(1.1,1.1); opacity: 0; z-index: -1; position: absolute; height: 100%; width: 100%; top: 0; left: 0; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }
.blog-overlay-image .blog-post .blog-post-image .blog-overlay-image { z-index: 1; }
.blog-overlay-image .blog-post:hover .blog-post-image, .blog-overlay-image .blog-post:hover .blog-post-image .blog-overlay-image { transform: scale(1,1); opacity: 1; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }
.blog-overlay-image .blog-post:hover .post-details * { color: #fff; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }
.blog-overlay-image .blog-category, .blog-overlay-image .blog-post .post-details .post-date, .blog-overlay-image .blog-post .post-details .post-read, .blog-overlay-image .blog-post .post-details .post-title { transition: all 0.4s ease-in-out; -moz-transition: all 0.4s ease-in-out; -webkit-transition: all 0.4s ease-in-out; -ms-transition: all 0.4s ease-in-out; }
.blog-overlay-image .blog-category { border-radius: 2px; padding: 6px 13px; border:1px solid; text-transform: uppercase; display: inline-block; font-size: 11px; line-height: 14px;  }
.blog-overlay-image .blog-post:hover .blog-category { background: #232323; color: #fff !important; border-color: #232323 !important  }
.blog-overlay-image .blog-post .post-details .post-date { opacity: 0; margin-bottom: -20px; transform: scale(1,0); }
.blog-overlay-image .blog-post:hover .post-details .post-date { opacity: .75; margin-bottom: 15px; transform: scale(1,1); }
.blog-overlay-image .blog-post .post-details .post-read { opacity: 1; transform: scale(1,1); }
.blog-overlay-image .blog-post:hover .post-details .post-read { opacity: 0; transform: scale(1,0); }
.blog-overlay-image .blog-post:hover .post-details .post-title { margin-bottom: -20px; position: relative; z-index: 4 }

/* blog modern */
.blog-modern .blog-category { display: inline-block;}
.blog-modern .blog-post:hover .blog-post-image img { -webkit-transform: scale3d(1,1,1); transform: scale3d(1,1,1);  }
.blog-modern .blog-post { overflow: visible; }
.blog-modern .blog-post-image { margin-bottom: 150px; }
.blog-modern .post-details { position: absolute; bottom: -100px; left: 30px; width: calc(100% - 60px); box-shadow: 0 0 30px rgba(0,0,0,.08); -webkit-transition: all 0.5s cubic-bezier(0,0,.2,1); transition: all 0.5s cubic-bezier(0,0,.2,1);}
.blog-modern .grid-item:hover .post-details { transform: translateY(-20px);  }
.blog-modern .grid-item:hover .post-details {box-shadow: 0 0 40px rgba(0,0,0,.08);}

/* blog standard */
.blog-hover-btn *{ transition-duration: 0.35s; -moz-transition-duration: 0.35s; -webkit-transition-duration: 0.35s; -ms-transition-duration: 0.35s; }
.blog-hover-btn a { position: relative; padding-left: 19px; }
.blog-hover-btn .blog-icon{ position: absolute; left: 0; top: 2px; transform: translateY(0); -moz-transform: translateY(0); -webkit-transform: translateY(0); -ms-transform: translateY(0); }
.blog-hover-btn .blog-icon-hover{ opacity: 0; transform: translateY(10px); -moz-transform: translateY(10px); -webkit-transform: translateY(10px); -ms-transform: translateY(10px); }
.blog-hover-btn:hover .blog-icon{ opacity: 0; transform: translateY(-10px); -moz-transform: translateY(-10px); -webkit-transform: translateY(-10px); -ms-transform: translateY(-10px); }
.blog-hover-btn:hover .blog-icon-hover{ opacity: 1; transform: translateY(0); -moz-transform: translateY(0); -webkit-transform: translateY(0); -ms-transform: translateY(0); }

/* blog widget */
.blog-widget { list-style: none; padding: 0; margin: 0; }
.blog-widget li figure { height:auto; width: 140px; margin-bottom: 0; }
.blog-widget li .media-body { line-height: normal; padding-left: 30px; position: relative; top: -1px; }

.blog-post-image .blog-plus-icon { opacity: 0; transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; }
.blog-post:hover .blog-plus-icon { opacity: 1; }
.blog-post-image .blog-rounded-icon { width: 46px; height: 46px; line-height: 48px; border: 1px solid; border-radius: 100%; text-align: center; opacity: 0; margin-top: 15px; transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; }
.blog-post:hover .blog-rounded-icon { opacity: 1; margin-top: 0; }
.blog-image img { width: 100%; }
.blog-text .blog-details-overlap { position: absolute; transform: translate(-50% , -50%); left: 50%; top: 0; padding: 6px 20px 5px; white-space: nowrap; }

.to-top { animation: toTop 0.3s linear forwards; }
.to-right { animation: toRight 0.3s linear forwards; }
.to-bottom { animation: toBottom 0.3s linear forwards; }
.to-left { animation: toLeft 0.3s linear forwards; }

/* sidebar */
.blog-left-side-bar .blog-content, .blog-right-side-bar .blog-sidebar { float: right; }
.blog-left-side-bar .blog-sidebar, .blog-right-side-bar .blog-content { float: left; }
.aside-title { position: absolute; top: 0; left: 0; right: 0; transform: translateY(-50%); }
.aside-title span { padding: 2px 20px; }
.shopping-left-side-bar .shopping-content, .shopping-right-side-bar .shopping-sidebar, .blog-left-side-bar .blog-content, .blog-right-side-bar .blog-sidebar { order: 13; -ms-flex-order: 13; }
.shopping-left-side-bar .shopping-sidebar, .shopping-right-side-bar .shopping-content, .blog-right-side-bar .blog-content, .blog-left-side-bar .blog-sidebar { order: 1; -ms-flex-order: 1; }

/* blog tag */
.blog-tag { font-size: 10px; padding: 6px 14px; text-transform: uppercase; margin-right: 3px; text-align: center; vertical-align: middle }
.blog-tag:hover { border-color: #0038e3 !important; color: #fff; background-color: #0038e3; }
.blog-tag:last-child { margin-right: 0; }
.blog-auther-tag { font-size: 11px; line-height: 11px; padding: 5px 10px; text-transform: uppercase; position: absolute; }
.blog-auther-tag:hover { border-color: transparent; color: #232323 !important; background-color: #fff; }

/* blog category style 01 */
.blog-category-style-01 { overflow: hidden; transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out; }
.blog-category-style-01 .blog-image{ width: calc(100% + 50px); transition: opacity 0.35s, transform 0.35s, -webkit-transform 0.35s; -moz-transition: opacity 0.35s, transform 0.35s, -webkit-transform 0.35s; -webkit-transition: opacity 0.35s, transform 0.35s, -webkit-transform 0.35s; -ms-transition: opacity 0.35s, transform 0.35s, -webkit-transform 0.35s; transform: translate3d(-40px, 0, 0); -moz-transform: translate3d(-40px, 0, 0); -webkit-transform: translate3d(-40px, 0, 0); -ms-transform: translate3d(-40px, 0, 0); }
.blog-category-style-01:hover .blog-image{ width: calc(100% + 0); transform: translate3d(0, 0, 0); -webkit-transform: translate3d(0, 0, 0); -moz-transform: translate3d(0, 0, 0); -ms-transform: translate3d(0, 0, 0); }
.blog-category-style-01 img { transition-duration: 0.35s; -moz-transition-duration: 0.35s; -webkit-transition-duration: 0.35s; -ms-transition-duration: 0.35s; }
.blog-category-style-01:hover img { opacity: 0.5; transition-duration: 0.35s; -moz-transition-duration: 0.35s; -webkit-transition-duration: 0.35s; -ms-transition-duration: 0.35s; }
.blog-category-style-01 .blog-post-details { position: absolute; top: 0; left: 0; width: 100%; height: 100%; }
.blog-category-style-01 .post-details {padding: 8px 22px}

/* author */
.about-author-img { float: left; max-width: 100px; margin-right: 35px; margin-bottom: 15px; }
.about-author-text { top: -5px; }
.author-sharing a { margin-right: 15px; color: #737373; }
.author-sharing a:hover { color: #000; }
.alignnone { margin: 5px 30px 40px 0; }
.aligncenter, div.aligncenter { display: block; margin: 5px auto 5px auto; }
.alignright { float:right; margin: 5px 0 30px 40px; }
.alignleft { float: left; margin: 5px 40px 30px 0; }
a img.alignright { float: right; margin: 5px 0 40px 30px; }
a img.alignnone { margin: 5px 20px 20px 0; }
a img.alignleft { float: left; margin: 5px 20px 20px 0; }
a img.aligncenter { display: block; margin-left: auto; margin-right: auto; }
.wp-caption { background: #f7f7f7; padding:17px; text-align: center; border: 1px solid rgba(0,0,0,.1); max-width: 50%; }
.wp-caption figcaption { padding: 18px 25px 0 25px; }
.wp-caption img { width: 100%; }
.wp-caption.alignnone { margin: 5px 30px 40px 0; }
.wp-caption.alignleft { margin: 5px 40px 30px 0; }
.wp-caption.alignright { margin: 5px 0 40px 30px; }
.wp-caption p.wp-caption-text { font-size: 11px; line-height: 17px; margin: 0; padding: 0 4px 5px; }
.screen-reader-text { clip: rect(1px, 1px, 1px, 1px); position: absolute !important; height: 1px; width: 1px; overflow: hidden; }
.screen-reader-text:focus { background-color: #f1f1f1; border-radius: 3px; box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6); clip: auto !important; color: #21759b; display: block; font-size: 14px; font-size: 0.875rem; font-weight: bold; height: auto; left: 5px; line-height: normal; padding: 15px 23px 14px; text-decoration: none; top: 5px; width: auto; z-index: 1; }

/* blog comments */
.blog-comment li { padding-bottom: 65px; position: relative; list-style: none; }
.blog-comment li .user-comment { overflow: hidden; flex: 1; max-width: 100%; }
.blog-comment > li:first-child { padding-top: 0; }
.blog-comment li:last-child { border-bottom: medium none; padding-bottom: 0; }
.blog-comment li .blog-comment-box { padding: 40px 50px; border-radius: 10px;}
.blog-comment li .btn-reply { float: right; font-size: 11px; line-height: initial; padding: 7px 16px 6px; border-radius: 50px; border: 1px solid rgba(43.9, 43.9, 43.9, 0.25); background-color: #ffffff; }
.blog-comment li .btn-reply:hover { border-color: #0038e3; background: #0038e3; color: #fff; }
.blog-comment li ul.child-comment { list-style: outside none none; margin-left:70px;}
.blog-comment li ul.child-comment li { margin-top: 60px; padding-bottom: 0;}

/* sidebar latest post */
.latest-post-sidebar { list-style: none; padding: 0; margin: 0; }
.latest-post-sidebar li { margin-bottom: 45px; }
.latest-post-sidebar li:last-child { margin-bottom: 0; }
.latest-post-sidebar li figure { height:65px; width: 80px; margin: 0; }
.latest-post-sidebar li .media-body { line-height: normal; padding-left: 30px; position: relative; top: -3px; }

/* pagination style 01 */
.pagination-style-01 .page-link { border: 0; padding:12px 5px; margin: 0 10px; background: transparent; color: #828282; min-width: 40px; text-align: center; border-radius: 100% }
.pagination-style-01 .page-item:first-child .page-link, .pagination-style-01 .page-item:last-child .page-link { padding: 0; }
.pagination-style-01 .page-item:last-child .page-link { margin-right: 0; }
.pagination-style-01 .page-item:first-child .page-link:hover, .pagination-style-01 .page-item:last-child .page-link:hover { background: transparent; color: #232323; box-shadow: none }
.pagination-style-01 .page-link:hover, .pagination-style-01 .active .page-link { background: #fff; color: #232323; box-shadow: 0 0 10px rgba(23,23,23,.15) }

/* ===================================
    24. Shop
====================================== */

.product-box, .product-box .product-image { position: relative; overflow: hidden; }
.product-box, .product-box .product-image img { width: 100%; }
.product-image .product-hover { position: absolute; top: 0; left: 0; width: 100%; height: 100%; opacity: 0; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }
.product-hover { position: absolute; top: 0; left: 0; width: 100%; height: 100%; opacity: 0; }
.product-image:hover .product-hover{ opacity: 1; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }
.product-price del { margin-right: 8px }

.product-box .product-hover-right { position: absolute; top: 0; right: 0; opacity: 0; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }
.product-box:hover .product-hover-right { opacity: 1; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }

.product-image .hover-image { position: absolute; top: 0; left: 0; right: 0; opacity: 0; visibility: hidden; transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; }
.product-box:hover .hover-image { display: block; opacity: 1; visibility: visible; }
.product-box:hover .default-image { opacity: 0 !important; visibility: hidden; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }
.product-box .product-hover-bottom { position: absolute; bottom: 0; left: 0; width: 100%; opacity: 0; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }
.product-box:hover .product-hover-bottom { opacity: 1; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }
.product-box:hover .hover-img { opacity: 1; }
.product-box .product-overlay { position: absolute; top: 0; left: 0; width: 100%; height: 100%; visibility: hidden; opacity: 0; -webkit-transition: all 0.4s ease-in-out; transition: all 0.4s ease-in-out; }
.product-box:hover .product-overlay { opacity: 1; visibility: visible; -webkit-transition: all 0.4s ease-in-out; transition: all 0.4s ease-in-out; }

.product-box .move-top-bottom { opacity: 0; transform: translateY(-15px); -moz-transform: translateY(-15px); -webkit-transform: translateY(-15px); -ms-transform: translateY(-15px); transition-property: transform, opacity; -moz-transition-property: transform, opacity; -webkit-transition-property: transform, opacity; -ms-transition-property: transform, opacity; }
.product-box .move-bottom-top { opacity: 0; transform: translateY(15px); -moz-transform: translateY(15px); -webkit-transform: translateY(15px); -ms-transform: translateY(15px); transition-property: transform, opacity; -moz-transition-property: transform, opacity; -webkit-transition-property: transform, opacity; -ms-transition-property: transform, opacity; }
.product-box:hover .move-top-bottom, .product-box:hover .move-bottom-top { opacity: 1; transform: translateY(0px); -moz-transform: translateY(0px); -webkit-transform: translateY(0px); -ms-transform: translateY(0px); }

.product-box .move-left-right { opacity: 0; transform: translateX(-15px); -moz-transform: translateX(-15px); -webkit-transform: translateX(-15px); -ms-transform: translateX(-15px); transition-property: transform, opacity; -moz-transition-property: transform, opacity; -webkit-transition-property: transform, opacity; -ms-transition-property: transform, opacity; }
.product-box .move-right-left { opacity: 0; transform: translateX(15px); -moz-transform: translateX(15px); -webkit-transform: translateX(15px); -ms-transform: translateX(15px); transition-property: transform, opacity; -moz-transition-property: transform, opacity; -webkit-transition-property: transform, opacity; -ms-transition-property: transform, opacity; }
.product-box:hover .move-left-right, .product-box:hover .move-right-left { opacity: 1; transform: translateX(0px); -moz-transform: translateX(0px); -webkit-transform: translateX(0px); -ms-transform: translateX(0px); }

.product-image .product-link-icon:nth-child(1), .product-image:hover .product-link-icon:nth-child(1) { -webkit-transition-delay: 0s; -moz-transition-delay: 0s; -o-transition-delay: 0s; transition-delay: 0s; }
.product-image .product-link-icon:nth-child(2), .product-image:hover .product-link-icon:nth-child(2) { -webkit-transition-delay: 0.05s; -moz-transition-delay: 0.05s; -o-transition-delay: 0.05s; transition-delay: 0.05s; }
.product-image .product-link-icon:nth-child(3), .product-image:hover .product-link-icon:nth-child(3) { -webkit-transition-delay: 0.1s; -moz-transition-delay: 0.1s; -o-transition-delay: 0.1s; transition-delay: 0.1s; }
.product-image .product-link-icon:nth-child(4), .product-image:hover .product-link-icon:nth-child(4) { -webkit-transition-delay: 0.15s; -moz-transition-delay: 0.15s; -o-transition-delay: 0.15s; transition-delay: 0.15s; }
.product-image .product-link-icon:nth-child(5), .product-image:hover .product-link-icon:nth-child(5) { -webkit-transition-delay: 0.2s; -moz-transition-delay: 0.2s; -o-transition-delay: 0.2s; transition-delay: 0.2s; }
.tooltip-inner {font-size: 11px; padding: 4px 13px 5px; text-transform: uppercase; font-family: 'Poppins', sans-serif; font-weight: 500; border-radius: 2px;}

.product-link-icon { position: relative; border-radius: 100%; background-color: #fff; color: #232323; display: inline-block; margin: 5px; height: 40px; width: 40px; line-height: 42px; box-shadow: 0 0 30px rgba(0,0,0,.2); text-align: center; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }
.product-link-icon:hover { color: #232323; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }
.product-link-icon i { margin: 0; font-size: 15px; }

.product-badge { position: absolute; top: 25px; left: 25px; font-size: 10px; font-weight: 600; color: #fff; line-height: 11px; text-align: center; text-transform: uppercase; display: block; padding: 5px 8px; border-radius: 2px; }
.product-badge.green { background-color: #21b375; }
.product-badge.red { background-color: #f34a53; }
.product-badge.orange { background-color: #f36e4a; }

.product-image .product-hover-details a { background: #232323; bottom: -50px; left: 0; width: 100%; text-align: center; position: absolute; padding: 12px 0; transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; z-index: 1; }
.product-image .product-hover-details a:hover { color: #fff; }
.product-image .product-hover-details a:after { content: "/"; color: #545553; font-size: 11px; position: absolute; right: -5px; top: 2px; }
.product-image .product-hover-details a:last-child:after { content: ""; }
.product-image .hover-img { position: absolute; top: 0; left: 0; opacity: 0; visibility: hidden; transition-duration: 0.5s; -moz-transition-duration: 0.5s; -webkit-transition-duration: 0.5s; -ms-transition-duration: 0.5s; }
.product-box:hover .product-hover-details a{ bottom: 0; }
.product-box:hover .hover-img { opacity: 1; visibility: visible; transition-duration: 0.5s; -moz-transition-duration: 0.5s; -webkit-transition-duration: 0.5s; -ms-transition-duration: 0.5s; }

/* shop detail color */
.shop-color { display: inline-block; }
.shop-color li { display: inline-block;}
.shop-color li > input[type="radio"] + label span { height: 32px; width: 32px; border-radius: 100%; margin-right: 8px;}
.shop-color li > input[type="radio"] + label span:after { line-height: 32px; font-size: 10px }
.shop-color li > input[type="radio"]:checked + label span:after { color: #fff}

/* shop detail size */
.shop-size { display: inline-block; }
.shop-size li { display: inline-block;}
.shop-size li > input[type="radio"] + label span { height: 32px; width: 32px; line-height: 30px; border-radius: 100%; background: #f7f7f7; border: 1px solid transparent; text-align: center; margin-right: 10px;}
.shop-size li > input[type="radio"]:checked + label span { border: 1px solid rgba(35,35,35,.25); color: #232323}
.shop-size li > input[type="radio"] + label span:after { height: 100%; width: 100%; top: 0; left: 0; content: ""; }
.shop-size li > input[type="radio"]:checked + label span:after { display: none; }

/* shop category style 01 */
.shop-category-style-01 .shop-product, .shop-category-style-01 .shop-product-image { position: relative; overflow: hidden; }
.shop-category-style-01 .shop-product-image .shop-product-overlay { position: absolute; top: 0; left: 0; width: 100%; height: 100%; visibility: hidden; opacity: 0; -webkit-transition: all 0.4s ease-in-out; transition: all 0.4s ease-in-out; }
.shop-category-style-01 .shop-product .shop-product-content { position: absolute; bottom: 25px; left: 25px; width: calc(100% - 50px); -webkit-transition: all 0.3s ease-in-out; transition: all 0.3s ease-in-out; }
.shop-category-style-01 .shop-product-content a, .shop-category-style-01 .shop-product-content i { -webkit-transition: all 0.4s ease-in-out; transition: all 0.4s ease-in-out; }
.shop-category-style-01 .shop-product:hover .shop-product-content a, .shop-category-style-01 .shop-product:hover .shop-product-content i { color: #fff !important; -webkit-transition: all 0.4s ease-in-out; transition: all 0.4s ease-in-out; }
.shop-category-style-01 .shop-product:hover .shop-product-overlay { opacity: 1; visibility: visible; -webkit-transition: all 0.4s ease-in-out; transition: all 0.4s ease-in-out; }
.shop-category-style-01 .shop-product:hover .shop-product-content { background-color: #232323 !important; border-radius: 4px; bottom: 25px; -webkit-transition: all 0.3s ease-in-out; transition: all 0.3s ease-in-out;  }
.shop-category-style-01 .shop-product-image img { width: 100%;}

/* shop category style 02 */
.shop-category-style-02 .shop-product-image { margin-right: -25px; width: 100%; -webkit-transition: all 0.3s ease-in-out; transition: all 0.3s ease-in-out; position: relative }
.shop-category-style-02 .shop-product-image img { -webkit-transition: all 0.3s ease-in-out; transition: all 0.3s ease-in-out;  }
.shop-category-style-02:hover .shop-product-image img { opacity: .3; padding: 15px; -webkit-transition: all 0.3s ease-in-out; transition: all 0.3s ease-in-out;  }
.shop-category-style-02 .shop-product-image .product-view-link { opacity: 0; transform: scale(1,1); -webkit-transform: scale(1,1) translate(-50%, -50%); -moz-transform: scale(1,1) translate(-50%, -50%); -ms-transform: scale(1,1) translate(-50%, -50%); -webkit-transition: all 0.3s ease-in-out; transition: all 0.3s ease-in-out; top: 50%; left: 50%; }
.shop-category-style-02:hover .shop-product-image a.product-view-link { opacity: 1; transform: scale(1.1,1.1) translate(-50%, -50%); -webkit-transform: scale(1.1,1.1) translate(-50%, -50%); -moz-transform: scale(1.1,1.1) translate(-50%, -50%); -ms-transform: scale(1.1,1.1) translate(-50%, -50%); }

/* products details page */
.product-image-slider img {cursor: crosshair;}
.product-images-box .slider-product-next, .product-images-box .slider-product-prev {width: 65px; height: 65px}

/* other */
.cart-products td { color: #828282; vertical-align: middle; border-bottom: 1px solid #e4e4e4; border-top: 0; padding: 25px 10px; line-height: 28px; }
.cart-products th { border-top: 0; border-bottom: 1px solid #e4e4e4!important; padding: 15px 10px; font-weight: 500; text-transform: capitalize; }
.cart-products .product-remove{ width: 25px; }
.cart-products .cart-product-image { max-width: 75px; }
.cart-products .product-thumbnail{ width: 110px; }
.cart-products .product-name .variation{ float: left; width: 100%; }
.cart-products .product-name .variation .variation-Size{ float: left; margin-right: .25em; display: inline-block; list-style: none outside; font-weight: normal;}
.cart-products .product-name .variation .variation-Size p{ margin: 0; }

.quantity{ position: relative; display: inline-block; vertical-align: middle; }
.qty-btn{ width: 22px; height: 19px; line-height: 19px; border: 0; border-left: 1px solid #e8e8e8; padding: 0; margin: 0; position: absolute; background: transparent;}
.qty-btn:focus { border: none; border-left: 1px solid #e8e8e8;}
.qty-plus:focus{ border:none; border-bottom: 1px solid #e8e8e8; border-left: 1px solid #e8e8e8;}
.qty-minus{ right: 1px; top: 20px; padding-left: 2px; border-radius: 0;}
.qty-plus{ top: 1px; right: 1px; border-bottom: 1px solid #e8e8e8; height: 20px; line-height: 20px; padding-left: 1px; border-radius: 0;}
.qty-text { margin: 0; padding: 0 24px 0 0; width: 65px; line-height: 40px; height: 40px; outline: none; border-radius: 0; outline: none; text-align: center; -webkit-appearance: textfield; -moz-appearance: textfield; }

.coupon-code-panel{ position: relative; }
.coupon-code-panel input{ margin: 0; border: none; border: 1px dashed #e4e4e4; padding: 10px 70px 11px 45px; width: 100%; position: relative}
.coupon-code-panel:before {position: absolute; content: "\e8bf"; font-family: 'feather'; position: absolute; left: 20px; z-index: 2; line-height: 44px}
.coupon-code-panel .apply-coupon-btn{ position: absolute; right: 10px; top: 50%; -webkit-transform: translateY(-50%); -ms-transform: translateY(-50%); transform: translateY(-50%); color: #232323; font-weight: 500; font-size: 12px; line-height: 20px; text-transform: capitalize; }
.cart-btn{ position: relative; font-size: 12px; line-height: 20px; }
.cart-btn:after{ content: ''; position: absolute; width: 90%; height: 2px; bottom: 0; left: 50%; background: #000; -webkit-transform: translateX(-50%); -ms-transform: translateX(-50%); transform: translateX(-50%); }

.total-price-table td,.total-price-table th{ border: none; border-bottom: 1px solid #e4e4e4; padding-top: 20px; padding-bottom: 20px; }
.total-price-table tr:last-child th, .total-price-table tr:last-child td{ border: none; }
.total-price-table .calculate-shipping th .calculate-shipping-title{ position: relative; color: #212529; }
.total-price-table .calculate-shipping th .calculate-shipping-title:after{ font-family: 'feather'; content: "\e842"; position: absolute; right: 0; top: 0; font-weight: 700;}
.total-price-table .calculate-shipping th .calculate-shipping-title:hover{ outline: none; color: #212529; }

.price-filter.ui-widget.ui-widget-content { border-radius: 0; background-color: #dfdfdf; border: 1px solid #fff; height: 5px; margin: 25px 3px 30px 8px; }
.price-filter .ui-slider-range { background-color: #232323; height: 3px; }
.price-filter .ui-slider-handle { background-color: #fff; width: 13px; height: 13px; top: -5px; border: 1px solid #bbb; border-radius: 100%; cursor: ew-resize; outline: 0; }
.price-filter-details .btn-filter { font-size: 10px; color: #101010; background-color: transparent; float: right; padding: 1px 12px; outline: none; font-weight: 500; text-align: center; text-decoration: none; text-transform: uppercase; border: 1px solid #d6d6d6; line-height: 19px; }
.price-filter-details .btn-filter:hover { color: #fff; background-color: #232323; border: 1px solid #232323; }
.price-filter-details .price-filter-amount .price-amount { border: none; padding: 0; max-width: 115px; background-color: transparent; }

/* ===================================
    25. Elements
====================================== */

/* client logo style 01 */
.client-logo-style-01 .client-box { position: relative; width: 100%; height: 100%; display: inline-block; z-index: 0; transition-duration: 0.3s; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; opacity: 1; }
.client-logo-style-01 .client-box:hover { z-index: 10; }
.client-logo-style-01 .client-box:before, .client-logo-style-01 .client-box:after { content: ""; display: block; position: absolute; background: #fff; top: 50%; left: 50%; transform: translate(-50%, -50%); -webkit-transform: translate(-50%, -50%); -moz-transform: translate(-50%, -50%); -ms-transform: translate(-50%, -50%); -o-transform: translate(-50%, -50%); transform-origin: 50% 50%; -webkit-transform-origin: 50% 50%; -moz-transform-origin: 50% 50%; -ms-transform-origin: 50% 50%; -o-transform-origin: 50% 50%; opacity: 1; }
.client-logo-style-01 .client-box:before { width: calc(100% + 3px); height: calc(100% + 4px); z-index: 1; transition: height 1s ease, opacity .8s ease; -webkit-transition: height 1s ease, opacity .8s ease; -moz-transition: height 1s ease, opacity .8s ease; -ms-transition: height 1s ease, opacity .8s ease; -o-transition: height 1s ease, opacity .8s ease; }
.client-logo-style-01 .client-box:after { height: calc(100% + 4px); width: 100%; z-index: 1; transition: width 1s ease, opacity .8s ease; }
.client-logo-style-01 .client-box:hover:before { transition: height .2s ease, opacity .3s ease; -webkit-transition: height .2s ease, opacity .3s ease; -moz-transition: height .2s ease, opacity .3s ease; -ms-transition: height .2s ease, opacity .3s ease; -o-transition: height .2s ease, opacity .3s ease; height: calc(100% - 20px); opacity: .7; }
.client-logo-style-01 .client-box:hover:after { transition: width .2s ease, opacity .3s ease; -webkit-transition: width .2s ease, opacity .3s ease; -moz-transition: width .2s ease, opacity .3s ease; -ms-transition: width .2s ease, opacity .3s ease; -o-transition: width .2s ease, opacity .3s ease; width: calc(100% - 20px); opacity: .8; }
.client-logo-style-01 .client-box .client-box-image { z-index: 2; position: relative; }

/* client logo style 02 */
.client-logo-style-02 .client-logo {opacity: 0.6; transition-duration: 0.2s; -webkit-transition-duration: 0.2s; -moz-transition-duration: 0.2s; -ms-transition-duration: 0.2s; -o-transition-duration: 0.2s;  }
.client-logo-style-02 .client-logo:hover {opacity: 1; transition-duration: 0.2s; -webkit-transition-duration: 0.2s; -moz-transition-duration: 0.2s; -ms-transition-duration: 0.2s; -o-transition-duration: 0.2s; }

/* client logo style 03 */
.client-logo-style-03 img { filter: grayscale(1); transition-property: filter; -webkit-transition-property: filter; -moz-transition-property: filter; -ms-transition-property: filter; -o-transition-property: filter; transition-duration: 0.4s; -webkit-transition-duration: 0.4s; -moz-transition-duration: 0.4s; -ms-transition-duration: 0.4s; -o-transition-duration: 0.4s; }
.client-logo-style-03 a:hover img { filter: grayscale(0); transition-property: filter; transition-duration: 0.4s; -webkit-transition-duration: 0.4s; -moz-transition-duration: 0.4s; -ms-transition-duration: 0.4s; -o-transition-duration: 0.4s; }

/* client logo style 04 */
.client-logo-style-04 .client-box { position: relative; width: 100%; height: 100%; display: inline-block; z-index: 0; transition-duration: 0.3s; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; }
.client-logo-style-04 .client-box .client-overlay { position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: -1; opacity: 0; transform: scale(0.8); transition-duration: 0.3s; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; }
.client-logo-style-04 .client-box:hover .client-overlay { opacity: 1; transform: scale(1); }

/* client logo style 05 */
.client-logo-style-05 a {display: block}
.client-logo-style-05 a img { position: relative; top:0; -webkit-transition: all 0.2s cubic-bezier(0.2, 0, 0.3, 1); -moz-transition: all 0.2s cubic-bezier(0.2, 0, 0.3, 1); -ms-transition: all 0.2s cubic-bezier(0.2, 0, 0.3, 1); -o-transition: all 0.2s cubic-bezier(0.2, 0, 0.3, 1); transition: all 0.2s cubic-bezier(0.2, 0, 0.3, 1);  }
.client-logo-style-05 a:hover img { transform: translateY(-10px); -moz-transform: translateY(-10px); -webkit-transform: translateY(-10px); -ms-transform: translateY(-10px);  }

/* client logo style 06 */
.client-logo-style-06 .client-box { opacity: .5; height: 100%; display: -webkit-box; display: -ms-flexbox; display: flex!important; -webkit-box-orient: vertical; -webkit-box-direction: normal; -ms-flex-direction: column; flex-direction: column; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; transition-duration: 0.2s; -webkit-transition-duration: 0.2s; -moz-transition-duration: 0.2s; -ms-transition-duration: 0.2s; -o-transition-duration: 0.2s; }
.client-logo-style-06 .client-box:hover { opacity: 1; transition-duration: 0.2s; -webkit-transition-duration: 0.2s; -moz-transition-duration: 0.2s; -ms-transition-duration: 0.2s; -o-transition-duration: 0.2s; }

/* client logo style 07 */
.client-logo-style-07 .client-logo {opacity: 1; -webkit-transition: all 0.4s cubic-bezier(0.2, 0, 0.3, 1); -moz-transition: all 0.4s cubic-bezier(0.2, 0, 0.3, 1); -ms-transition: all 0.4s cubic-bezier(0.2, 0, 0.3, 1); -o-transition: all 0.4s cubic-bezier(0.2, 0, 0.3, 1); transition: all 0.4s cubic-bezier(0.2, 0, 0.3, 1);  }
.client-logo-style-07:hover .client-logo { -webkit-filter: blur(2px); filter: blur(2px); opacity: .6; }
.client-logo-style-07 .client-logo:hover {opacity: 1; -webkit-filter: blur(0); filter: blur(0); }

/* interactive banner style 01 */
.interactive-banners-style-01 * { transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; }
.interactive-banners-style-01 { display: inline-block; float: none; border: 0; position: relative; }
.interactive-banners-style-01 .interactive-banners-image { position: relative; overflow: hidden; }
.interactive-banners-style-01 .interactive-banners-image img { width: 100%;}
.interactive-banners-style-01 .interactive-banners-hover { position: absolute; width:100%; height:100%; left: 0; top: 0; }
.interactive-banners-style-01:hover .interactive-banners-hover { opacity: 1; }
.interactive-banners-style-01 .interactive-banners-icon { width: 35px; height: 35px; line-height: 36px; border-radius: 100%; text-align: center; opacity: 0; position: absolute; top: 50%; left: 50%; margin: -18px 0 0 0; transform: translate(-50%, -50%); -moz-transform: translate(-50%, -50%); -webkit-transform: translate(-50%, -50%); -ms-transform: translate(-50%, -50%); transition-property: transform, opacity; -moz-transition-property: transform, opacity; -webkit-transition-property: transform, opacity; -ms-transition-property: transform, opacity;}
.interactive-banners-style-01:hover .rounded-icon { opacity: 1; transform: translateX(-18px); -moz-transform: translateX(-18px); -webkit-transform: translateX(-18px); -ms-transform: translateX(-18px); }
.interactive-banners-style-01 .interactive-banners-title { opacity: 1; transform: translateY(35px); -moz-transform: translateY(35px); -webkit-transform: translateY(35px); -ms-transform: translateY(35px); transition-property: transform, opacity; -moz-transition-property: transform, opacity; -webkit-transition-property: transform, opacity; -ms-transition-property: transform, opacity; }
.interactive-banners-style-01:hover .interactive-banners-title { opacity: 0; transform: translateY(70px); -moz-transform: translateY(70px); -webkit-transform: translateY(70px); -ms-transform: translateY(70px);}
.interactive-banners-style-01 .interactive-banners-sub-title { opacity: 0; transform: translateY(70px); -moz-transform: translateY(70px); -webkit-transform: translateY(70px); -ms-transform: translateY(70px); transition-property: transform, opacity; -moz-transition-property: transform, opacity; -webkit-transition-property: transform, opacity; -ms-transition-property: transform, opacity; }
.interactive-banners-style-01:hover .interactive-banners-sub-title { opacity: 1; transform: translateY(10px); -moz-transform: translateY(10px); -webkit-transform: translateY(10px); -ms-transform: translateY(10px);}
.interactive-banners-style-01 .scale {-moz-transform: scale3d(1, 1, 1); -o-transform: scale3d(1, 1, 1); -ms-transform: scale3d(1, 1, 1); -webkit-transform: scale3d(1, 1, 1); transform: scale3d(1, 1, 1);}
.interactive-banners-style-01:hover .scale { -moz-transform: scale3d(1.1, 1.1, 1); -o-transform: scale3d(1.1, 1.1, 1); -ms-transform: scale3d(1.1, 1.1, 1); -webkit-transform: scale3d(1.1, 1.1, 1); transform: scale3d(1.1, 1.1, 1); }
.interactive-banners-style-01:hover img {opacity: .5}

/* interactive banner style 02 */
.interactive-banners-style-02 .category-name { position: absolute; top: 40px; left: 50px; line-height: 14px; background: #fff; color: #232323; text-transform: uppercase; padding: 8px 15px; -webkit-transition: all 0.2s cubic-bezier(0.2, 0, 0.3, 1); -moz-transition: all 0.2s cubic-bezier(0.2, 0, 0.3, 1); -ms-transition: all 0.2s cubic-bezier(0.2, 0, 0.3, 1); -o-transition: all 0.2s cubic-bezier(0.2, 0, 0.3, 1); transition: all 0.2s cubic-bezier(0.2, 0, 0.3, 1); }
.interactive-banners-style-02 > a { position: relative; display: block;}
.interactive-banners-style-02:hover .category-name { background: #232323; color: #fff; transition: all 0.5s ease-in-out; -webkit-transition: all 0.5s ease-in-out; -moz-transition: all 0.5s ease-in-out; -ms-transition: all 0.5s ease-in-out; -o-transition: all 0.5s ease-in-out; box-shadow: 0 0 10px rgba(0,0,0,.2) }
.interactive-banners-style-02 .category-content { position: absolute; bottom: 0; left: 0; padding: 20px 50px 40px; width: 100%;  }
.interactive-banners-style-02 .interactive-banners-icon { opacity: 0; transform: translateY(10px); -moz-transform: translateY(10px); -webkit-transform: translateY(10px); -ms-transform: translateY(10px); -webkit-transition: all 0.2s cubic-bezier(0.2, 0, 0.3, 1); -moz-transition: all 0.2s cubic-bezier(0.2, 0, 0.3, 1); -ms-transition: all 0.2s cubic-bezier(0.2, 0, 0.3, 1); -o-transition: all 0.2s cubic-bezier(0.2, 0, 0.3, 1); transition: all 0.2s cubic-bezier(0.2, 0, 0.3, 1); }
.interactive-banners-style-02:hover .interactive-banners-icon { opacity: 1; transform: translateY(0); -moz-transform: translateY(0); -webkit-transform: translateY(0); -ms-transform: translateY(0); }

/* interactive banner style 03 */
.interactive-banners-style-03 * { transition: all 0.5s ease-in-out; -webkit-transition: all 0.5s ease-in-out; -moz-transition: all 0.5s ease-in-out; -ms-transition: all 0.5s ease-in-out; -o-transition: all 0.5s ease-in-out; }
.interactive-banners-style-03 { z-index: 0; position: relative; }
.interactive-banners-style-03 .interactive-banners-image { width: 100%; height: 100%; left: 0; z-index: -1; opacity: 0; top:0; }
.interactive-banners-style-03:hover .interactive-banners-image { opacity: 1; -ms-transform: scale(1.1,1.1); -webkit-transform: scale(1.1,1.1); transform: scale(1.1,1.1); }
.interactive-banners-style-03:hover span a, .interactive-banners-style-03:hover i, .interactive-banners-style-03:hover h6 a, .interactive-banners-style-03:hover span { color: #fff !important; }
.interactive-banners-style-03:hover .interactive-banners-name { border-color: #fff !important; }

/* interactive banner style 04 */
.interactive-banners-style-04 .interactive-banners-content .btn { position: absolute; bottom: -30px; opacity: 0; margin: 0 auto; left: 0; right: 0 }
.interactive-banners-style-04 .interactive-banners-content:hover .btn { bottom:0; opacity: 1 }

/* interactive banner style 05 */
.interactive-banners-style-05 * {-webkit-transition: all 0.4s cubic-bezier(0.2, 0, 0.3, 1); -moz-transition: all 0.4s cubic-bezier(0.2, 0, 0.3, 1); -ms-transition: all 0.4s cubic-bezier(0.2, 0, 0.3, 1); -o-transition: all 0.4s cubic-bezier(0.2, 0, 0.3, 1); transition: all 0.4s cubic-bezier(0.2, 0, 0.3, 1); }
.interactive-banners-style-05 { position: relative; overflow: hidden; }
.interactive-banners-style-05 .interactive-banners-content, .interactive-banners-style-05 .interactive-banners-overlayer { position: absolute; bottom: 0; left: 0; width:100%; z-index: 1; transform: translateY(calc(100% - 130px)); -webkit-transform: translateY(calc(100% - 130px)); -moz-transform: translateY(calc(100% - 130px)); -ms-transform: translateY(100%) translateY(-130px); }
.interactive-banners-style-05:hover .interactive-banners-content, .interactive-banners-style-05:hover .interactive-banners-overlayer {  transform: translateY(0px); -webkit-transform: translateY(0px); -moz-transform: translateY(0px); -ms-transform: translateY(0px);  }
.interactive-banners-style-05 .interactive-banners-content .interactive-banners-content-text { opacity: 0; visibility: hidden; transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out;}
.interactive-banners-style-05:hover .interactive-banners-content .interactive-banners-content-text { opacity: 1; visibility: visible; }
.interactive-banners-style-05 .overlayer-box { height: 0; z-index: -1; }
.interactive-banners-style-05:hover .overlayer-box { height: 100%; }
.interactive-banners-style-05:hover .section-link { transform: translateY(0); }
.interactive-banners-style-05:hover .section-link { opacity: 0; transform: translateY(100%);}

/* interactive banner style 06 */
.interactive-banners-style-06 { display: inline-block; position: relative; }
.interactive-banners-style-06 .interactive-banners-text { -webkit-transform: translateX(20%); transform: translateX(20%); transition: -webkit-transform .3s cubic-bezier(.2,.95,.25,1); transition: transform .3s cubic-bezier(.2,.95,.25,1); transition: transform .3s cubic-bezier(.2,.95,.25,1),-webkit-transform .3s cubic-bezier(.2,.95,.25,1); -webkit-transform-origin: left center; transform-origin: left center; }
.interactive-banners-style-06 a:hover .interactive-banners-text { -webkit-transform: translateX(0); transform: translateX(0); }
.interactive-banners-style-06 .interactive-banners-icon { -webkit-transform: scaleX(0) translateX(0); transform: scaleX(0) translateX(0); transition: -webkit-transform .3s cubic-bezier(.2,.95,.25,1); transition: transform .3s cubic-bezier(.2,.95,.25,1); transition: transform .3s cubic-bezier(.2,.95,.25,1),-webkit-transform .3s cubic-bezier(.2,.95,.25,1); -webkit-transform-origin: left center; transform-origin: left center; }
.interactive-banners-style-06 a:hover .interactive-banners-icon { -webkit-transform: scaleX(1) translateX(-3.727em); transform: scaleX(1) translateX(0); }

/* interactive banner style 07 */
.interactive-banners-style-07 .interactive-banners-box, .interactive-banners-style-07 .interactive-banners-box .interactive-banners-box-image { position: relative; overflow: hidden; }
.interactive-banners-style-07 .interactive-banners-box-image .fancy-text-overlay, .interactive-banners-style-07 .interactive-banners-box .fancy-text-content, .interactive-banners-style-07 .interactive-banners-box img { transition: all 0.5s ease-in-out; -moz-transition: all 0.5s ease-in-out; -webkit-transition: all 0.5s ease-in-out; -ms-transition: all 0.5s ease-in-out; }
.interactive-banners-style-07 .interactive-banners-box .fancy-text-content { position: absolute; width: 100%; left: 0px; top: auto; z-index: 9; bottom: 0px; height: auto; transform: translateY(10px); -webkit-transform: translateY(10px); -moz-transform: translateY(10px); -ms-transform: translateY(10px); }
.interactive-banners-style-07 .interactive-banners-box:hover .fancy-text-content { transform: translateY(-50px); -webkit-transform: translateY(-50px); -moz-transform: translateY(-50px); -ms-transform: translateY(-50px); }
.interactive-banners-style-07 .interactive-banners-box .fancy-text-content span { opacity: 1; transition: all 0.5s ease-in-out; -moz-transition: all 0.5s ease-in-out; -webkit-transition: all 0.5s ease-in-out; -ms-transition: all 0.5s ease-in-out; }
.interactive-banners-style-07 .interactive-banners-box:hover .fancy-text-content span { opacity: 0;}
.interactive-banners-style-07 .interactive-banners-box .fancy-text-content .btn { opacity: 0; white-space: nowrap; transform: translateY(15px); -webkit-transform: translateY(15px); -moz-transform: translateY(15px); -ms-transform: translateY(15px); }
.interactive-banners-style-07 .interactive-banners-box:hover .fancy-text-content .btn { opacity: 1; transform: translateY(0); -webkit-transform: translateY(0); -moz-transform: translateY(0); -ms-transform: translateY(0); }
.interactive-banners-style-07 .interactive-banners-box:hover img { opacity: .7}

/* interactive banner style 08 */
.interactive-banners-style-08 .interactive-banners-box-image { position: relative; overflow: hidden; }
.interactive-banners-style-08 .interactive-banners-box-image img { -webkit-transform: scale(1); -moz-transform: scale(1); -ms-transform: scale(1); -o-transform: scale(1); transform: scale(1); -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }
.interactive-banners-style-08:hover .interactive-banners-box-image img { -webkit-transform: scale(1.1); -moz-transform: scale(1.1); -ms-transform: scale(1.1); -o-transform: scale(1.1); transform: scale(1.1); -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }
.interactive-banners-style-08 .interactive-banners-box-image .btn { z-index: 1; position: absolute; top: 50%; left: 50%; height: 44px; width: 44px; line-height: 40px; padding: 0; opacity: 0; -webkit-transform: translate(-50%, -50%); -moz-transform: translate(-50%, -50%); -ms-transform: translate(-50%, -50%); -o-transform: translate(-50%, -50%); transform: translate(-50%, -50%); }
.interactive-banners-style-08:hover .interactive-banners-box-image .btn { opacity: 1; }
.interactive-banners-style-08 span { opacity: 1; transition-property: transform, opacity; -moz-transition-property: transform, opacity; -webkit-transition-property: transform, opacity; -ms-transition-property: transform, opacity;  transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out;}
.interactive-banners-style-08:hover span { opacity: 0; }
.interactive-banners-style-08 .interactive-banners-box-sub-title { position: absolute; bottom: 0; width: 100%; opacity: 0; transform: translateY(20px); -moz-transform: translateY(20px); -webkit-transform: translateY(20px); -ms-transform: translateY(20px); transition-property: transform, opacity; -moz-transition-property: transform, opacity; -webkit-transition-property: transform, opacity; -ms-transition-property: transform, opacity;  transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; }
.interactive-banners-style-08:hover .interactive-banners-box-sub-title { opacity: 1; transform: translateY(0px); -moz-transform: translateY(0px); -webkit-transform: translateY(0px); -ms-transform: translateY(0px); }
.interactive-banners-style-08 .interactive-banners-box-hover { opacity: 0; transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out;}
.interactive-banners-style-08:hover .interactive-banners-box-hover { opacity: 1;}

/* interactive banner style 09 */
.interactive-banners-style-09 figure { position: relative; overflow: hidden; }
.interactive-banners-style-09 figcaption { position: absolute; top: 0; left: 0; width: 100%; height: 100%; }
.interactive-banners-style-09 .interactive-banners-content .interactive-banners-hover-icon { position: absolute; left: 60px; bottom: 60px; transform: scale(1); -webkit-transform: scale(1); -moz-transform: scale(1); -ms-transform: scale(1); transition: all 0.5s ease-in-out; -moz-transition: all 0.5s ease-in-out; -webkit-transition: all 0.5s ease-in-out; -ms-transition: all 0.5s ease-in-out; }
.interactive-banners-style-09 figcaption:hover .interactive-banners-content .interactive-banners-hover-icon { opacity: 0; transform: scale(0); -webkit-transform: scale(0); -moz-transform: scale(0); -ms-transform: scale(0); transition: all 0.5s ease-in-out; -moz-transition: all 0.5s ease-in-out; -webkit-transition: all 0.5s ease-in-out; -ms-transition: all 0.5s ease-in-out; }
.interactive-banners-style-09 .interactive-banners-hover-action { opacity: 0; position: absolute; bottom:-25px; height: 100%; width: 100%; transition: all 0.5s ease-in-out; -moz-transition: all 0.5s ease-in-out; -webkit-transition: all 0.5s ease-in-out; -ms-transition: all 0.5s ease-in-out; }
.interactive-banners-style-09 .interactive-banners-action-content { overflow: hidden; text-overflow: ellipsis; display: -webkit-box; min-height: 75px; -webkit-line-clamp: 3; -webkit-box-orient: vertical; }
.interactive-banners-style-09 figcaption:hover .interactive-banners-hover-action { opacity: 1; bottom:0; transition: all 0.5s ease-in-out; -moz-transition: all 0.5s ease-in-out; -webkit-transition: all 0.5s ease-in-out; -ms-transition: all 0.5s ease-in-out; }
.interactive-banners-style-09 figure img { -webkit-transition-duration: 2s; -moz-transition-duration: 2s; -ms-transition-duration: 2s; transition-duration: 2s; }
.interactive-banners-style-09 figure:hover img { -webkit-transform: scale(1.4); -ms-transform: scale(1.4); -moz-transform: scale(1.4); transform: scale(1.4); -o-transform: scale(1.4); -webkit-transition-duration: 4s; transition-duration: 4s; }
.interactive-banners-style-09 .interactive-banners-overlay { opacity: 0; position: absolute; bottom:0; height: 100%; width: 100%; transition: all 0.5s ease-in-out; -moz-transition: all 0.5s ease-in-out; -webkit-transition: all 0.5s ease-in-out; -ms-transition: all 0.5s ease-in-out; }
.interactive-banners-style-09 figure:hover .interactive-banners-overlay { opacity: 1; transition: all 0.5s ease-in-out; -moz-transition: all 0.5s ease-in-out; -webkit-transition: all 0.5s ease-in-out; -ms-transition: all 0.5s ease-in-out; }

/* interactive banner style 10 */
.interactive-banners-style-10 figure { position: relative; margin-bottom: 0; }
.interactive-banners-style-10 figcaption { position: absolute; top: 0; left: 0; height: 100%; width: 100%; opacity: 0; transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out; }
.interactive-banners-style-10 figcaption a {top:-15px; position: relative}
.interactive-banners-style-10 figure:hover figcaption { opacity: 1; }
.interactive-banners-style-10 figure:hover figcaption a {top: 0}

/* interactive banner style 11 */
.interactive-banners-style-11 .interactive-banners-box, .interactive-banners-style-11 .interactive-banners-box .interactive-banners-box-image { position: relative; overflow: hidden; }
.interactive-banners-style-11 .interactive-banners-box-image .interactive-banners-text-overlay, .interactive-banners-style-11 .interactive-banners-box .interactive-banners-text-content, .interactive-banners-style-11 .interactive-banners-box .interactive-banners-box-image img { transition: all 0.5s ease-in-out; -moz-transition: all 0.5s ease-in-out; -webkit-transition: all 0.5s ease-in-out; -ms-transition: all 0.5s ease-in-out; }
.interactive-banners-style-11 .interactive-banners-box-image .interactive-banners-text-overlay { opacity: .3; position: absolute; top: 0; left: 0; width: 100%; height: 100%; transition: all 0.4s ease-in-out; -moz-transition: all 0.4s ease-in-out; -webkit-transition: all 0.4s ease-in-out; -ms-transition: all 0.4s ease-in-out; }
.interactive-banners-style-11 .interactive-banners-box:hover .interactive-banners-text-overlay { opacity: .77; }
.interactive-banners-style-11 .interactive-banners-box:hover .interactive-banners-box-image img { -webkit-filter: blur(3px); filter: blur(3px); transform: scale(1.1); -webkit-transform: scale(1.1); -moz-transform: scale(1.1); -ms-transform: scale(1.1); }
.interactive-banners-style-11 .interactive-banners-box .interactive-banners-text-content { position: absolute; width: 100%; left: 0px; top: auto; z-index: 9; bottom: 0px; height: auto; transform: translateY(20px); -webkit-transform: translateY(20px); -moz-transform: translateY(20px); -ms-transform: translateY(20px); }
.interactive-banners-style-11 .interactive-banners-box:hover .interactive-banners-text-content { transform: translateY(-65px); -webkit-transform: translateY(-65px); -moz-transform: translateY(-65px); -ms-transform: translateY(-65px); }
.interactive-banners-style-11 .interactive-banners-box .interactive-banners-text-content .btn { opacity: 0; transform: translateY(15px); -webkit-transform: translateY(15px); -moz-transform: translateY(15px); -ms-transform: translateY(15px); }
.interactive-banners-style-11 .interactive-banners-box:hover .interactive-banners-text-content .btn { opacity: 1; transform: translateY(0); -webkit-transform: translateY(0); -moz-transform: translateY(0); -ms-transform: translateY(0); }

/* interactive banner style 12 */
.interactive-banners-style-12 figure { margin: 0; }
.interactive-banners-style-12 figure .interactive-banners-image { position: relative; overflow: hidden;  }
.interactive-banners-style-12 figure .interactive-banners-image img { transition-duration: 0.3s; -moz-transition-duration: 0.3s; -webkit-transition-duration: 0.3s; -ms-transition-duration: 0.3s; transition-timing-function: ease-in-out; -moz-transition-timing-function: ease-in-out; -webkit-transition-timing-function: ease-in-out; -ms-transition-timing-function: ease-in-out; }
.interactive-banners-style-12 figure:hover img { opacity: 0.4; }
.interactive-banners-style-12 figure .interactive-banners-details { position: absolute; top: 0; left: 0; width: 100%; height: 100%; transition-duration: 0.3s; -moz-transition-duration: 0.3s; -webkit-transition-duration: 0.3s; -ms-transition-duration: 0.3s; transition-timing-function: ease-in-out; -moz-transition-timing-function: ease-in-out; -webkit-transition-timing-function: ease-in-out; -ms-transition-timing-function: ease-in-out; }
.interactive-banners-style-12 figure .interactive-banners-details a { transform: scale(.9, .9); -webkit-transform: scale(.9, .9); -moz-transform: scale(.9, .9); -ms-transform: scale(.9, .9); -o-transform: scale(.9, .9); opacity: 0; transition-duration: 0.3s; -moz-transition-duration: 0.3s; -webkit-transition-duration: 0.3s; -ms-transition-duration: 0.3s; transition-timing-function: ease-in-out; -moz-transition-timing-function: ease-in-out; -webkit-transition-timing-function: ease-in-out; -ms-transition-timing-function: ease-in-out; }
.interactive-banners-style-12 figure:hover .interactive-banners-details a { transform: scale(1, 1); -webkit-transform: scale(1, 1); -moz-transform: scale(1, 1); -ms-transform: scale(1, 1); -o-transform: scale(1, 1); opacity: 1; }
.interactive-banners-style-12 figure figcaption { position: absolute; bottom: 0; left: 0; width: 100%; transform: translateY(50%); transition-duration: 0.3s; -moz-transition-duration: 0.3s; -webkit-transition-duration: 0.3s; -ms-transition-duration: 0.3s; transition-timing-function: ease-in-out; -moz-transition-timing-function: ease-in-out; -webkit-transition-timing-function: ease-in-out; -ms-transition-timing-function: ease-in-out; }
.interactive-banners-style-12 figure:hover figcaption { transform: translateY(30px); opacity: 0 }

/* interactive banner style 13 */
.interactive-banners-style-13 * { -webkit-transition-duration: 0.4s; -moz-transition-duration: 0.4s; -ms-transition-duration: 0.4s; -o-transition-duration: 0.4s; transition-duration: 0.4s;  }
.interactive-banners-style-13 { overflow: hidden; position: relative; }
.interactive-banners-style-13 .interactive-banners-content { position: absolute; top: 0; right: 0; height: 100%; width: 35%; display: -webkit-box; display: -ms-flexbox; display: flex!important; -webkit-box-orient: vertical; -webkit-box-direction: normal; -ms-flex-direction: column; flex-direction: column; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; }
.interactive-banners-style-13 .interactive-banners-content .interactive-banners-title { margin-bottom: -20px; }
.interactive-banners-style-13:hover .interactive-banners-title { margin-bottom: 10px; }
.interactive-banners-style-13 .interactive-banners-content .interactive-banners-link { opacity: 0; transform: scale(1,1); }
.interactive-banners-style-13:hover .interactive-banners-link { opacity: 1; transform: scale(1,1); }
.interactive-banners-style-13 .interactive-banners-image { position: relative; left: 0; width: 75%; }
.interactive-banners-style-13:hover .interactive-banners-image { left: -20%; filter: grayscale(100%); opacity: .8 }

/* interactive banner style 14 */
.interactive-banners-style-14 * { transition: all 0.4s ease-in-out; -moz-transition: all 0.4s ease-in-out; -webkit-transition: all 0.4s ease-in-out; -ms-transition: all 0.4s ease-in-out; }
.interactive-banners-style-14 figure { position: relative; overflow: hidden; }
.interactive-banners-style-14 figcaption { position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 1; }
.interactive-banners-style-14 figcaption .hover-show-content { opacity: 0; visibility: hidden; margin-bottom: -40px; }
.interactive-banners-style-14 figure figcaption .hover-content-detail { opacity: 0; min-height: 50px; height: 50px; overflow: hidden; -webkit-transition: opacity 0.2s, -webkit-transform 0.35s; transition: opacity 0.2s, transform 0.35s; -webkit-transform: translate3d(0,25px,0); transform: translate3d(0,25px,0); }
.interactive-banners-style-14 figure:hover figcaption .hover-content-detail { height: auto; -webkit-transform: translate3d(0,0,0); transform: translate3d(0,0,0); -webkit-transition-delay: 0.05s; transition-delay: 0.05s; -webkit-transition-duration: 0.35s; transition-duration: 0.35s; opacity: 1; transition: all 0.5s ease-in-out; -moz-transition: all 0.5s ease-in-out; -webkit-transition: all 0.5s ease-in-out; -ms-transition: all 0.5s ease-in-out; }
.interactive-banners-style-14 figure:hover figcaption .hover-content { transform: translateY(-40px); }
.interactive-banners-style-14 figure:hover figcaption .hover-show-content { opacity: 1; visibility: visible; margin: 0; }
.interactive-banners-style-14 figure figcaption .overlayer-box { opacity: 0; }
.interactive-banners-style-14 figure:hover figcaption .overlayer-box { opacity: .85; }
.interactive-banners-style-14 figcaption .hover-action-btn { position: absolute; bottom: 40px; left: 0; width: 100%; }
.interactive-banners-style-14 figure:hover figcaption .hover-action-btn { opacity: 0; visibility: hidden; transform: translateY(50px);  }

/* interactive list style */
.fullscreen-hover-list { max-height: 80vh; overflow: auto; }
.fullscreen-hover-list .hover-list-item { display: table; }
.fullscreen-hover-list .hover-list-item a { display: inline-block; }
.fullscreen-hover-list .hover-list-item .fullscreen-hover-image { position: absolute; top: 0; left: 0; height: 100%; width: 100%; z-index: -1; opacity: 0; visibility: hidden; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }
.fullscreen-hover-list .hover-list-item.active .fullscreen-hover-image { opacity: 1; visibility: visible; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s;  }
.fullscreen-hover-list .hover-list-item .interactive-title { display: inline-block; vertical-align: middle; padding: 25px 35px; color: transparent; -webkit-text-stroke: 2px #000; text-stroke: 2px #000; position: relative; }
.fullscreen-hover-list.light .hover-list-item .interactive-title { color: #3e3e3e; -webkit-text-stroke: 2px #4e4e4f; text-stroke: 2px #4e4e4f; -webkit-text-fill-color: transparent; -webkit-background-clip: text; transition-duration: 0.5s; }
.fullscreen-hover-list.light .hover-list-item:hover .interactive-title {color:#fff; -webkit-text-stroke: 0 #4e4e4f; text-stroke: 0 #4e4e4f; transition-duration: 0.5s; transition-timing-function: ease-in-out;}
.fullscreen-hover-list .hover-list-item .interactive-title:after { content: attr(data-link-text); position: absolute; left: 35px; bottom: 25px; width: 0; height: calc(100% - 50px); -webkit-text-fill-color: #000; transition-duration: 0.4s; overflow: hidden; transition-timing-function: ease-in-out; }
.fullscreen-hover-list.light .hover-list-item .interactive-title:after { -webkit-text-fill-color: #fff; }
.fullscreen-hover-list .hover-list-item.active .interactive-title:after { width: calc(100% - 70px); transition-duration: 0.7s; transition-timing-function: ease-in-out; }
.fullscreen-hover-list .hover-list-item .interactive-number { opacity: .3; display: inline-block; vertical-align: middle; transition-duration: 0.7s; transition-timing-function: ease-in-out; }
.fullscreen-hover-list .hover-list-item.active .interactive-number { opacity: 1; transform: translateX(65px); transition-duration: 0.7s; transition-timing-function: ease-in-out; }
.fullscreen-hover-list .hover-list-item .interactive-line { display: inline-block; vertical-align: middle; width: 30px; height: 1px; margin-left: 35px; transition-duration: 0.7s; transition-timing-function: ease-in-out; }
.fullscreen-hover-list .hover-list-item.active .interactive-line { transform: scale(0); opacity: 0; transition-duration: 0.7s; transition-timing-function: ease-in-out; }
.fullscreen-hover-list .hover-list-item.active .interactive-title {color:#fff; -webkit-text-stroke: 0 #4e4e4f; text-stroke: 0 #4e4e4f; }
.fullscreen-hover-list .hover-list-item .interactive-icon { vertical-align: middle; line-height: normal; font-size: 50px; position: relative; top: 6px; opacity: 0; visibility: hidden; transition-duration: .5s; transition-timing-function: ease-in-out; }

/* services box style 01 */
.services-box-style-01 * { transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; }
.services-box-style-01 .services-box-hover { position: absolute; width: 100%; height: 100%; left: 0; top: 0; opacity: 0;}
.services-box-style-01:hover .services-box-hover { opacity: 1; }
.services-box-style-01 .services-box-hover .services-icon { line-height: 50px; display: inline-block; vertical-align: middle; text-align: center; margin: 0 5px; opacity: 0; transform: translateY(15px); -moz-transform: translateY(15px); -webkit-transform: translateY(15px); -ms-transform: translateY(15px); }
.services-box-style-01 .services-icon a { width: 44px; height: 44px; display: inline-block; vertical-align: middle; line-height: 46px; text-align: center; margin: 0 3px; }
.services-box-style-01:hover img { transform: scale(1.1, 1.1); -moz-transform: scale(1.1, 1.1); -webkit-transform: scale(1.1, 1.1); -ms-transform: scale(1.1, 1.1); opacity: 0.5; }
.services-box-style-01:hover .services-icon { transition-delay: .2s; opacity: 1; transform: translateY(0); -moz-transform: translateY(0); -webkit-transform: translateY(0); -ms-transform: translateY(0);}

/* pie charts style 01 */
.pie-chart-style-01 { text-align: center; position: relative; width: auto; display: -webkit-box; display: -ms-flexbox; display: flex;  -webkit-box-align: center; -ms-flex-align: center; align-items: center;  -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; }
.pie-chart-style-01 .percent { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); -webkit-transform: translate(-50%, -50%); -moz-transform: translate(-50%, -50%); -ms-transform: translate(-50%, -50%); }

/* pie charts style 02 */
.pie-chart-style-02 { text-align: center; position: relative; width: auto; display: -webkit-box; display: -ms-flexbox; display: flex;  -webkit-box-align: center; -ms-flex-align: center; align-items: center;  -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; }
.pie-chart-style-02 .percent { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); -webkit-transform: translate(-50%, -50%); -moz-transform: translate(-50%, -50%); -ms-transform: translate(-50%, -50%); }

/* list style 01 */
.list-style-01 * { transition-duration: 0.4s; -webkit-transition-duration: 0.4s; -moz-transition-duration: 0.4s; -ms-transition-duration: 0.4s; }
.list-style-01 li { position: relative; padding: 12px 25px 14px 25px; z-index: 1; list-style: none; }
.list-style-01 li i { position: absolute; top: 21px; left: 0; font-size: 14px; opacity: .7 }
.list-style-01 li .list-hover { position: absolute; top: 0; left: 0; width: 100%; height: 100%; opacity: 0; z-index: -1; }
.list-style-01 li:hover .list-hover { opacity: 1; }
.list-style-01 li:hover i {left: 25px; opacity: 1}
.list-style-01 li:hover {padding: 12px 25px 14px 50px;}

/* list style 02 */
.list-style-02 li { list-style: none; line-height: 30px; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -webkit-box-orient: horizontal; -webkit-box-direction: normal; -ms-flex-direction: row; flex-direction: row; -webkit-box-align: start; -ms-flex-align: start; align-items: start; }
.list-style-02 li i { line-height: 30px; }

/* list style 03 */
.list-style-03 { list-style: none; }
.list-style-03.light li { border-bottom: 1px solid rgba(255,255,255,.15);}
.list-style-03 li { padding: 10px 0; border-bottom: 1px solid #e4e4e4; }
.list-style-03 li:last-child { border-bottom: none; }

/* list style 04 */
.list-style-04 li { padding: 10px 20px; list-style: none; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -webkit-box-orient: horizontal; -webkit-box-direction: normal; -ms-flex-direction: row; flex-direction: row; }
.list-style-04 li:hover { box-shadow: 0 0 20px rgba(0,0,0,.15); transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out; }

/* list style 05 */
.list-style-05 {padding-left: 20px; text-align: left; margin-left: 10px;}
.list-style-05 li {margin-bottom: 10px}

/* list style 06 */
.list-style-06 li { list-style: none; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -webkit-box-orient: horizontal; -webkit-box-direction: normal; -ms-flex-direction: row; flex-direction: row; -webkit-box-align: start; -ms-flex-align: start; align-items: start; }
.list-style-06 li i { font-size: 11px; margin: auto; }

/* list style 07 */
.list-style-07 li { position: relative; display: inline-block; width: 100%; margin-bottom: 15px; line-height: 18px; }
.list-style-07 li:last-child { margin-bottom: 0; }
.list-style-07 li a { display: inline-block; text-align: left;}
.list-style-07 li .item-qty { font-size: 14px; position: absolute; top: 1px; right: 0; text-align: right;}
.list-style-07 li a:hover .product-cb:after, .list-style-07 li a.active .product-cb:after, .list-style-07 li a .product-cb:hover:after { font-family: "Font Awesome\ 5 Free"; font-size: 8px; content: "\f00c"; position: absolute; left: 3px; top: -1px; font-weight: 900; line-height: 16px; }
.list-style-07 li a:hover { color: #0038e3; }
.list-style-07.filter-category li, .list-style-07.filter-size li { padding: 0 30px; }
.list-style-07.filter-category li a .product-cb, .list-style-07.filter-size li a .product-cb { width: 16px; height: 16px; border-radius: 100%; text-align: center; margin-right: 12px; display: inline-block; border: 1px solid #cacaca; background-color: #fff; position: relative; top: 4px; }
.list-style-07.filter-category li a .product-cb.product-category-cb, .list-style-07.filter-size li a .product-cb.product-size-cb { position: absolute; top: 2px; left: 0; }
.list-style-07.filter-color li a .product-cb { width: 16px; height: 16px; text-align: center; margin-right: 12px; display: inline-block; border-radius: 100%; position: relative; top: 2px; }
.list-style-07.filter-color li a:hover .product-cb:after { color: #fff; top: 0; left: 4px; }

/* list style 08 */
.list-style-08 li { display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; padding: 28px 0; }
.list-style-08 li:last-child { padding-bottom: 0; }
.list-style-08 li .label-new, .list-style-08 li .label-hot { background-color: #70a967; display: inline-block; vertical-align: middle; padding: 2px 9px; font-size: 10px; line-height: 15px; color: #fff; text-transform: uppercase; margin-left: 10px; border-radius: 1px; }
.list-style-08 li .label-hot { background-color: #d74a3a; }

/* accordion style 01 */
.accordion-style-01 .panel .panel-heading { padding: 5px 0; }
.accordion-style-01 .panel .panel-body {padding: 20px 15px 25px 7px;}

/* accordion style 02 */
.accordion-style-02 .panel .panel-heading { border-bottom: 1px solid; padding: 15px 25px 15px 0; position: relative; }
.accordion-style-02 .panel:last-child .panel-heading { border: none; }
.accordion-style-02 .panel:last-child .panel-heading.active-accordion { border-bottom: 1px solid; }
.accordion-style-02 .panel .panel-heading i { position: absolute; top: 50%; right: 0; transform: translateY(-50%); -webkit-transform: translateY(-50%); -moz-transform: translateY(-50%); -ms-transform: translateY(-50%); }
.accordion-style-02 .panel .panel-body { padding: 30px 20px 30px 0; }

/* accordion style 03 */
.accordion-style-03 .panel {padding-left: 32px; padding-right: 32px; margin-bottom: 22px}
.accordion-style-03 .panel .panel-heading { padding: 18px 25px 18px 0; position: relative; }
.accordion-style-03 .panel .panel-heading i { position: absolute; top: 50%; right: 0; transform: translateY(-50%); -webkit-transform: translateY(-50%); -moz-transform: translateY(-50%); -ms-transform: translateY(-50%); }
.accordion-style-03 .panel .panel-body { padding: 0 20px 30px 0; }

/* accordion style 04 */
.accordion-style-04 .panel { border-bottom: 1px solid; padding: 20px 30px; }
.accordion-style-04 .panel:first-child { padding-top: 0; }
.accordion-style-04 .panel:last-child { border: none; padding-bottom: 0;}
.accordion-style-04 .panel-heading { position: relative; }
.accordion-style-04 .panel .panel-time { min-width: 195px; display: inline-block; }
.accordion-style-04 .panel .accordion-toggle { display: inline-block; width: calc(100% - 400px); }
.accordion-style-04 .panel .panel-title i { position: absolute; right: 0; top: 8px; font-weight: 700; }
.accordion-style-04 .panel .panel-speaker { display: inline-block; }
.accordion-style-04 .panel .panel-body { margin-left: 199px; width: 38%; padding: 10px 0 7px; }

/* accordion style 05 */
.accordion-style-05  * {-webkit-transition: all 0.2s cubic-bezier(0.2, 0, 0.3, 1); -moz-transition: all 0.2s cubic-bezier(0.2, 0, 0.3, 1); -ms-transition: all 0.2s cubic-bezier(0.2, 0, 0.3, 1); -o-transition: all 0.2s cubic-bezier(0.2, 0, 0.3, 1); transition: all 0.2s cubic-bezier(0.2, 0, 0.3, 1);}
.accordion-style-05 .panel .panel-heading { border-bottom: 1px solid; padding: 15px 50px 15px 50px; position: relative; }
.accordion-style-05 .panel .panel-heading.active-accordion { background-color: #1C101E; border-bottom-color: #1C101E !important; padding-top: 35px; }
.accordion-style-05 .panel:last-child .panel-heading { border: none; }
.accordion-style-05 .panel:last-child .panel-heading.active-accordion { border-bottom: 1px solid; }
.accordion-style-05 .panel .panel-heading i { position: absolute; top: 50%; right: 35px; transform: translateY(-50%); -webkit-transform: translateY(-50%); -moz-transform: translateY(-50%); -ms-transform: translateY(-50%); }
.accordion-style-05 .panel .panel-heading.active-accordion i { top: calc(50% + 10px); }
.accordion-style-05 .panel .panel-body { padding: 0 90px 20px 50px; }
.accordion-style-05 .panel .collapse.show, .accordion-style-05 .panel .collapsing { background-color: #1C101E;}
.accordion-style-05 .panel .collapse.show .panel-body, .accordion-style-05 .panel .collapsing .panel-body { padding-bottom: 40px; }

/* time table */
.time-table .panel { border-bottom: 1px solid; padding: 35px 70px; }
.time-table .panel-heading { position: relative; }
.time-table .panel .panel-time { min-width: 210px; display: inline-block; }
.time-table .panel .panel-body { margin-left: 0; width: calc(100% - 410px); padding: 10px 0 7px; display: inline-flex; }
.time-table .panel .panel-speaker { display: inline-block; }
.time-table .tab-content > .tab-pane { padding: 0 15px; }

/* newsletter email */
.newsletter-email input { padding-right: 130px; }
.newsletter-email .btn { position: absolute; line-height: 18px; top: 50%; transform: translateY(-50%); -webkit-transform: translateY(-50%); -moz-transform: translateY(-50%); -ms-transform: translateY(-50%); -o-transform: translateY(-50%); right: 0; border: 1px solid #b0b0b0; border-top: 0; border-right: 0; border-bottom: 0; padding:0 25px }

/* newsletter style 01 */
.newsletter-style-01 .btn { position: absolute; top: 50%; border-radius:0 100px 100px 0; right: 0px; border: 1px solid rgba(0,0,0,0.1); padding: 8px 24px; letter-spacing: inherit; transform: translateY(-50%); -moz-transform: translateY(-50%); -webkit-transform: translateY(-50%); -ms-transform: translateY(-50%); height: 100%; }
.newsletter-style-01 input { padding-right: 125px; }

/* newsletter style 02 */
.newsletter-style-02 .btn { position: absolute; top: 50%; border-radius:0; right: 0px; border: 1px solid rgba(0,0,0,0.1); letter-spacing: inherit; transform: translateY(-50%); -moz-transform: translateY(-50%); -webkit-transform: translateY(-50%); -ms-transform: translateY(-50%); }
.newsletter-style-02 input { padding-right: 145px; }

/* newsletter style 03 */
.newsletter-style-03 .btn { position: absolute; top: 0; right: -1px; bottom: 0; border: 0; padding: 4px 18px; letter-spacing: inherit; overflow: hidden; }
.newsletter-style-03 input { padding-right: 70px; }
.newsletter-style-03 .medium-input + .btn { padding: 4px 18px; }
.newsletter-style-03 .large-input + .btn { padding: 4px 23px; }

/* newsletter style 04 */
.newsletter-style-04 .btn { position: absolute; top: 50%; border-radius:0 6px 6px 0; right: 0px; border: 0 solid rgba(0,0,0,0.1); padding: 8px 35px; transform: translateY(-50%); -moz-transform: translateY(-50%); -webkit-transform: translateY(-50%); -ms-transform: translateY(-50%); height: 99%; }
.newsletter-style-04 input { padding-right: 210px; }

/* instagram */
.instafeed-grid { list-style: none; text-align: center; }
.instafeed-grid figure { position: relative; overflow: hidden; margin-bottom: 0; border-radius: 3px; }
.instafeed-grid figure a .insta-counts { opacity: 0; position: absolute; top: 0; left: 0; text-align: center; background: rgba(35,35,35,0.7); width: 100%; height: 100%; transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out;}
.instafeed-grid figure a .insta-counts i { margin-right: 5px; margin-left: 5px; position: relative; display: block; font-size: 20px; color: #fff; top: 50%; transform: translateY(-50%); -webkit-transform: translateY(-50%); -moz-transform: translateY(-50%); -ms-transform: translateY(-50%); -o-transform: translateY(-50%);}
.instafeed-grid figure:hover a .insta-counts { opacity: 1 }
.instagram-follow-api .instagram-title {z-index: 1; white-space: nowrap; padding: 15px 30px; box-shadow: 0 0 35px rgba(23,23,23,.4) }

/* sidebar instagram */
aside .instafeed-grid.gutter-small { margin-left: -5px; margin-right: -5px;}
aside .instafeed-grid.gutter-very-small { margin-left: -3px; margin-right: -3px;}
aside .instafeed-grid.gutter-medium { margin-left: -7px; margin-right: -7px;}
aside .instafeed-grid.gutter-large { margin-left: -10px; margin-right: -10px;}
aside .instafeed-grid.gutter-extra-large { margin-left: -15px; margin-right: -15px;}
aside .instafeed-grid.gutter-double-extra-large { margin-left: -20px; margin-right: -20px;}

/* feature box */
.feature-box * { transition-duration: 0.4s; -webkit-transition-duration: 0.4s; -moz-transition-duration: 0.4s; -ms-transition-duration: 0.4s; }
.feature-box { display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; -webkit-box-orient: vertical; -webkit-box-direction: normal; -ms-flex-direction: column; flex-direction: column; text-align: center; z-index: 1; position: relative; transition-duration: 0.4s; -webkit-transition-duration: 0.4s; -moz-transition-duration: 0.4s; -ms-transition-duration: 0.4s; }
.feature-box .feature-box-content { max-width: 100%; }
.feature-box-left-icon .feature-box-icon { margin-right: 35px; }
.feature-box-left-icon { flex-flow: row wrap; -webkit-flex-flow: row wrap; -ms-flex-flow: row wrap; -webkit-box-align: stretch!important; -ms-flex-align: stretch!important; align-items: stretch!important; -webkit-box-pack: start; -ms-flex-pack: start; justify-content: flex-start; text-align: left; }
.feature-box-left-icon .feature-box-content { overflow: hidden; flex: 1; }
.feature-box-left-icon-middle { flex-flow: row wrap; -webkit-flex-flow: row wrap; -ms-flex-flow: row wrap; -webkit-box-align: center !important; -ms-flex-align: center !important; align-items: center !important; -webkit-box-pack: start; -ms-flex-pack: start; justify-content: flex-start; text-align: left; }
.feature-box-left-icon-middle .feature-box-content { overflow: hidden; -webkit-box-flex: 1; flex: 1; }
.feature-box.text-left { -webkit-box-align: start; -ms-flex-align: start; align-items: flex-start; }
.feature-box.text-right { -webkit-box-align: end; -ms-flex-align: end; align-items: flex-end; }
.feature-box.feature-box-shadow:hover, .feature-box .feature-box-shadow:hover  { border-color: transparent !important; -moz-transform: translate3d(0, -3px, 0); -o-transform: translate3d(0, -3px, 0); -ms-transform: translate3d(0, -3px, 0); -webkit-transform: translate3d(0, -3px, 0); transform: translate3d(0, -3px, 0); }
.feature-box.feature-box-shadow .feature-box-overlay { box-shadow: 0 0 20px rgba(0,0,0,.05); opacity: 0; }
.feature-box.feature-box-shadow:hover .feature-box-overlay { opacity: 1; }
.feature-box-overlay { position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; z-index: -1; opacity: 0; }
.feature-box:hover .feature-box-overlay { opacity: 1;  }
.feature-box.feature-box-dark-hover:hover .feature-box-icon, .feature-box.feature-box-dark-hover:hover i, .feature-box.feature-box-dark-hover:hover span, .feature-box.feature-box-dark-hover:hover p, .feature-box.feature-box-dark-hover:hover .btn, .feature-box.feature-box-dark-hover:hover a, .feature-box.feature-box-dark-hover:hover h1, .feature-box.feature-box-dark-hover:hover h2, .feature-box.feature-box-dark-hover:hover h3, .feature-box.feature-box-dark-hover:hover h4, .feature-box.feature-box-dark-hover:hover h5, .feature-box.feature-box-dark-hover:hover h6 { color: #ffffff !important; }
.feature-box.feature-box-dark-hover:hover .btn { border-color: #ffffff !important; }
.feature-box.feature-box-dark-hover:hover [class^="text-gradient"], .feature-box.feature-box-dark-hover:hover [class*=" text-gradient"] { color: #ffffff !important; background-image: none; -webkit-background-clip: inherit; -webkit-text-fill-color: inherit; }
.feature-box.feature-box-light-hover:hover .feature-box-icon, .feature-box.feature-box-light-hover:hover i, .feature-box.feature-box-light-hover:hover span, .feature-box.feature-box-light-hover:hover p, .feature-box.feature-box-light-hover:hover .btn, .feature-box.feature-box-light-hover:hover a, .feature-box.feature-box-light-hover:hover h1, .feature-box.feature-box-light-hover:hover h2, .feature-box.feature-box-light-hover:hover h3, .feature-box.feature-box-light-hover:hover h4, .feature-box.feature-box-light-hover:hover h5, .feature-box.feature-box-light-hover:hover h6 { color: #232323 !important; }
.feature-box.feature-box-light-hover:hover .btn { border-color: #232323 !important; }
.feature-box.feature-box-light-hover:hover [class^="text-gradient"], .feature-box.feature-box-light-hover:hover [class*=" text-gradient"] { color: #232323 !important; background-image: none; -webkit-background-clip: inherit; -webkit-text-fill-color: inherit; }
.feature-box.feature-box-light-hover:hover p, .feature-box.feature-box-dark-hover:hover p {opacity: .65}

/* feature box icon hover */
.feature-box-icon-rounded { display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; -webkit-box-align: center; -ms-flex-align: center; align-items: center; position: relative; overflow: hidden; }
.feature-box-icon-rounded i { position: relative; z-index: 1; }
.feature-box-icon-rounded .feature-box-icon-hover { position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; opacity: 0; }
.feature-box:hover .feature-box-icon-hover { opacity: 1; }
.feature-box:hover .light i { color: #232323; }
.feature-box:hover .dark i { color: #ffffff; }

/* feature box show hover */
.feature-box.feature-box-show-hover .feature-box-move-bottom-top { width: 100%; -webkit-transform: translateY(20px); -moz-transform: translateY(20px); -ms-transform: translateY(20px); -o-transform: translateY(20px); transform: translateY(20px); overflow: hidden; }
.feature-box.feature-box-show-hover:hover .feature-box-move-bottom-top { -webkit-transform: translateY(-5px); -moz-transform: translateY(-5px); -ms-transform: translateY(-5px); -o-transform: translateY(-5px); transform: translateY(-5px); }
.feature-box.feature-box-show-hover .move-bottom-top { opacity: 0; -webkit-transform: translateY(20px); -moz-transform: translateY(20px); -ms-transform: translateY(20px); -o-transform: translateY(20px); transform: translateY(20px); }
.feature-box.feature-box-show-hover:hover .move-bottom-top { opacity: 1; -webkit-transform: translateY(0px); -moz-transform: translateY(0px); -ms-transform: translateY(0px); -o-transform: translateY(0px); transform: translateY(0px); }

/* feature box hide show hover */
.feature-box.feature-box-hide-show-hover .feature-box-move-bottom-top { width: 100%; -webkit-transform: translateY(30px); -moz-transform: translateY(30px); -ms-transform: translateY(30px); -o-transform: translateY(30px); transform: translateY(30px); overflow: hidden; }
.feature-box.feature-box-hide-show-hover:hover .feature-box-move-bottom-top { -webkit-transform: translateY(-30px); -moz-transform: translateY(-30px); -ms-transform: translateY(-30px); -o-transform: translateY(-30px); transform: translateY(-30px); }
.feature-box.feature-box-hide-show-hover .move-bottom-top { opacity: 0; -webkit-transform: translateY(20px); -moz-transform: translateY(20px); -ms-transform: translateY(20px); -o-transform: translateY(20px); transform: translateY(20px); }
.feature-box.feature-box-hide-show-hover:hover .move-bottom-top { opacity: 1; -webkit-transform: translateY(0px); -moz-transform: translateY(0px); -ms-transform: translateY(0px); -o-transform: translateY(0px); transform: translateY(0px); }
.feature-box.feature-box-hide-show-hover:hover .feature-box-icon { opacity: 0; }

/* feature box svg */
.feature-box-svg { position: relative; }
.feature-box-svg i { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 1; }

/* tab */
.nav-tabs { border: none; }
.nav-tabs > li.nav-item { display: inline-block; float: none; margin: 0; padding: 0 20px; }
.nav-tabs > li.nav-item > a.nav-link { border: none; }
.nav-tabs > li.nav-item > a.nav-link { border-bottom: 1px solid transparent; }
.nav-tabs > li.nav-item > a.nav-link.active, .nav-tabs > li.nav-item > a.nav-link.active:hover, .nav-tabs > li.nav-item > a.nav-link:hover { background: transparent; border-color: #222222; color: #222222; cursor: pointer; }
.nav-tabs.nav-tabs-light > li.nav-item > a.nav-link.active, .nav-tabs.nav-tabs-light > li.nav-item > a.nav-link.active:focus, .nav-tabs.nav-tabs-light > li.nav-item > a.nav-link:focus, .nav-tabs.nav-tabs-light > li.nav-item > a.nav-link:hover { border-bottom: 1px solid #fff; color: #fff; }

/* tab style 01 */
.tab-style-01 .nav-tabs > li.nav-item { padding: 0 35px; }
.tab-style-01 .nav-tabs > li.nav-item > a.nav-link { padding: 0; margin-left: 25px; position: relative; border-bottom: none !important }
.tab-style-01 .grid-filter.nav-tabs > li.nav-item > a.nav-link { margin-left: 0; }
.tab-style-01 .nav-tabs > li.nav-item .tab-border { width: 0; height: 2px; display: block; margin-top: 5px; opacity: 0; visibility: hidden; transition-duration: 0.5s; }
.tab-style-01 .nav-tabs > li.nav-item > a.nav-link.active +.tab-border,
.tab-style-01 .nav-tabs > li.active .tab-border { opacity: 1; width: 100%; visibility: visible; }
.tab-style-01 .nav-tabs > ul { counter-reset: slideNum; }
.tab-style-01 .nav-tabs > li { counter-increment: slideNum; line-height: 16px }
.tab-style-01 .nav-tabs > li.nav-item > a.nav-link:before { content: "0"counter(slideNum)"."; position: absolute; left: -25px; }
.tab-style-01 .grid-filter.nav-tabs > li.nav-item > a.nav-link:before { display: none; }
.tab-style-01 .nav-tabs > li:last-child { border-right: none; padding-right: 0 }
.tab-style-01 .nav-tabs > li:first-child { padding-left: 0 }
.tab-style-01.without-number .nav-tabs > li.nav-item > a.nav-link:before {content: ""}
.tab-style-01.without-number .nav-tabs > li.nav-item > a.nav-link {margin-left: 0}
@media(hover: hover) {
    .tab-style-01 .nav-tabs > li.nav-item > a.nav-link:hover +.tab-border { opacity: 1; width: 100%; visibility: visible; }
}

/* tab style 02 */
.tab-style-02 .nav-tabs > li.nav-item { margin: 0 20px; }
.tab-style-02 .nav-tabs > li.nav-item *, .tab-style-02 .nav-tabs > li.nav-item:hover * { transition-duration: 0.3s; }
.tab-style-02 .nav-tabs > li.nav-item > a.nav-link { border-bottom: none; }
.tab-style-02 .nav-tabs > li.nav-item > a.nav-link > i { display: block; margin: 0 auto 15px; -webkit-transform: translateY(5px); -moz-transform: translateY(5px); -ms-transform: translateY(5px); -o-transform: translateY(5px); transform: translateY(5px); }
.tab-style-02 .nav-tabs > li.nav-item > a.nav-link:hover > i, .tab-style-02 .nav-tabs > li.nav-item > a.nav-link:focus > i, .tab-style-02 .nav-tabs > li.nav-item > a.nav-link.active > i { -webkit-transform: translateY(0px); -moz-transform: translateY(0px); -ms-transform: translateY(0px); -o-transform: translateY(0px); transform: translateY(0px); }
.tab-style-02 .nav-tabs > li.nav-item > a.nav-link > .tab-label { display: block; margin: 0 auto 20px; -webkit-transform: translateY(-15px); -moz-transform: translateY(-15px); -ms-transform: translateY(-15px); -o-transform: translateY(-15px); transform: translateY(-15px); }
.tab-style-02 .nav-tabs > li.nav-item > a.nav-link:hover > .tab-label, .tab-style-02 .nav-tabs > li.nav-item > a.nav-link:focus > .tab-label, .tab-style-02 .nav-tabs > li.nav-item > a.nav-link.active > .tab-label { -webkit-transform: translateY(0px); -moz-transform: translateY(0px); -ms-transform: translateY(0px); -o-transform: translateY(0px); transform: translateY(0px); }
.tab-style-02 .nav-tabs > li.nav-item > a.nav-link > .tab-label {opacity: 0;}
.tab-style-02 .nav-tabs > li.nav-item > a.nav-link:hover > .tab-label, .tab-style-02 .nav-tabs > li.nav-item > a.nav-link:focus > .tab-label, .tab-style-02 .nav-tabs > li.nav-item > a.nav-link.active > .tab-label {opacity: 1;}
.tab-style-02 .nav-tabs > li.nav-item > a.nav-link.active, .tab-style-02 .nav-tabs > li.nav-item > a.nav-link.active:focus, .tab-style-02 .nav-tabs > li.nav-item > a.nav-link:focus, .tab-style-02 .nav-tabs > li.nav-item > a.nav-link:hover { border-bottom: none; color: #0038e3; background: none; }
.tab-style-02 .nav-tabs.nav-tabs-light > li.nav-item > a.nav-link.active, .tab-style-02 .nav-tabs.nav-tabs-light > li.nav-item > a.nav-link.active:focus, .tab-style-02 .nav-tabs.nav-tabs-light > li.nav-item > a.nav-link:focus, .tab-style-02 .nav-tabs.nav-tabs-light > li.nav-item > a.nav-link:hover { color: #fff; }

/* tab style 03 */
.tab-style-03 .nav-tabs > li.nav-item { padding: 0 35px; }
.tab-style-03 .nav-tabs > li { border-right: 1px solid rgba(130, 130, 130, 0.3); line-height: 12px; }
.tab-style-03 .nav-tabs > li.nav-item > a.nav-link { padding: 0; position: relative; border-bottom: none !important }
.tab-style-03 .nav-tabs > li:last-child { border-right: none; padding-right: 0 }
.tab-style-03 .nav-tabs > li:first-child { padding-left: 0 }
.tab-style-03 .nav-tabs.nav-tabs-light > li { border-right: 1px solid rgba(255, 255, 255, 0.3);}

/* tab style 04 */
.tab-style-04 .nav-tabs > li.nav-item { position: relative; z-index: 1; padding: 0; border: 1px solid; }
.tab-style-04 .nav-tabs > li.nav-item > a.nav-link { border-bottom: none; border-radius: 0; font-size: 12px; line-height: 14px; letter-spacing: 2px; padding: 16px 50px; text-transform: uppercase; z-index: 1; }
.tab-style-04 .tab-bg-active { position: absolute; top: -1px; left: -1px; width: calc(100% + 2px); height: calc(100% + 2px); z-index: -1; opacity: 0; }
.tab-style-04 .nav-tabs > li.nav-item > a.nav-link.active { color: #fff !important; }
.tab-style-04 .nav-tabs > li.nav-item > a.nav-link.active +.tab-bg-active { opacity: 1; }
.tab-style-04 .nav-tabs > li.nav-item > a.nav-link.active:focus, .tab-style-04 .nav-tabs > li.nav-item > a.nav-link:focus, .tab-style-04 .nav-tabs > li.nav-item > a.nav-link:hover { border-bottom: none; color: #232323; background: none; }
.tab-style-04 .nav-tabs.nav-tabs-light > li.nav-item > a.nav-link.active, .tab-style-04 .nav-tabs.nav-tabs-light > li.nav-item > a.nav-link.active:focus, .tab-style-04 .nav-tabs.nav-tabs-light > li.nav-item > a.nav-link:focus, .tab-style-04 .nav-tabs.nav-tabs-light > li.nav-item > a.nav-link:hover { color: #fff; }

/* tab style 05 */
.tab-style-05 .nav-tabs { border: none;}
.tab-style-05 .nav-tabs li { font-size:14px; line-height: 28px; padding: 0; }
.tab-style-05 .nav-tabs li a { display: flex; align-items: center; padding: 8px 40px; border-radius: 25px; border: none !important; justify-content: center}
.tab-style-05 .nav-tabs li i {font-size:18px}
.tab-style-05 .nav-tabs li a:hover { border: none; color: #262B35 !important; }
.tab-style-05 .nav-tabs .nav-link.active { background: #fff !important; box-shadow:0 0px 15px 0 rgba(23,23,23,.08); border: none; color: #262B35 !important; }

/* tab style 06 */
.tab-style-06 .nav-tabs { border-bottom: 1px solid rgba(0,0,0,0.10); }
.tab-style-06 .nav-tabs > li.nav-item { width: 20%; padding: 0;}
.tab-style-06 .nav-tabs > li.nav-item > a.nav-link > span { display: block; color: #828282; font-weight:400; }
.tab-style-06 .nav-tabs > li.nav-item > a.nav-link { padding: 0 40px 22px; color: #232323; font-weight:600; border-width: 3px;  }
.tab-style-06 .nav-tabs > li.nav-item > a.nav-link:hover, .tab-style-06 .nav-tabs > li.nav-item > a.nav-link.active, .tab-style-06 .nav-tabs > li.nav-item > a.nav-link.active:focus, .tab-style-06 .nav-tabs > li.nav-item > a.nav-link:focus,.tab-style-06 .nav-tabs > li.nav-item > a.nav-link:hover { border-bottom: 3px solid #ff7a56; color: #232323; opacity: 1; }

/* tab style 07 */
.tab-style-07 .nav-tabs > li.nav-item { margin: 0 0 -1px 0; }
.tab-style-07 .nav-tabs > li.nav-item *, .tab-style-07 .nav-tabs > li.nav-item:hover * { -webkit-transition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }
.tab-style-07 .nav-tabs > li.nav-item > a.nav-link { color: #232323; border-bottom: 3px solid transparent; padding: 30px 40px 28px 40px; }
.tab-style-07 .nav-tabs > li.nav-item > a.nav-link.active { border-color: #232323; }
.tab-style-07 .nav-tabs > li.nav-item > a.nav-link > .tab-label { opacity: 0; display: block; margin: 0 auto 20px; -webkit-transform: translateY(-15px); -moz-transform: translateY(-15px); -ms-transform: translateY(-15px); -o-transform: translateY(-15px); transform: translateY(-15px); }
.tab-style-07 .nav-tabs > li.nav-item > a.nav-link:hover > .tab-label, .tab-style-07 .nav-tabs > li.nav-item > a.nav-link:focus > .tab-label, .tab-style-07 .nav-tabs > li.nav-item > a.nav-link.active > .tab-label { opacity: 1; -webkit-transform: translateY(0px); -moz-transform: translateY(0px); -ms-transform: translateY(0px); -o-transform: translateY(0px); transform: translateY(0px); }

/* testimonials style 01 */
.testimonials-style-01 * { -webkit-transition-duration: 0.5s; -moz-transition-duration: 0.5s; -ms-transition-duration: 0.5s; -o-transition-duration: 0.5s; transition-duration: 0.5s; }
.testimonials-style-01 { position: relative; -webkit-transition-duration: 0.5s; -moz-transition-duration: 0.5s; -ms-transition-duration: 0.5s; -o-transition-duration: 0.5s; transition-duration: 0.5s; }
.testimonials-style-01 .testimonials-quotes { position: absolute; top: 0; -ms-transform: translateY(-50%) rotateY(0deg) rotateX(0deg); -moz-transform: translateY(-50%) rotateY(0deg) rotateX(0deg); -webkit-transform: translateY(-50%) rotateY(0deg) rotateX(0deg); transform: translateY(-50%) rotateY(0deg) rotateX(0deg); }
.testimonials-style-01:hover { box-shadow: 0 0 15px rgba(0,0,0,.07); background: #fff }
.testimonials-style-01:hover .testimonials-bubble { color: #0039CC; }
.testimonials-style-01:hover .testimonials-quotes { -ms-transform: translateY(-50%) rotateY(-180deg) rotateX(-180deg); -moz-transform: translateY(-50%) rotateY(-180deg)  rotateX(-180deg); -webkit-transform: translateY(-50%) rotateY(-180deg)  rotateX(-180deg); transform: translateY(-50%) rotateY(-180deg)  rotateX(-180deg); }

/* testimonials style 02 */
.testimonials-style-02 { transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out; }
.testimonials-style-02:hover { box-shadow: 0 0 20px rgba(0,0,0,.07); transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out; }
.testimonials-style-02 .testimonials-content { position: relative; }
.testimonials-style-02 .testimonials-content .testimonials-rounded-icon { height: 75px; width: 75px; line-height: 71px; overflow: hidden; box-shadow: 0 0 25px rgba(0,0,0,.05); position: absolute; top: 0; left: 50%; transform: translate(-50%, -50%) scale(1); -webkit-transform: translate(-50%, -50%) scale(1); -moz-transform: translate(-50%, -50%) scale(1); -ms-transform: translate(-50%, -50%) scale(1); transition: all 0.3s ease-in-out; }
.testimonials-style-02 .testimonials-content .testimonials-rounded-icon i { line-height: inherit; }
.testimonials-style-02:hover .testimonials-rounded-icon { transform: translate(-50%, -50%) scale(1.2); -webkit-transform: translate(-50%, -50%) scale(1.2); -moz-transform: translate(-50%, -50%) scale(1.2); -ms-transform: translate(-50%, -50%) scale(1.2); }

/* testimonials style 03 */
.testimonials-style-03 .testimonials-bubble { position: relative; -webkit-transition: all 0.2s ease; -moz-transition: all 0.2s ease; -ms-transition: all 0.2s ease; transition: all 0.2s ease; }
.testimonials-style-03 .testimonials-bubble:before, .testimonials-style-01 .testimonials-bubble:after { content: ""; height: 0; width: 0; border-width: 19px 14px 0; position: absolute; bottom: -20px; left: 50%; -webkit-transform: translateX(-50%); transform: translateX(-50%);  -webkit-transition: border-color 0.2s ease, bottom 0.2s ease; transition: border-color 0.2s ease, bottom 0.2s ease; }
.testimonials-style-03 .testimonials-bubble:before { border-style: solid; height: 0; width: 0; border-color: #fff transparent transparent; border-width: 27px 10px 0; border-left-color: transparent !important; border-right-color: transparent !important; bottom: -19px; left: 50px; z-index: 1; -webkit-transform: rotate(27deg); -ms-transform: rotate(27deg); transform: rotate(27deg); }

/* testimonials style 04 */
.testimonials-style-04 .testimonials-bubble { position: relative; border: 1px solid; -webkit-transition: all 0.2s ease; -moz-transition: all 0.2s ease; -ms-transition: all 0.2s ease; transition: all 0.2s ease; }
.testimonials-style-04 .testimonials-bubble:after, .testimonials-style-04 .testimonials-bubble:before {	top: 100%; left: 50px; border: solid transparent; content: " ";	height: 0; width: 0; position: absolute; pointer-events: none; }
.testimonials-style-04 .testimonials-bubble:after { border-color: rgba(255, 255, 255, 0); border-top-color: #ffffff; border-width: 14px 13px 0 0; margin-left: 2px; }
.testimonials-style-04 .testimonials-bubble:before { border-color: rgba(237, 237, 237, 0); border-top-color: #ededed; border-width: 18px 18px 0 0; }

/* testimonials carousel style 01 */
.testimonials-carousel-style-01 { min-width: 1170px; }

/* testimonials carousel style 02 */
.testimonials-carousel-style-02 { min-width: 1170px; }

/* team style 01 */
.team-style-01 figure { position: relative; overflow: hidden; margin: 0; }
.team-style-01 figure .team-member-image { position: relative; overflow: hidden; }
.team-style-01 .team-overlay, .team-style-01 figure figcaption { position: absolute; top: 0; left: 0; height: 100%; width: 100%; opacity: 0; visibility: hidden; transition: opacity .3s,visibility .3s; }
.team-style-01 figure figcaption .social-icon a {margin: 0 10px}
.team-style-01 figure figcaption .social-icon a:first-child {margin-left: 0}
.team-style-01 figure figcaption .social-icon a:last-child {margin-right: 0}
.team-style-01 figure:hover figcaption, .team-style-01 figure:hover .team-overlay { opacity: 1; visibility: visible; }
.team-style-01 figure figcaption .social-icon, .team-style-01 figure figcaption .team-title, .team-style-01 figure figcaption .team-sub-title { -webkit-transform: translateY(15px); transform: translateY(15px); opacity: 0; transition: opacity .85s cubic-bezier(.19,1,.22,1),-webkit-transform .85s cubic-bezier(.19,1,.22,1); transition: transform .85s cubic-bezier(.19,1,.22,1),opacity .85s cubic-bezier(.19,1,.22,1); transition: transform .85s cubic-bezier(.19,1,.22,1),opacity .85s cubic-bezier(.19,1,.22,1),-webkit-transform .85s cubic-bezier(.19,1,.22,1); }
.team-style-01 figure:hover figcaption .social-icon, .team-style-01 figure:hover figcaption .team-title, .team-style-01 figure:hover figcaption .team-sub-title { opacity: 1; -webkit-transform: translateY(0); transform: translateY(0); }
.team-style-01 figure:hover figcaption .team-title { transition-delay: 0s; }
.team-style-01 figure:hover figcaption .team-sub-title { transition-delay: .2s; }
.team-style-01 figure:hover figcaption .social-icon { transition-delay: .1s; }
.team-style-01 figure figcaption .social-icon a:hover {opacity: .7;}

/* team style 02 */
.team-style-02 figure .team-member-image { position: relative; overflow: hidden; }
.team-style-02 figure .team-member-details { position: absolute; height: 100%; width: 100%; left: 0; top: 100%; transition-duration: 0.3s; -moz-transition-duration: 0.3s; -webkit-transition-duration: 0.3s; -ms-transition-duration: 0.3s; transition-timing-function: ease-in-out; -moz-transition-timing-function: ease-in-out; -webkit-transition-timing-function: ease-in-out; -ms-transition-timing-function: ease-in-out; }
.team-style-02 figure .team-member-image, .team-style-02 figure:hover .team-member-image { transition: all ease-in-out 0.3s; -moz-transition: all ease-in-out 0.3s; -webkit-transition: all ease-in-out 0.3s; -ms-transition: all ease-in-out 0.3s; }
.team-style-02 figure { margin: 0; transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out; }
.team-style-02 figure:hover .team-member-details { top: 0; }
.team-style-02 figure .social-icon a i {  -webkit-transition: all 0.2s cubic-bezier(0.2, 0, 0.3, 1); -moz-transition: all 0.2s cubic-bezier(0.2, 0, 0.3, 1); -ms-transition: all 0.2s cubic-bezier(0.2, 0, 0.3, 1); -o-transition: all 0.2s cubic-bezier(0.2, 0, 0.3, 1); transition: all 0.2s cubic-bezier(0.2, 0, 0.3, 1); }
.team-style-02 figure .social-icon a:hover i { -moz-transform: scale3d(1.3, 1.3, 1.3); -o-transform: scale3d(1.3, 1.3, 1.3); -ms-transform: scale3d(1.3, 1.3, 1.3); -webkit-transform: scale3d(1.3, 1.3, 1.3); transform: scale3d(1.3, 1.3, 1.3); }

/* team style 03 */
.team-style-03 figure { overflow: hidden; }
.team-style-03 figure .team-member-image { position: relative; overflow: hidden; }
.team-style-03 figure .team-member-image img { transform: scale(1); transition: all 0.5s ease-in-out; -moz-transition: all 0.5s ease-in-out; -webkit-transition: all 0.5s ease-in-out; -ms-transition: all 0.5s ease-in-out; }
.team-style-03 figure:hover .team-member-image img { transform: scale(1.1); transition: all 0.5s ease-in-out; -moz-transition: all 0.5s ease-in-out; -webkit-transition: all 0.5s ease-in-out; -ms-transition: all 0.5s ease-in-out; }

/* vertical counter */
.vertical-counter-number { overflow: hidden; width: 100%; height: auto; }
.vertical-counter-number ul{ text-align: center; margin: 0; padding: 0; transform: translateY(0); transition: 2s ease-in-out; list-style: none; }

/* counter style 01 */
.counter-style-01 .counter { position: relative; display: inline-block; }
.counter-style-01 .counter:after { position: absolute; content: "+"; left: 100%; top: 0; margin-left: 5px; }

/* progress bar */
.progress { overflow: visible; height: 4px; position: relative; border-radius: 0 }
.progress .progress-title { position: absolute; top: -28px; }
.progress .progress-bar { position: relative; overflow: visible; }
.progress .progress-bar span { position: absolute; top: -30px; right: 0; opacity: 0 }
.progress .progress-bar.appear span { opacity: 1;}

/* progress bar style 01 */
.progress-step-style-01 { overflow: visible; height: 8px; position: relative; border-radius: 0; background-color: transparent; margin-top: 30px; }
.progress-step-style-01 .progress-bar { border-radius: 6px; }

/* process step style 01 */
.process-step-style-01 .process-step-icon-box { position: relative; }
.process-step-style-01 .process-step-icon-box .process-step-bfr { position: absolute; top: 50%; left: calc(100% + 15px); transform: translate(-50%, -50%); -webkit-transform: translate(-50%, -50%); -moz-transform: translate(-50%, -50%); -ms-transform: translate(-50%, -50%); }
.process-step-style-01:last-child .process-step-bfr { display: none; }
.process-step-style-01 .process-step-icon { width: 130px; height: 130px; box-shadow: 0 0 10px rgba(0,0,0,.07); line-height: 130px; border-radius: 100%; text-align: center; display: inline-block; vertical-align: middle; transition-duration: 0.5s; -moz-transition-duration: 0.5s; -webkit-transition-duration: 0.5s; -ms-transition-duration: 0.5s; }
.process-step-style-01 .process-step-icon i { vertical-align: middle; }
.process-step-style-01:hover .process-step-icon {transform: translateY(-5px); box-shadow: 0 8px 25px rgba(0,0,0,.11); transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out; }

/* process step style 02 */
.process-step-style-02 { display: -ms-flexbox; display: flex; -ms-flex-wrap: wrap; flex-wrap: wrap; }
.process-step-style-02 * { transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out;}
.process-step-style-02 .process-step-item { display: flex; position: relative; flex-flow: row wrap; align-items: stretch!important; justify-content: flex-start; text-align: left; transition: box-shadow .3s ease,background .3s ease; }
.process-step-style-02 .process-step-item:hover .process-step-icon { background-color: #fff; color: #27ae60; -webkit-transform: scale(1.1); -moz-transform: scale(1.1); -ms-transform: scale(1.1); -ootransform: scale(1.1); transform: scale(1.1); }
.process-step-style-02 .process-step-icon { position: relative; width: 50px; height: 50px; border-radius: 100%; font-size: 19px; line-height: 48px; color: #fff;}
.process-step-style-02 .process-step-icon-wrap { position: relative; margin-right: 40px; }
.process-step-style-02 .process-step-icon-wrap .process-step-item-box-bfr { position: absolute; bottom: 20px; left: 50%; width: 1px; height: calc(100% - 90px); -webkit-transform: translateX(-50%); -ms-transform: translateX(-50%); transform: translateX(-50%);}
.process-step-style-02 .process-content { overflow: hidden; flex: 1; max-width: 100%;  margin-bottom: 3.7em; }
.process-step-style-02:last-child .process-content { margin-bottom: 0;}

/* process step style 03 */
.process-step-style-03 .process-step-item { display: block; position: relative; float: left; padding: 45px 25px 0; z-index: 1; }
.process-step-style-03 .process-step-item:hover .process-step-number-bfr,
.process-step-style-03 .process-step-item:hover .process-step-number-afr { -webkit-transform: scale(1.1); -moz-transform: scale(1.1); -ms-transform: scale(1.1); -ootransform: scale(1.1); transform: scale(1.1); }
.process-step-style-03 .process-step-item .process-step-number-afr, 
.process-step-style-03 .process-step-number .process-step-number-bfr { width: 56px; height: 56px; border-radius: 50%; position: absolute; top: -9px; left: 50%; margin-left: -28px; z-index: -1; -webkit-transition: .3s cubic-bezier(.3,.58,.55,1); transition: .3s cubic-bezier(.3,.58,.55,1); }
.process-step-style-03 .process-step-icon { position: absolute; margin-bottom: 0; line-height: 1; top: 10px; left: 50%; margin-left: -18px; width: 36px; height: 36px; text-align: center; }
.process-step-style-03 .process-step-item-box .process-step-item-box-bfr { display: block; width: calc(100% + 30px); height: 1px; position: absolute; content: ''; top: 28px; left: -15px; }
.process-step-style-03 .process-step-number { position: relative; z-index: 1; text-align: center; font-size: 21px; line-height: 38px; display: block; -webkit-transition: all 250ms ease-in-out; -moz-transition: all 250ms ease-in-out; -o-transition: all 250ms ease-in-out; transition: all 250ms ease-in-out; }
.process-step-style-03 .process-step-number .process-step-number-bfr { width: 72px; height: 72px; top: -17px; margin-left: -36px; z-index: -1; opacity: 0;}
.process-step-style-03 .process-step-item:hover .process-step-number .process-step-number-bfr { opacity: 1; }
.process-step-style-03 .process-content { padding-top: 50px; }
.process-step-style-03.simple .process-step-number .process-step-number-bfr {opacity: 1;}

/* process step style 04 */
.process-step-style-04 .process-step-item { display: block; position: relative; float: left; padding: 45px 25px 0; z-index: 1; }
.process-step-style-04 .process-step-item:hover .process-step-item-bfr, .process-step-style-04 .process-step-item:hover .process-step-icon-bfr, .process-step-style-04 .process-step-item:hover .process-step-number-bfr { -webkit-transform: scale(1.2); -moz-transform: scale(1.2); -ms-transform: scale(1.2); -ootransform: scale(1.2); transform: scale(1.2); }
.process-step-style-04 .process-step-item .process-step-item-bfr, .process-step-style-04 .process-step-icon .process-step-icon-bfr, .process-step-style-04 .process-step-number .process-step-number-bfr { width: 40px; height: 40px; border-radius: 50%; position: absolute; top: 8px; left: 50%; margin-left: -20px; -webkit-transition: .3s cubic-bezier(.3,.58,.55,1); transition: .3s cubic-bezier(.3,.58,.55,1); }
.process-step-style-04 .process-step-icon { position: absolute; margin-bottom: 0; line-height: 1; top: 10px; left: 50%; font-weight: 700; margin-left: -18px; width: 36px; height: 36px; text-align: center; }
.process-step-style-04 .process-step-item-box .process-step-item-box-bfr { display: block; width: 60%; height: 1px; position: absolute; content: ''; top: 28px; left: 75%; }
.process-step-style-04:last-child .process-step-item-box .process-step-item-box-bfr { display: none; }
.process-step-style-04 .process-step-icon .process-step-icon-bfr { width: 72px; height: 72px; opacity: .2; margin-left: -36px; top: -18px; z-index: -1; }
.process-step-style-04 .process-step-number { position: relative; z-index: 1; text-align: center; font-size: 18px; line-height: 38px; display: block; -webkit-transition: all 250ms ease-in-out; -moz-transition: all 250ms ease-in-out; -o-transition: all 250ms ease-in-out; transition: all 250ms ease-in-out; }
.process-step-style-04 .process-step-number .process-step-number-bfr { width: 56px; height: 56px; top: -10px; margin-left: -28px; z-index: -1; opacity: .4; }
.process-step-style-04 .process-content { padding-top: 50px; }

/* fancy box */
.fancy-box * { transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; }
.fancy-box { overflow: hidden; position: relative; }
.fancy-box .fancy-box-content { position: absolute; left: 0px; top: auto; z-index: 9; bottom: 0px; transform: translateY(0); -webkit-transform: translateY(0); -moz-transform: translateY(0); -ms-transform: translateY(0); height:auto; }
.fancy-box:hover .fancy-box-content { transform: translateY(-50px); -webkit-transform: translateY(-50px); -moz-transform: translateY(-50px); -ms-transform: translateY(-50px); }
.fancy-box .fancy-box-content .btn { opacity: 0; transform: translateY(15px); -webkit-transform: translateY(15px); -moz-transform: translateY(15px); -ms-transform: translateY(15px); transition-delay: 0s; -webkit-transition-delay: 0s; -moz-transition-delay: 0s; -ms-transition-delay: 0s; }
.fancy-box:hover .fancy-box-content .btn { opacity: 1; transform: translateY(0); -webkit-transform: translateY(0); -moz-transform: translateY(0); -ms-transform: translateY(0); transition-delay: 0.2s; -webkit-transition-delay: 0.2s; -moz-transition-delay: 0.2s; -ms-transition-delay: 0.2s; }
.fancy-box .fancy-box-image img { width: 100%; transform: scale(1); -webkit-transform: scale(1); -moz-transform: scale(1); -ms-transform: scale(1); transition-duration: 0.4s; -webkit-transition-duration: 0.4s; -moz-transition-duration: 0.4s; -ms-transition-duration: 0.4s; }
.fancy-box:hover .fancy-box-image img { transform: scale(1.1); -webkit-transform: scale(1.1); -moz-transform: scale(1.1); -ms-transform: scale(1.1); transition-duration: 0.4s; -webkit-transition-duration: 0.4s; -moz-transition-duration: 0.4s; -ms-transition-duration: 0.4s; }

/* fancy box background */
.fancy-box-background {position: relative; z-index: 1;}
.bg-banner-image { position: absolute; top: 0; left: 0; opacity: 0; height: 100%; width: 100%; z-index: -1; visibility: hidden; -webkit-transition-duration: 0.5s; -moz-transition-duration: 0.5s; -ms-transition-duration: 0.5s; -o-transition-duration: 0.5s; transition-duration: 0.5s; background-size: cover }
.fancy-box-background .opacity-medium { z-index: -1; }
.fancy-box-background .fancy-box-item { position: static; }
.fancy-box-background.fancy-box-col .fancy-box-item { position: relative; }
.fancy-box-background .fancy-box-item:hover .bg-banner-image {opacity: 1; visibility: visible; -webkit-transition-duration: 0.5s; -moz-transition-duration: 0.5s; -ms-transition-duration: 0.5s; -o-transition-duration: 0.5s; transition-duration: 0.5s; }
.fancy-box-background .fancy-box-item:hover .light, .fancy-box-background .fancy-box-item:hover .light i { color: #fff; }
.fancy-box-background .fancy-box-item:hover .dark { color: #232323; }
.fancy-box-background:after {height: 100%; width: 100%; top: 0; left: 0; content: ""; z-index: -1; position: absolute;}
.fancy-box-background .fancy-text-box li:hover {background-color: rgba(0, 0, 0, 0.7);-webkit-transition-duration: 0.5s; -moz-transition-duration: 0.5s; -ms-transition-duration: 0.5s; -o-transition-duration: 0.5s; transition-duration: 0.5s; cursor: pointer;}
.fancy-box-background .fancy-text-box li:hover span { color: rgba(255, 255, 255, 0.9);}
.fancy-box-background .fancy-text-box li:hover a { color: rgba(255, 255, 255, 0.9);}

/* fancy text box style 01 */
.fancy-text-box-style-01 .fancy-text-box { display: inline-block; position: relative;}
.fancy-text-box-style-01 .fancy-text-box .fancy-text-box-border-left, .fancy-text-box-style-01 .fancy-text-box .fancy-text-box-border-right { border:10px solid #0039cc; height: 100%; width: 67px; content:""; display: block; position: absolute; top: 50%; bottom: 0; left: 0; transform: translateY(-50%); -webkit-transform: translateY(-50%); -moz-transform: translateY(-50%); -ms-transform: translateY(-50%); }
.fancy-text-box-style-01 .fancy-text-box .fancy-text-box-border-left {  border-right: 0;}
.fancy-text-box-style-01 .fancy-text-box .fancy-text-box-border-right { border-left: 0; right: 0; left: inherit;}
.fancy-text-box-style-01 .fancy-text-box .fancy-text-content { font-size: 80px; line-height: 72px; margin-bottom: 0; margin-right: 15px; }

/* fancy text box style 02 */
.fancy-text-box-style-02 { border-bottom: 1px solid; border-right: 1px solid; overflow: hidden; }
.fancy-text-box-style-02:last-child { border-right: 0}
.fancy-text-box-style-02 .text-box-wrapper .text-box, .fancy-text-box-style-02 .text-box-wrapper .text-box-hover { padding: 52px 20px 43px; display: block; width: 100%; -webkit-transition: all 0.5s ease-in-out; -moz-transition: all 0.5s ease-in-out; -ms-transition: all 0.5s ease-in-out; transition: all 0.5s ease-in-out; }
.fancy-text-box-style-02 .text-box-wrapper:hover .text-box { color: #ff6437; opacity: 0; visibility: hidden; transform: translateY(-100%); -webkit-transform: translateY(-100%); -moz-transform: translateY(-100%); -ms-transform: translateY(-100%); -webkit-transition: all 0.5s ease-in-out; -moz-transition: all 0.5s ease-in-out; -ms-transition: all 0.5s ease-in-out; transition: all 0.5s ease-in-out; }
.fancy-text-box-style-02 .text-box-wrapper .text-box-hover { opacity: 0; position: absolute; }
.fancy-text-box-style-02 .text-box-wrapper:hover .text-box-hover { opacity: 1; transform: translateY(-100%); -webkit-transform: translateY(-100%); -moz-transform: translateY(-100%); -ms-transform: translateY(-100%); -webkit-transition: all 0.5s ease-in-out; -moz-transition: all 0.5s ease-in-out; -ms-transition: all 0.5s ease-in-out; transition: all 0.5s ease-in-out; }

/* fancy text box style 03 */
.fancy-text-box-style-03 { position: relative; overflow: hidden; }
.fancy-text-box-style-03 * { transition: all 0.5s ease-in-out; -moz-transition: all 0.5s ease-in-out; -webkit-transition: all 0.5s ease-in-out; -ms-transition: all 0.5s ease-in-out;}
.fancy-text-box-style-03 .fancy-text-content { position: absolute; width: 100%; left: 0px; top: auto; z-index: 9; bottom: 0px; height: auto; transform: translateY(70px); -webkit-transform: translateY(70px); -moz-transform: translateY(70px); -ms-transform: translateY(70px); }
.fancy-text-box-style-03:hover .fancy-text-content {  transform: translateY(0px); -webkit-transform: translateY(0px); -moz-transform: translateY(0px); -ms-transform: translateY(0px); }
.fancy-text-box-style-03:hover .fancy-text-content .feature-box-overlay { opacity: 1; }
.fancy-text-box-style-03 .fancy-text-content .fancy-text-box-bottom { opacity: 0; transform: translateY(15px); -webkit-transform: translateY(15px); -moz-transform: translateY(15px); -ms-transform: translateY(15px); }
.fancy-text-box-style-03:hover .fancy-text-content .fancy-text-box-bottom { opacity: 1; transform: translateY(0); -webkit-transform: translateY(0); -moz-transform: translateY(0); -ms-transform: translateY(0); }

/* image masking */
.image-mask { -webkit-text-fill-color: transparent; -webkit-background-clip: text; }

/* pricing table style 01 */
.pricing-table-style-01 .pricing-table { transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out; }
.pricing-table-style-01 .pricing-header .popular-label { position: absolute; top: 0; left: 50%; padding: 6px 17px; transform: translate(-50%, -50%); -webkit-transform: translate(-50%, -50%); -moz-transform: translate(-50%, -50%); -ms-transform: translate(-50%, -50%); }

/* pricing table style 02 */
.pricing-table-style-02 .pricing-table { overflow: hidden; }
.pricing-table-style-02 .pricing-table .popular-label { position: absolute; top: -28px; right: -47px; width: 120px; transform-origin: 0; padding: 10px 0 8px; transform: rotate(45deg); -webkit-transform: rotate(45deg); -moz-transform: rotate(45deg); -ms-transform: rotate(45deg); }
.pricing-table-style-02 .pricing-body ul li { padding: 15px 0; }
.pricing-table-style-02 .pricing-body ul li:last-child { padding-bottom: 0; }
.pricing-table-style-02 .pricing-popular { left: -10px; position: relative; z-index: 1; width: calc(100% + 20px); }

/* countdown style 01 */
.countdown.countdown-style-01 .countdown-box { display: inline-block; text-align: center; padding: 0; float: none; margin: 0 40px; }
.countdown.countdown-style-01 .countdown-box:first-child { margin-left: 0; }
.countdown.countdown-style-01 .countdown-box:last-child { margin-right: 0; }
.countdown.countdown-style-01 .number { font-size: 47px; text-align: center; font-weight: 600; line-height: 50px; color: #fff; }
.countdown.countdown-style-01 span { display: block; font-size: 12px; text-align: center; font-weight: 400; color: #fff; text-transform: uppercase; margin-top: -2px; opacity: .5; }

/* countdown style 02 */
.countdown.countdown-style-02 .countdown-box { display: inline-block; text-align: center; padding: 0; float: none; margin: 0; padding: 0 20px; position: relative; }
.countdown.countdown-style-02 .countdown-box:after { content: ":"; position: absolute; right: 0; top: 5px; font-size: 34px; opacity: .45; }
.countdown.countdown-style-02 .countdown-box:last-child:after { display: none; }
.countdown.countdown-style-02 .number { width: 90px; font-size: 47px; text-align: center; font-weight: 300; line-height: 50px; color: #232323; }
.countdown.countdown-style-02 span { display: block; font-size: 12px; text-align: center; font-weight: 400; color: #828282; text-transform: uppercase; margin-top: -2px; opacity: 1; }

/* countdown style 03 */
.countdown.countdown-style-03 .countdown-box { display: inline-block; text-align: center; padding: 0; float: none; margin: 0; padding: 0 20px; position: relative; }
.countdown.countdown-style-03 .countdown-box:after { content: ":"; position: absolute; right: 0; top: 18%; font-size: 34px; font-weight: 600; color: #232323; }
.countdown.countdown-style-03 .countdown-box:last-child:after { display: none; }
.countdown.countdown-style-03 .number { width: 80px; font-size: 38px; letter-spacing: -1px; text-align: center; font-weight: 600; line-height: 38px; color: #fff; }
.countdown.countdown-style-03 span { display: block; font-size: 13px; text-align: center; font-weight: 500; color: #fff; text-transform: uppercase; margin-top: -2px; opacity: 1; }

/* table style 01 */
.table-style-01 { width: 100%; }
.table-style-01 table { width: 100%; }
.table-style-01 td, .table-style-01 th { padding: 12px 10px; text-align: center; }
.table-style-01 tr th:first-child, .table-style-01 tr td:first-child { padding-left: 20px; text-align: left; }

/* table style 02 */
.table-style-02 { width: 100%; }
.table-style-02 th, .table-style-02 td { border-bottom: 1px solid #e8e8e8; padding: 10px 15px; }
.table-style-02 tr:last-child th, .table-style-02 tr:last-child td { border: 0; }
.table-style-02 th { width: 150px; }

/* rotate box */
.rm-rotate-box { position: relative; text-align: center; }
.rm-rotate-box .flipper:hover .front,.rm-rotate-box .flipper:hover .back { transform-style: preserve-3d; -webkit-transform-style: preserve-3d; transition: transform 1s cubic-bezier(0.3, 1, 0.6, 1); }
.rm-rotate-box .to-left .front,.rm-rotate-box .to-right .front { transform: rotateY(0deg); -webkit-transform: rotateY(0deg); -moz-transform: rotateY(0deg); -ms-transform: rotateY(0deg); }
.rm-rotate-box .to-top .front,.rm-rotate-box .to-bottom .front { transform: rotateX(0deg); -webkit-transform: rotateX(0deg); -moz-transform: rotateX(0deg); -ms-transform: rotateX(0deg); }
.rm-rotate-box .to-left .back { transform: rotateY(180deg); -webkit-transform: rotateY(180deg); -moz-transform: rotateY(180deg); -ms-transform: rotateY(180deg); }
.rm-rotate-box .to-left:hover .front { transform: rotateY(-180deg); -webkit-transform: rotateY(-180deg); -moz-transform: rotateY(-180deg); -ms-transform: rotateY(-180deg); }
.rm-rotate-box .to-left:hover .back { transform: rotateY(0deg); -webkit-transform: rotateY(0deg); -moz-transform: rotateY(0deg); -ms-transform: rotateY(0deg); }
.rm-rotate-box .to-right:hover .front { transform: rotateY(180deg); -webkit-transform: rotateY(180deg); -moz-transform: rotateY(180deg); -ms-transform: rotateY(180deg); }
.rm-rotate-box .to-right .back { transform: rotateY(-180deg); -webkit-transform: rotateY(-180deg); -moz-transform: rotateY(-180deg); -ms-transform: rotateY(-180deg); }
.rm-rotate-box .to-right:hover .back { transform: rotateY(0deg); -webkit-transform: rotateY(0deg); -moz-transform: rotateY(0deg); -ms-transform: rotateY(0deg); }
.rm-rotate-box .to-top .back { transform: rotateX(-180deg); -webkit-transform: rotateX(-180deg); -moz-transform: rotateX(-180deg); -ms-transform: rotateX(-180deg); }
.rm-rotate-box .to-top:hover .front { transform: rotateX(180deg); -webkit-transform: rotateX(180deg); -moz-transform: rotateX(180deg); -ms-transform: rotateX(180deg); }
.rm-rotate-box .to-top:hover .back { transform: rotateX(0deg); -webkit-transform: rotateX(0deg); -moz-transform: rotateX(0deg); -ms-transform: rotateX(0deg); }
.rm-rotate-box .to-bottom .back { transform: rotateX(180deg); -webkit-transform: rotateX(180deg); -moz-transform: rotateX(180deg); -ms-transform: rotateX(180deg); }
.rm-rotate-box .to-bottom:hover .front { transform: rotateX(-180deg); -webkit-transform: rotateX(-180deg); -moz-transform: rotateX(-180deg); -ms-transform: rotateX(-180deg); }
.rm-rotate-box .to-bottom:hover .back { transform: rotateX(0deg); -webkit-transform: rotateX(0deg); -moz-transform: rotateX(0deg); -ms-transform: rotateX(0deg); }
.rm-rotate-box .thumb-wrap { -webkit-perspective: 1000px; perspective: 1000px; }
.rm-rotate-box .front, .rm-rotate-box .back { background-size: cover; min-height: 415px; z-index: 1; transform-style: preserve-3d; -webkit-transform-style: preserve-3d; backface-visibility: hidden; -webkit-backface-visibility: hidden; perspective: inherit; transition: transform 0.8s cubic-bezier(0.5, 0.2, 0.2, 0.8); }
.rm-rotate-box .back { position: absolute; display: block; top: 0; left: 0; width: 100%; height: 100%; }
.rm-rotate-box .back .content-wrap { top: 50%; bottom: inherit; left: 0; transform: translate3d(0, -50%, 0) scale(1); -webkit-transform: translate3d(0, -50%, 0) scale(1); -moz-transform: translate3d(0, -50%, 0) scale(1); -ms-transform: translate3d(0, -50%, 0) scale(1); }
.rm-rotate-box .content-wrap { position: absolute; width: 100%; display: block; transform-style: preserve-3d; -webkit-transform-style: preserve-3d; perspective: inherit; backface-visibility: hidden; -webkit-backface-visibility: hidden; z-index: 2; bottom: 55px; left: 0; transform: translate3d(0, 0, 0) scale(1); -webkit-transform: translate3d(0, 0, 0) scale(1); -moz-transform: translate3d(0, 0, 0) scale(1); -ms-transform: translate3d(0, 0, 0) scale(1); }

/* image back offset shadow */
.image-back-offset-shadow { z-index: 1; opacity: 0; margin: 0; position: relative; display: inline-block; -webkit-transition: all 0.4s ease-in-out; transition: all 0.4s ease-in-out; }
.image-back-offset-shadow.active { opacity: 1; }
.image-back-offset-shadow img { -webkit-transition: all 0.4s ease-in-out; transition: all 0.4s ease-in-out; }
.image-back-offset-shadow.active img { -webkit-transform: translate(12px, -12px); transform: translate(15px, -15px); }
.image-back-offset-shadow .overlay { position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: -1; -webkit-transition: all 0.4s ease-in-out; transition: all 0.4s ease-in-out; }
.image-back-offset-shadow.active .overlay { -webkit-transform: translate(-9px, 9px); transform: translate(-9px, 9px); }
.image-back-offset-shadow.position-right.active img { -webkit-transform: translate(10px, -20px); transform: translate(10px, -20px); }
.image-back-offset-shadow.position-right .overlay { -webkit-transition: all 0.4s ease-in-out; transition: all 0.4s ease-in-out; }
.image-back-offset-shadow.position-right.active .overlay { -webkit-transform: translate(35px, 5px); transform: translate(35px, 5px); }

/* ===================================
    26. Footer
====================================== */

footer .footer-logo img { max-height: 36px; }
footer ul {padding-left: 0; list-style: none;}
footer ul li { margin-bottom: 7px; }
footer ul li:last-child { margin-bottom: 0; }
footer .footer-top ul li.media { margin-bottom: 25px; }
footer .footer-top ul li.media:last-child { margin-bottom: 0; }
footer .instagram-follow-api ul li {margin-bottom: 0}
footer .footer-horizontal-link li {margin-right: 35px; list-style: none; margin-bottom: 0}
footer .footer-horizontal-link li:last-child {margin-right: 0}

/* footer dark */
.footer-dark, .footer-dark a { color: #8890A4; }
.footer-dark a:hover { color: #fff; }
.footer-dark .footer-social a { color: #fff; }
.footer-dark .footer-social a:hover { color: #8890A4; }

/*footer sticky*/
.footer-sticky { position: fixed; width: 100%; bottom: 0; left: 0; right: 0; z-index: 0; }

/* scroll to top */
.scroll-top-arrow, .scroll-top-arrow:focus { background: #fff; font-size: 17px; line-height: 34px; box-shadow: 0 0 25px rgba(23,23,23,.25); display: none; height: 34px; width: 34px; padding: 0; position: fixed; right: 45px; text-align: center; text-decoration: none; bottom: 45px; z-index: 1029; border-radius: 100%; }
.scroll-top-arrow:hover { background: #fff; color: #828282; box-shadow: 0 0 30px rgba(23,23,23,.4);-webkit-transform: translateY(-2px); -ms-transform: translateY(-2px); transform: translateY(-2px);}

/* footer instagram feed */
footer .instafeed-grid.gutter-small { margin-left: -5px; margin-right: -5px;}
footer .instafeed-grid.gutter-very-small { margin-left: -3px; margin-right: -3px;}
footer .instafeed-grid.gutter-medium { margin-left: -7px; margin-right: -7px;}
footer .instafeed-grid.gutter-large { margin-left: -10px; margin-right: -10px;}
footer .instafeed-grid.gutter-extra-large { margin-left: -15px; margin-right: -15px;}
footer .instafeed-grid.gutter-double-extra-large { margin-left: -20px; margin-right: -20px;}

/* ===================================
    27. home and other pages
====================================== */

/* startup */
.home-startup .ares { background: rgba(0,0,0,0.5); transition: 0.3s ease-in-out; }
.home-startup .ares:before { color: #fff; font-family: 'feather' !important; }
.home-startup .ares.tp-leftarrow,.home-startup .ares.tparrows.tp-leftarrow:before { content: "\e828"; font-size: 19px; }
.home-startup .ares.tp-rightarrow,.home-startup .ares.tparrows.tp-rightarrow:before { content: "\e829"; font-size: 19px; }
.home-startup .ares.tparrows:hover:before { color: #fff; }
.home-startup .ares:hover { background: rgba(0,0,0,0.9); }
.home-startup .rs-btn:hover div,.home-startup .rs-btn:hover .fa-play { transition: 0.3s ease-in-out !important; }
.home-startup .rs-btn:hover div { background: #fff !important; }
.home-startup .rs-btn:hover .fa-play { color: #00B057 !important; }
.home-startup .tp-mask-wrap { display: inline !important; }
.home-startup .tp-bullet .tp-bullet-image, .home-startup .tp-bullet .tp-bullet-title { display: none !important; }
.footer-startup.footer-light a:hover { color: #27ae60;}

/* hotel and resort */
.footer-hotel-resort.footer-dark, .footer-hotel-resort.footer-dark a { color: rgba(255,255,255,.4); }
.footer-hotel-resort.footer-dark .footer-social a, .footer-hotel-resort .social-icon-style-12 .light li a:hover { color: #fff; }
.footer-hotel-resort.footer-dark a:hover { color: #c89965; }
.home-hotel-resort .hermes .tp-bullet{ overflow:hidden; border-radius:50%; width:10px; height:10px; background-color:rgba(255,255,255,0.2); box-shadow:inset 0 0 0 2px rgba(204,204,204,0); -webkit-transition:background 0.3s ease; transition:background 0.3s ease; position:absolute}
.home-hotel-resort .hermes .tp-bullet:hover {background-color:rgba(255,255,255,1)}
.home-hotel-resort .hermes .tp-bullet:after{ content:' '; position:absolute; bottom:0; height:0; left:0; width:100%; background-color:rgb(255,255,255); box-shadow:0 0 1px rgba(204,204,204,0); -webkit-transition:height 0.3s ease; transition:height 0.3s ease}
.home-hotel-resort .hermes .tp-bullet.selected:after{height:100%}
.home-hotel-resort .book-now-btn { transition: background-color 0.3s ease-in-out !important; padding: 10px 26px !important; }
.home-hotel-resort .book-now-btn:hover { background-color: #fff !important; color: #232323 !important; }

/* architecture */
.box-layout .navbar.navbar-boxed { padding-left: 75px; padding-right: 75px; }
.architecture.fancy-text-box-style-01 .fancy-text-box:before, .architecture.fancy-text-box-style-01 .fancy-text-box:after { border-color: rgba(130,130,130,.2); }
.home-architecture .metis.tparrows { width: 67px; height: 67px; }
.home-architecture .metis.tparrows:before { font-family: 'feather' !important; font-size: 20px; padding-top: 3px; }
.home-architecture .tparrows.tp-rightarrow { transform: matrix(1, 0, 0, 1, -67, -327) !important; }
.home-architecture .tparrows.tp-leftarrow { transform: matrix(1, 0, 0, 1, -67, -260) !important; }
.home-architecture .tparrows.tp-rightarrow:before { content: "\e829"; }
.home-architecture .tparrows.tp-leftarrow:before { content: "\e828"; }
.home-architecture .tparrows:hover { background: #23262d !important; }
.home-architecture .tparrows:hover:before { color: white; transform: scale(1) !important; }
.home-architecture-middle-text { top: 50%; transform: translateY(-50%); -webkit-transform: translateY(-50%); -moz-transform: translateY(-50%); -ms-transform: translateY(-50%); }

/* application */
.footer-application.footer-dark, .footer-application.footer-dark a { color: rgba(255,255,255,.5); }
.footer-application.footer-dark a:hover { color: #fff; }
.footer-application.footer-dark .footer-social a { color: #fff; }
.footer-application.footer-dark .footer-social a:hover { opacity: .5; }
.banner-bottom-right-images > img { bottom: -95px; position: relative; }

/* events and conference */
.footer-events-conference.footer-dark, .footer-events-conference.footer-dark a { color: rgba(255, 255, 255, .4);}
.footer-events-conference.footer-dark a:hover { color: #fff;}

/* spa salon */
.rev-nav-ares-effect .ares { background: none !important; opacity: 0; transition: 0.3s ease-in-out; }
.home-spa-salon .ares { overflow: hidden; background: transparent; }
.home-spa-salon .ares:before { font-family: 'feather' !important; font-size: 16px !important; color: #fff !important; transition: 0.3s ease-in-out !important; }
.home-spa-salon .ares:hover .tp-arr-titleholder { color: #FF8089 !important; }
.home-spa-salon .tp-leftarrow:before { content: '\e828' !important; left: -10px; }
.home-spa-salon .ares:hover:before { transform: translateX(-10px) !important; color: #FF8089 !important; }
.home-spa-salon .tp-rightarrow:before { content: '\e829' !important; right: -10px; }
.home-spa-salon .tp-rightarrow:hover:before { transform: translateX(10px) !important; }
.home-spa-salon .tp-arr-titleholder { top: -60px !important; left: 5px !important; color: #fff !important; font-size: 16px !important; font-weight: 300 !important; opacity: 1 !important; transform: translateX(0) !important; }
.home-spa-salon .tp-rightarrow .tp-arr-titleholder { left: auto !important; right: 15px !important; }
.home-spa-salon .explore-btn { transition: background 0.3s ease-in-out !important; z-index: 100 !important; }
.home-spa-salon .tp-tab-mask { overflow: visible !important; }
.home-spa-salon .tp-tab { opacity: 0.5 !important; width: 35px !important; height: 19px !important; }
.home-spa-salon .tp-tab.selected { opacity: 1 !important; }
.home-spa-salon .tp-tab.selected .tp-tab-title:before { content: ''; position: absolute; bottom: 0; left: 50%; width: 46%; height: 1px; background: #fff; transform: translateX(-50%); }
.home-spa-salon .tp-tab-title { background: transparent !important; color: #fff !important; padding: 0 !important; font-size: 15px !important; font-weight: 400 !important; font-family: 'Poppins', sans-serif !important;  }
.footer-spa-salon.footer-dark, .footer-spa-salon.footer-dark a { color: #828282;}
.footer-spa-salon.footer-dark a:hover { color: #fff;}

/* finance */
.footer-finance.footer-light a:hover { color: #8bb867;}
.footer-finance.footer-light .social-icon-style-12 li a:hover {opacity: 1; color: #828282}

/* seo agency */
.footer-seo-agency.footer-light a:hover { color: #374162}

/* consulting */
.footer-consulting.footer-dark .footer-top, .footer-consulting.footer-dark .footer-bottom, .footer-consulting.footer-dark .footer-top a, .footer-consulting.footer-dark .footer-bottom a { color: #828282;}
.footer-consulting.footer-dark .footer-top a:hover, .footer-consulting.footer-dark .footer-bottom a:hover { color: #fff;}
.footer-consulting.footer-dark .social-icon-style-12 li a:hover {opacity: 1}

/* home decor */
.home-decor .tp-caption.btn { transition: all 0.3s ease-in-out !important; }
.home-decor .tp-bullet { background: #BF8C4C !important; opacity: 0.6; border: 1px solid #BF8C4C!important; width: 10px !important; height: 10px !important; }
.home-decor .tp-bullet.selected { opacity: 0.2; }
.home-decor .tp-bullet:after { display: none !important; }
.footer-decor.footer-dark, .footer-decor.footer-dark a { color: #828282;}
.footer-decor.footer-dark a:hover { color: #cb933d;}
.footer-decor.footer-dark .social-icon-style-12 li a:hover {opacity: 1}

/* web agency */
.web-agency .process-step-style-02 .process-step-item:hover .process-step-icon { color: #0038e3; }

/* restaurant */
.footer-restaurant.footer-light a:hover { color: #ca943d;}

/* digital agency */
.footer-digital-agency .footer-logo img { max-height: inherit; }
.footer-digital-agency.footer-light a:hover { color: #e6994e;}
.footer-digital-agency.footer-light .social-icon-style-12 li a:hover {opacity: 1}

/* marketing agency */
.team-tulip-bubble { position: absolute; left: 10px; bottom: 160px; border-radius: 4px; padding: 40px 45px; }
.team-tulip-bubble:before { top: 100%; right: 50px; border: solid transparent; content: " "; height: 0; width: 0; position: absolute; border-color: rgba(237, 237, 237, 0); border-top-color: #F4D956; border-width: 18px 0 0 18px; }
.footer-marketing-agency.footer-light a:hover { color: #969896;}

/* photography */
.home-photography .ares { opacity: 0; background: transparent !important; min-width: 176px !important; transition: 0.3s ease-in-out; }
.home-photography .ares:before { content: '' !important; position: absolute; top: 50%; width: 0; height: 1px; background: linear-gradient(to left, #3edfb6, #58e49a, #7ce878, #a5e850, #d0e51b) !important; transition: width 0.3s ease-in-out !important;}
.home-photography .ares.tp-rightarrow:before { position: absolute; right: 0 !important; }
.home-photography .ares .tp-title-wrap { transform: scale(1) !important; background: transparent; margin: 0 !important; overflow: visible; }
.home-photography .ares .tp-arr-titleholder { opacity: 1; font-size: 18px; font-weight: 400; line-height: 20px; color: #828282; margin: 0 !important; padding: 0; transition: 0.3s ease-in-out; }
.home-photography .tparrows:hover .tp-arr-titleholder { color: #fff; }
.home-photography .ares:hover:before { width: 133px; }
.home-photography .ares .tp-title-wrap:before { content: "" !important; position: absolute; width: 133px; height: 1px; background: #28292B; top: 50%; transition: 0.3s ease-in-out; }
.home-photography .ares.tp-rightarrow:hover .tp-arr-titleholder { transform: translateX(-200px); }
.home-photography .ares.tp-leftarrow:hover .tp-arr-titleholder { transform: translateX(200px); }
.home-photography .tp-leftarrow .tp-title-wrap .tp-arr-titleholder { display: inline-block; left: -48px; }
.home-photography .tp-rightarrow .tp-title-wrap:before { right: 0; }
.home-photography .tp-rightarrow .tp-title-wrap .tp-arr-titleholder { display: inline-block; right: -52px; }
.home-photography .zeus { display: none !important; }
.home-photography .anim-text { animation: myanim 70s infinite; animation-timing-function: linear; }
.home-photography .anim-text2 { animation: myanim 70s infinite; animation-delay: 35s; animation-timing-function: linear; }
.footer-photography.footer-dark, .footer-photography.footer-dark a { color: #828282;}
.footer-photography.footer-dark a.btn { color: #c7da26;}
.footer-photography.footer-dark a:hover { color: #c7da26;}
.footer-photography.footer-dark .social-icon-style-12 li a:hover {opacity: 1}

/* dentist */
.footer-dentist.footer-light a:hover { color: #00B8B8;}

@keyframes myanim {
    0% {right: 0;}
    100% {right: 4000px;}
}

/* cafe */
.home-cafe .nav-tabs > li.nav-item > a.nav-link.active, .home-cafe.nav-tabs > li.nav-item > a.nav-link.active:hover, .home-cafe .nav-tabs > li.nav-item > a.nav-link:hover { color: #cd9452; }

/* creative agency */
.footer-creative-agency .footer-logo img { max-height: inherit; }
.footer-creative-agency.footer-dark, .footer-creative-agency.footer-dark a { color: #828282;}
.footer-creative-agency.footer-dark ul li a { font-size: 16px; line-height: 34px;}
.footer-creative-agency.footer-dark a:hover { color: #cc754c;}
.home-creative-agency.client-logo-style-06 .client-box { opacity: .25;}
.home-creative-agency.client-logo-style-06 .client-box:hover, .footer-creative-agency.footer-dark .social-icon-style-12 li a:hover { opacity: 1;}

/* design agency */
.footer-design-agency.footer-light a:hover { color: #ffcc2e;}

/* vertical portfolio */
.home-vertical-portfolio .navbar { padding: 0 6rem; }

/* furniture shop */
.home-furniture-shop .hermes .tp-bullet{ overflow:hidden; border-radius:50%; width:10px; height:10px; background-color:rgba(35,35,35,0.2); box-shadow:inset 0 0 0 2px rgba(204,204,204,0); -webkit-transition:background 0.3s ease; transition:background 0.3s ease; position:absolute}
.home-furniture-shop .hermes .tp-bullet:hover {background-color:rgba(35,35,35,1)}
.home-furniture-shop .hermes .tp-bullet:after{ content:' '; position:absolute; bottom:0; height:0; left:0; width:100%; background-color:rgb(35,35,35); box-shadow:0 0 1px rgba(204,204,204,0); -webkit-transition:height 0.3s ease; transition:height 0.3s ease}
.home-furniture-shop .hermes .tp-bullet.selected:after{height:100%}
.home-furniture-shop .tparrows.tp-leftarrow:before { content: "\e657"; font-family: 'icomoon'; color: #232323; opacity: 0.5; font-size: 45px; }
.home-furniture-shop .tparrows.tp-rightarrow:before { content: "\e658"; font-family: 'icomoon'; color: #232323; opacity: 0.5; font-size: 45px; }
.footer-furniture-shop.footer-dark .footer-top, .footer-furniture-shop.footer-dark .footer-top a, .footer-furniture-shop.footer-dark .footer-bottom, .footer-furniture-shop.footer-dark .footer-bottom a  { color: #828282;}
.footer-furniture-shop.footer-dark a:hover { color: #fff;}

/* fashion shop */
.home-fashion-shop .tp-tab { opacity: 0.5; }
.home-fashion-shop .tp-tab-mask { max-width: inherit !important; width: auto; padding: 0; margin: 0; }
.home-fashion-shop .tp-tab.selected span{ border-bottom: 2px solid #232323;}
.home-fashion-shop .tp-tabs:before { display: none; }
.footer-fashion-shop.footer-dark .footer-top, .footer-fashion-shop.footer-dark .footer-top  a{ color: #828282;}
.footer-fashion-shop.footer-dark a:hover { color: #fff;}

/* magazine */
.footer-magazine.footer-dark, .footer-magazine.footer-dark a { color: #828282;}
.footer-magazine.footer-dark a:hover { color: #fff;}
.home-magazine.blog-metro .post-details .blog-category { font-size: 12px; line-height: 20px; font-weight: 500; text-transform: uppercase; color: #fff; padding: 6px 13px; background: #c89965; border-radius: 2px; }
.home-magazine.blog-metro .post-details:hover .blog-category {background: #fff; color: #232323; box-shadow: 0 0 20px rgba(23,23,23,.2);}

/* lifestyle blog */
.footer-lifestyle-blog.footer-dark .footer-top, .footer-lifestyle-blog.footer-dark .footer-top  a{ color: #828282;}
.footer-lifestyle-blog.footer-dark a:hover { color: #fff;}

/* features header pages */
.footer-dark-header.footer-dark, .footer-dark-header.footer-dark a, .footer-header-with-push.footer-dark, .footer-header-with-push.footer-dark a, .footer-hamburger-menu-modern.footer-dark, .footer-hamburger-menu-modern.footer-dark a, .footer-hamburger-menu-half.footer-dark, .footer-hamburger-menu-half.footer-dark a, .footer-left-menu-modern, .footer-left-menu-modern.footer-dark a, .footer-responsive-sticky, .footer-responsive-sticky.footer-dark a  { color: #828282;}
.footer-dark-header.footer-dark a:hover, .footer-header-with-push.footer-dark a:hover, .footer-hamburger-menu-modern a:hover, .footer-hamburger-menu-half a:hover, .footer-left-menu-modern a:hover, .footer-responsive-sticky a:hover { color: #fff;}

/* what we offers */
.cd-headline.slide span b { border-top-width: 0px !important; border-left-width: 0px !important; border-right-width: 0px !important; }

/* onepage */
.footer-onepage.footer-dark, .footer-onepage.footer-dark a { color: #828282;}
.footer-onepage.footer-dark a:hover { color: #F4D956;}

/* landing page */
.litho-parallax-bg { width: 870px; }
.litho-landing-header .navbar-brand:after { background: rgba(130,130,130,.35); content: ""; height: calc(100% - 18px); position: absolute; right: 0; top: 50%; transform: translateY(-50%); width: 1px; }
.customer-bg-section { position: absolute; left: -70px; bottom: 150px; }

/* consulting */
.home-dentist .popup-youtube.absolute-middle-center { left: 100%; }

/* ===================================
    28. Others
====================================== */

/* outside box */
.outside-box-left { margin-left: -30vw; }
.outside-box-right { margin-right: -30vw; }
.outside-box-bottom { margin-bottom: -14vw; }

/* outside box text */
.outside-box-text-left { margin-left: -50vw; }
.outside-box-text-right { margin-right: -50vw; }

/* contact form */
.required.error { border:1px solid #ff0000 !important; }
input[type="checkbox"].error { box-shadow: 0px 0px 0px 1px rgb(255 0 0), 1px 1px 0px 0px rgb(255 0 0);}
.search-error { border: 1px solid red !important; }
.search-form-box .search-error { border: 0 !important; border-bottom: 1px solid red !important; }
.input-border-bottom.required.error {border-bottom:1px solid #ff0000 !important; border-top: none !important; border-left: none !important; border-right: none !important}
.g-recaptcha.error iframe { border: 1px solid #ff0000; }
.form-results {padding: 13px 20px; clear: both; width: 100%; margin-top: 20px; font-size: 14px; line-height: 22px; z-index: 2; max-width: 100%; flex: 0 0 100%; -ms-flex: 0 0 100%;}

/* page loading */
@keyframes spin { to { -webkit-transform: rotate(360deg); -ms-transform: rotate(360deg); transform: rotate(360deg); } }
.loading { position: relative; opacity: 1 !important; transition: all 0s ease-in-out !important; -moz-transition: all 0s ease-in-out !important; -webkit-transition: all 0s ease-in-out !important; -ms-transition: all 0s ease-in-out !important; -o-transition: all 0s ease-in-out !important; z-index: 99; }
.loading::after { background: none; content: "" !important; position: absolute; left: calc(50% - 10px) !important; top: calc(50% - 10px) !important; width: 20px !important; height: 20px !important; opacity: 1 !important; right: inherit !important; z-index: 9; text-align: center; border: 2px solid rgba(23, 23, 23, 0.2); border-top-color: rgba(23, 23, 23, 0.7); border-radius: 50%; animation: spin 1s linear infinite; -webkit-animation: spin 1s linear infinite; }
.loading:before { content: ""; background-color: rgba(255, 255, 255, .8) !important; position: absolute; right: 2px; top: 1px; width: calc(100% - 4px); height: calc(100% - 2px); border-radius: inherit; z-index: 1; }
.loading.dark:before { background-color: rgba(0, 0, 0, .8) !important; }
.loading.dark::after { border: 2px solid rgba(255, 255, 255, 0.2);  border-top-color: rgba(255, 255, 255, 0.7); }
.page-loader { position: absolute; width: 100%; height: 100%; left: 0; top: 0; background-color: rgba(255, 255, 255, 1) !important; opacity: 1 !important; transition: all 0s ease-in-out !important; -moz-transition: all 0s ease-in-out !important; -webkit-transition: all 0s ease-in-out !important; -ms-transition: all 0s ease-in-out !important; -o-transition: all 0s ease-in-out !important; z-index: 99999; }
.page-loader::after { background: none; content: "" !important; position: absolute; left: calc(50% - 25px) !important; top: calc(50% - 25px) !important; width: 50px !important; height: 50px !important; opacity: 1 !important; right: inherit !important; z-index: 9; text-align: center; border: 2px solid rgba(23, 23, 23, 0.2); border-top-color: rgba(23, 23, 23, 0.7); border-radius: 50%; animation: spin 1s linear infinite; -webkit-animation: spin 1s linear infinite; }
.grid-loading { overflow: hidden; position: relative; opacity: 1 !important; transition: all 0s ease-in-out !important; -moz-transition: all 0s ease-in-out !important; -webkit-transition: all 0s ease-in-out !important; -ms-transition: all 0s ease-in-out !important; -o-transition: all 0s ease-in-out !important; z-index: 99; }
.grid-loading::after { background: none; content: "" !important; position: absolute; top: 100px !important; left: calc(50% - 25px) !important; width: 50px !important; height: 50px !important; opacity: 1 !important; right: inherit !important; z-index: 9; text-align: center; border: 3px solid rgba(23, 23, 23, 0.2); border-top-color: rgba(23, 23, 23, 0.7); border-radius: 50%; animation: spin 1s linear infinite; -webkit-animation: spin 1s linear infinite; }
.grid-loading:before { content: ""; position: absolute; right: 2px; top: 1px; width: calc(100% - 4px); height: calc(100% - 2px); border-radius: inherit; z-index: 1; }
.grid-loading .grid-item { visibility: hidden; }
.grid-loading li.grid-item { overflow: hidden; opacity: 0; }

/*grid*/
.show-grid [class^="col-"] { background-color: #f5f5f5; border: 1px solid rgba(0, 0, 0, 0.2); margin-bottom: 15px; padding: 10px 0; text-align: center; }

/* for internet explorer 11 */
@media screen\0 {
    .menu-logo-center .menu-logo { transform: translateY(-50%); -webkit-transform: translateY(-50%); -moz-transform: translateY(-50%); -ms-transform: translateY(-50%); -o-transform: translateY(-50%); }

    /* image mask */
    .image-mask { background-image: none !important; }
    [class*="text-gradient-"] { background: inherit; -webkit-background-clip: inherit; -webkit-text-fill-color: inherit; }
    
    /* text gradient color */
    .text-gradient-magenta-orange { color: #e42464; }
    .text-gradient-magenta-orange-2 { color: #ec606c; }
    .text-gradient-purple-magenta { color: #502970; }
    .text-gradient-light-purple-light-orange { color: #b783ff; }
    .text-gradient-sky-blue-pink { color: #556fff; }
    .text-gradient-peacock-blue-crome-yellow { color: #36c1e1; }
    .text-gradient-peacock-blue-crome-yellow-2 { color: #5dcaa3; }
    .text-gradient-fast-blue-purple { color: #0039e3; }
    .text-gradient-tan-geraldine { color: #fb7f87; }
    .text-gradient-tussock-greenish-slate { color: #bf8c4c; }
    .text-gradient-light-purple-light-red { color: #bb85f9; }
    .text-gradient-light-brownish-orange-black { color: #e79e57; }
    .text-gradient-turquoise-green-yellowish { color: #3edfb6; }
    .text-gradient-red-violet-purple { color: #b93c90; }
    .text-gradient-fuego-turquoise { color: #0cd6a2 }
    .text-gradient-orange-pink, .btn-link.text-gradient-orange-pink, a.text-gradient-orange-pink, a.text-gradient-orange-pink-hover:hover { color: #ff6557; }

    /* input size */
    input { line-height: normal; }
    .small-input { min-height: 44px; line-height: normal; }
    .medium-input, .medium-select select { min-height: 50px; line-height: normal; }
    .large-input { min-height: 57px; line-height: normal; }
    .newsletter-style-02 form { line-height: normal; }
    .alert-dismissible .close { height: 100%;}

    /* other */
    .blog-comment li .btn-reply { border: 1px solid #939393; line-height: normal;}
    .price-filter-details .price-filter-amount .price-amount { line-height: normal; }
    .coupon-code-panel input { line-height: normal; }
    .blog-post .blog-post-image img { transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out; }
    .portfolio-bordered .portfolio-box .portfolio-hover { left: 20px; top: 20px; width: calc(100% - 40px); height: calc(100% - 40px); }
    .box-shadow-very-light-hover:hover, .box-shadow-light-hover:hover, .box-shadow-dark-hover:hover, .box-shadow-large-hover:hover, .box-shadow-extra-large-hover:hover, .box-shadow-medium-hover:hover, .box-shadow-small-hover:hover, .box-shadow-double-large-hover:hover { -webkit-transform: none; -moz-transform: none; -ms-transform: none; -o-transform: none; transform: none;}
    .instagram-follow-api { width: 100%; }
    .process-step-style-02 .process-step-item { width: 100%; }

    /* home branding agency */
    .drawing-animation-wrapper .drawing-animation {width: 100% !important; height: 100% !important;}
    .drawing-animation path, .drawing-animation circle, drawing-animation rect, .drawing-animation polygon, .drawing-animation ellipse { stroke-dasharray: inherit; stroke-dashoffset: inherit; -webkit-animation: drawing-animation 0s linear forwards; animation: drawing-animation 0s linear forwards; }
    .drawing-animation-wrapper svg{transform: scale(3.3) translateX(100px) !important;}
    
    /* shop category style 02 */
    .shop-category-style-02 .shop-product-image img { width: 100%;}
    
    /* tab style 01 */
    .tab-style-01 .nav-tabs > li.nav-item > a.nav-link:hover +.tab-border { opacity: 1; width: 100%; visibility: visible; }
    .parallax, .fix-background {background-position: top center !important; background-attachment: inherit !important;}
    
}

/* for retina */
@media only screen and (-webkit-min-device-pixel-ratio: 1.5), only screen and (min-resolution: 144dpi), only screen and (min-resolution: 1.5dppx) {
    .show-notify-popup .mfp-container { -webkit-animation-duration: 0.1s; -moz-animation-duration: 0.1s; -o-animation-duration: 0.1s; animation-duration: 0.1s; }
}

/* animation keyframe */
@-webkit-keyframes down-arrow { 0% { opacity:0; top:0px; }18% { opacity:0.2; top:3px; }36% { opacity:.4; top:8px; }54% { opacity:.6; top:18px; }72% { opacity:.8; top:18px; }100% { opacity:1; top:18px; }}
@keyframes down-arrow { 0% { opacity:0; top:0px; }18% { opacity:.0.2; top:3px; }36% { opacity:.4; top:8px; }54% { opacity:.6; top:18px; }72% { opacity:.8; top:18px; }100% { opacity:1; top:18px; }}

@-webkit-keyframes social-icon-animation { 0% { top: 130px; left: -100px; }80% { top: 30px; left: -50px; }100% { top: -30px; left: -15px; }}
@keyframes social-icon-animation { 0% { top: 130px; left: -100px; }80% { top: 30px; left: -50px; }100% { top: -30px; left: -15px; }}

@-webkit-keyframes ani-mfp { 0% { -webkit-transform: translateY(-100%); transform: translateY(-100%); visibility: hidden; opacity: 0; } 100% { -webkit-transform: translateY(0); transform: translateY(0); visibility: visible; opacity: 1; }}
@keyframes ani-mfp { 0% { -webkit-transform: translateY(-100%); transform: translateY(-100%); visibility: hidden; opacity: 0; } 100% { -webkit-transform: translateY(0); transform: translateY(0); visibility: visible; opacity: 1; }}

@-webkit-keyframes ani-mfp-close { 0% { -webkit-transform: translateY(0); transform: translateY(0); visibility: visible; opacity: 1; } 100% { -webkit-transform: translateY(-100%); transform: translateY(-100%); visibility: hidden; opacity: 0; }}
@keyframes ani-mfp-close { 0% { -webkit-transform: translateY(0); transform: translateY(0); visibility: visible; opacity: 1; } 100% { -webkit-transform: translateY(-100%); transform: translateY(-100%); visibility: hidden; opacity: 0; }}

@-webkit-keyframes comingsoonpopup { 0% { -webkit-transform: translateY(100%); transform: translateY(100%); visibility: hidden; opacity: 0; } 100% { -webkit-transform: translateY(0); transform: translateY(0); visibility: visible; opacity: 1; }}
@keyframes comingsoonpopup { 0% { -webkit-transform: translateY(100%); transform: translateY(100%); visibility: hidden; opacity: 0; } 100% { -webkit-transform: translateY(0); transform: translateY(0); visibility: visible; opacity: 1; }}

@-webkit-keyframes comingsoonpopupclose { 0% { -webkit-transform: translateY(0); transform: translateY(0); visibility: visible; opacity: 1; } 100% { -webkit-transform: translateY(100%); transform: translateY(100%); visibility: hidden; opacity: 0; }}
@keyframes comingsoonpopupclose { 0% { -webkit-transform: translateY(0); transform: translateY(0); visibility: visible; opacity: 1; } 100% { -webkit-transform: translateY(100%); transform: translateY(100%); visibility: hidden; opacity: 0; }}

@-webkit-keyframes drawing-animation { to { stroke-dashoffset: 0; }}
@keyframes drawing-animation { to { stroke-dashoffset: 0; }}

@-webkit-keyframes fadeIn { 0% { opacity: 0; }100% { opacity: 1; }}
@keyframes fadeIn { 0% { opacity: 0; }100% { opacity: 1; }}

@-webkit-keyframes fromTop { 0%{left: 0; top: -100%} 100%{left: 0; top: 0} }
@keyframes fromTop { 0%{left: 0; top: -100%} 100%{left: 0; top: 0} }

@-webkit-keyframes toTop { 0%{left: 0; top: 0} 100%{left: 0; top: -100%} }
@keyframes toTop { 0%{left: 0; top: 0} 100%{left: 0; top: -100%} }

@-webkit-keyframes fromRight { 0%{top: 0; left: 100%} 100%{top: 0; left: 0} }
@keyframes fromRight { 0%{top: 0; left: 100%} 100%{top: 0; left: 0} }

@-webkit-keyframes toRight { 0%{top: 0; left: 0} 100%{top: 0; left: 100%} }
@keyframes toRight { 0%{top: 0; left: 0} 100%{top: 0; left: 100%} }

@-webkit-keyframes fromBottom { 0%{left:0; top: 100%} 100%{left:0; top: 0} }
@keyframes fromBottom { 0%{left:0; top: 100%} 100%{left:0; top: 0} }

@-webkit-keyframes toBottom { 0%{left: 0; top: 0} 100%{left: 0; top: 100%} }
@keyframes toBottom { 0%{left: 0; top: 0} 100%{left: 0; top: 100%} }

@-webkit-keyframes fromLeft { 0%{top: 0; left: -100%} 100%{top: 0; left: 0} }
@keyframes fromLeft { 0%{top: 0; left: -100%} 100%{top: 0; left: 0} }

@-webkit-keyframes toLeft { 0%{top: 0; left: 0} 100%{top: 0; left: -100%} }
@keyframes toLeft { 0%{top: 0; left: 0} 100%{top: 0; left: -100%} }

@-webkit-keyframes video-icon-sonar { 0% { opacity: 1; -webkit-transform: scale(0.1); transform: scale(0.1); } 100% { opacity: 0; -webkit-transform: scale(1.10); transform: scale(1.10); } }
@keyframes video-icon-sonar { 0% { opacity: 1; -webkit-transform: scale(0.1); transform: scale(0.1); } 100% { opacity: 0; -webkit-transform: scale(1.10); transform: scale(1.10); } }



/* for safari */
@media screen and (-webkit-min-device-pixel-ratio:0) {
    .border-gradient-magenta-orange { border-image: linear-gradient(to right top, #e42464, #ed3c5f, #f44f5c, #fa6259, #ff7357) 1 / 0 0 1 0; border-image-slice: 1;  }
    .border-gradient-purple-magenta { border-image: linear-gradient(to right top, #502970, #7f2977, #ac2775, #d42d6b, #f34259) 1 / 0 0 1 0; border-image-slice: 1; }
    .border-gradient-light-purple-light-orange { border-image: linear-gradient(to right top, #b783ff, #e37be0, #fa7cc1, #ff85a6, #ff9393) 1 / 0 0 1 0; border-image-slice: 1; }
    .border-gradient-sky-blue-pink { border-image: linear-gradient(to right top, #556fff, #b263e4, #e05fc4, #f767a6, #ff798e) 1 / 0 0 1 0; border-image-slice: 1; }
    .border-gradient-peacock-blue-crome-yellow { border-image: linear-gradient(to right top, #36c1e1, #28ccc0, #74cf8e, #bac962, #feb95b) 1 / 0 0 1 0; border-image-slice: 1; }
    .border-gradient-fast-blue-purple { border-image: linear-gradient(to right top, #0039e3, #4132e0, #5e28dd, #741bd9, #8600d4) 1 / 0 0 1 0; border-image-slice: 1; }
    .border-gradient-light-purple-light-red { border-image: linear-gradient(to right top, #bb85f9, #c887e5, #da8bca, #eb8fb0, #fb9398) 1 / 0 0 1 0; border-image-slice: 1; }
    .border-gradient-light-brownish-orange-black { border-image: linear-gradient(to right top, #e79e57, #9d9049, #62794c, #405c4c, #353d3c) 1 / 0 0 1 0; border-image-slice: 1; }
    .border-gradient-red-violet-purple { border-image: linear-gradient(to right, #b93d90, #a92b95, #951b9c, #7a11a4, #5412ad) 1 / 0 0 1 0; border-image-slice: 1; }    
    .btn.btn-transparent-gradient-sky-blue-pink { border-color: #000000;}
    
}

/* for only safari */
@media not all and (min-resolution:.001dpcm) {
    .tilt-box:not(:root:root) {  z-index: -1; }
    .interactive-banners-style-01 [class^="border-radius-"], .interactive-banners-style-01 [class*=" border-radius-"], .interactive-banners-style-07 [class^="border-radius-"], .interactive-banners-style-07 [class*=" border-radius-"], .interactive-banners-style-07[class*=" border-radius-"], .interactive-banners-style-08[class*=" border-radius-"], .interactive-banners-style-10 [class^="border-radius-"], .interactive-banners-style-10 [class*=" border-radius-"], .team-style-01 [class^="border-radius-"], .team-style-01 [class*=" border-radius-"], [class^="border-radius-"] .blog-post-image, [class*=" border-radius-"] .blog-post-image, .product-box .product-image[class*=" border-radius-"], .instafeed-grid figure { -webkit-mask-image: -webkit-radial-gradient(white, black); }
}

/* for buy and demo button */
.theme-demos { display: none; width: 655px; height: 100%; min-height: 100%; position: fixed; background-color: #fff; right: -655px; top: 0; z-index: 1050; -webkit-transition: all .5s cubic-bezier(0.77, 0, 0.175, 1); transition: all .5s cubic-bezier(0.77, 0, 0.175, 1); }
.show-theme-demos .theme-demos { display: block; }
.theme-demos section { height: 100%; min-height: 100%; position: relative; z-index: 11; padding: 0; width: 655px; }
.theme-demos .demos-wrapper { height: 100%; overflow-y: hidden; padding: 40px 25px; background-color: #fff; width: 100%;  }
.theme-demos.active { -webkit-transform: translateX(-100%); transform: translateX(-100%); box-shadow: 0 0 45px 0 rgba(0, 0, 0, 0.25); }
.theme-demos .grid { padding: 0 20px;}
.theme-demos .buy-theme, .theme-demos .all-demo { position: absolute; left: -150px; bottom: 195px; transform: translateY(-50%) translateY(-7px); background-color: #fff; cursor: pointer; z-index: 10; transition: all .25s cubic-bezier(0.23, 1, 0.32, 1) .45s; min-width: 150px; }
.all-demo {box-shadow: 0px 0px 15px 0 rgba(0, 0, 0, 0.15); }
.theme-demos .buy-theme { bottom: 125px; text-align: center; background: #232323 }
.theme-demos .buy-theme a, .theme-demos .all-demo a { color: #232323; font-size: 11px; padding:12px 15px; display: block; text-decoration: none; font-weight: 500; }
.theme-demos .buy-theme a > i, .theme-demos .all-demo a > i { position: absolute; left: 0; top: 0; height: 100%; width: 59px; line-height: 55px; text-align: center; opacity: 0; font-size: 24px; transition-delay: 0.3s; }
.theme-demos .buy-theme .theme-wrapper, .theme-demos .all-demo .theme-wrapper { padding: 0 9px; position: relative; top: 0; opacity: 1; transition-delay: 0.3s; }
.theme-demos .buy-theme .theme-wrapper span, .theme-demos .all-demo .theme-wrapper span { font-size: 15px; font-weight: 600;  }
.theme-demos .buy-theme .theme-wrapper span { margin-right: 0; }
.theme-demos .buy-theme .theme-wrapper i, .theme-demos .all-demo .theme-wrapper i {margin-right: 10px; font-size: 21px;}
.theme-demos .buy-theme .theme-wrapper > div, .theme-demos .all-demo .theme-wrapper > div { display: inline-block; vertical-align: middle; font-size: 15px; line-height: 15px}
.theme-demos .buy-theme .theme-wrapper > div { color: #fff; font-size: 14px; text-transform: uppercase; line-height: 23px; }
.theme-demos .all-demo .theme-wrapper > div { color: #232323; font-size: 14px; text-transform: uppercase; line-height: 23px; }
.theme-demos.active .buy-theme, .theme-demos.active .all-demo { transition-timing-function: ease-in-out; -moz-transition-timing-function: ease-in-out; -webkit-transition-timing-function: ease-in-out; -o-transition-timing-function: ease-in-out; transition-duration: .2s; -moz-transition-duration: .2s; -webkit-transition-duration: .2s; -o-transition-duration: .2s; -webkit-transition: all .2s cubic-bezier(0.23, 1, 0.32, 1); transition: all .2s cubic-bezier(0.23, 1, 0.32, 1); -webkit-transform: translateX(107px) translateY(-50%) translateY(-7px); transform: translateX(107px) translateY(-50%) translateY(-7px); min-width: 50px; left: -163px; }
.theme-demos.active .buy-theme a > i, .theme-demos.active .all-demo a > i { opacity: 1; transition-delay: 0.3s; }
.theme-demos.active .buy-theme a > i {color: #fff}
.theme-demos.active .buy-theme .theme-wrapper, .theme-demos.active .all-demo .theme-wrapper { opacity: 0; transition-delay: 0s; }
.theme-demos .mCustomScrollBox { display: -webkit-box; display: -ms-flexbox; display: flex; width: 100%; }
.theme-demos .mCustomScrollBox { display: -webkit-box; display: -ms-flexbox; display: flex; }
.theme-demos .mCustomScrollBox .mCSB_container.mCS_y_hidden.mCS_no_scrollbar_y { -ms-flex-item-align: center; align-self: center; }
.theme-demos .mCustomScrollBox .mCSB_container { width: 100%; -ms-flex-item-align: start; align-self: flex-start; margin:0; padding-right: 15px !important; }
.theme-demos .clearfix { clear: both;}
.show-search-popup .theme-demos, .show-menu .theme-demos { z-index: 1;}

/* cookie message */
.gdpr-wrapper { top: 0 !important; bottom: 0; position: fixed; width: 100%; height: 100%; z-index: 9999; background-color: rgba(0, 0, 0, .8); }
.gdpr-container { position: absolute; bottom: 0; width: 100%; padding: 20px 20px; }
.gdpr-container .gdpr-content { display: inline-block; margin-right: 20px;}
.gdpr-container .btn { display: inline-block; }