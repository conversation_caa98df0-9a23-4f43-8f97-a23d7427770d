@charset "UTF-8";

/*!
 * Litho - v1.1
 * https://www.themezaa.com/
 * Copyright (c) 2021 themezaa.com
 */

@media (min-width: 1901px) {
    .litho-parallax-bg { width: 45%; }
}

@media (max-width: 1600px) {
    /* reset */
    .xl-last-order { order: 10; }

    /* typography */
    .text-extra-big { font-size: 200px; line-height: 200px; }

    /* letter spacing minus */
    .xl-letter-spacing-minus-1-half { letter-spacing: -0.50px; }
    .xl-letter-spacing-minus-1px { letter-spacing: -1px; }
    .xl-letter-spacing-minus-2px { letter-spacing: -2px; }
    .xl-letter-spacing-minus-3px { letter-spacing: -3px; }
    .xl-letter-spacing-minus-4px { letter-spacing: -4px; }
    .xl-letter-spacing-minus-5px { letter-spacing: -5px; }

    /* absolute middle center */
    .xl-absolute-middle-center { left: 50%; top: 50%; position: absolute; -ms-transform: translateX(-50%) translateY(-50%); -moz-transform: translateX(-50%) translateY(-50%); -webkit-transform: translateX(-50%) translateY(-50%); transform: translateX(-50%) translateY(-50%); }

    /* box shadow */
    .xl-box-shadow-none { box-shadow: none; }

    /* margin */
    .xl-margin-one-all { margin:1%; }
    .xl-margin-two-all { margin:2%; }
    .xl-margin-three-all { margin:3%; }
    .xl-margin-four-all { margin:4%; }
    .xl-margin-five-all { margin:5%; }
    .xl-margin-six-all { margin:6%; }
    .xl-margin-seven-all { margin:7%; }
    .xl-margin-eight-all { margin:8%; }
    .xl-margin-nine-all { margin:9%; }
    .xl-margin-ten-all { margin:10%; }
    .xl-margin-eleven-all { margin:11%; }
    .xl-margin-twelve-all { margin:12%; }
    .xl-margin-thirteen-all { margin:13%; }
    .xl-margin-fourteen-all { margin:14%; }
    .xl-margin-fifteen-all { margin:15%; }
    .xl-margin-sixteen-all { margin:16%; }
    .xl-margin-seventeen-all { margin:17%; }
    .xl-margin-eighteen-all { margin:18%; }
    .xl-margin-nineteen-all { margin:19%; }
    .xl-margin-twenty-all { margin:20%; }
    .xl-margin-5px-all { margin:5px; }
    .xl-margin-10px-all { margin:10px; }
    .xl-margin-15px-all { margin:15px; }
    .xl-margin-20px-all { margin:20px; }
    .xl-margin-25px-all { margin:25px; }
    .xl-margin-30px-all { margin:30px; }
    .xl-margin-35px-all { margin:35px; }
    .xl-margin-40px-all { margin:40px; }
    .xl-margin-45px-all { margin:45px; }
    .xl-margin-50px-all { margin:50px; }
    .xl-margin-55px-all { margin:55px; }
    .xl-margin-60px-all { margin:60px; }
    .xl-margin-65px-all { margin:65px; }
    .xl-margin-70px-all { margin:70px; }
    .xl-margin-75px-all { margin:75px; }
    .xl-margin-80px-all { margin:80px; }
    .xl-margin-85px-all { margin:85px; }
    .xl-margin-90px-all { margin:90px; }
    .xl-margin-95px-all { margin:95px; }
    .xl-margin-100px-all { margin:100px; }
    .xl-margin-1-rem-all { margin: 1rem; }
    .xl-margin-1-half-rem-all { margin: 1.5rem; }
    .xl-margin-2-rem-all { margin: 2rem; }
    .xl-margin-2-half-rem-all { margin: 2.5rem; }
    .xl-margin-3-rem-all { margin: 3rem; }
    .xl-margin-3-half-rem-all { margin: 3.5rem; }
    .xl-margin-4-rem-all { margin: 4rem; }
    .xl-margin-4-half-rem-all { margin: 4.5rem; }
    .xl-margin-5-rem-all { margin: 5rem; }
    .xl-margin-5-half-rem-all { margin: 5.5rem; }
    .xl-margin-6-rem-all { margin: 6rem; }
    .xl-margin-6-half-rem-all { margin: 6.5rem; }
    .xl-margin-7-rem-all { margin: 7rem; }
    .xl-margin-7-half-rem-all { margin: 7.5rem; }
    .xl-margin-8-rem-all { margin: 8rem; }
    .xl-margin-8-half-rem-all { margin: 8.5rem; }
    .xl-margin-9-rem-all { margin: 9rem; }
    .xl-margin-9-half-rem-all { margin: 9.5rem; }
    .xl-margin-10-rem-all { margin: 10rem; }
    .xl-margin-10-half-rem-all { margin: 10.5rem; }

    /* margin top */
    .xl-margin-one-top { margin-top:1%; }
    .xl-margin-two-top { margin-top:2%; }
    .xl-margin-three-top { margin-top:3%; }
    .xl-margin-four-top { margin-top:4%; }
    .xl-margin-five-top { margin-top:5%; }
    .xl-margin-six-top { margin-top:6%; }
    .xl-margin-seven-top { margin-top:7%; }
    .xl-margin-eight-top { margin-top:8%; }
    .xl-margin-nine-top { margin-top:9%; }
    .xl-margin-ten-top { margin-top:10%; }
    .xl-margin-eleven-top { margin-top:11%; }
    .xl-margin-twelve-top { margin-top:12%; }
    .xl-margin-thirteen-top { margin-top:13%; }
    .xl-margin-fourteen-top { margin-top:14%; }
    .xl-margin-fifteen-top { margin-top:15%; }
    .xl-margin-sixteen-top { margin-top:16%; }
    .xl-margin-seventeen-top { margin-top:17%; }
    .xl-margin-eighteen-top { margin-top:18%; }
    .xl-margin-nineteen-top { margin-top:19%; }
    .xl-margin-twenty-top { margin-top:20%; }
    .xl-margin-5px-top { margin-top:5px; }
    .xl-margin-10px-top { margin-top:10px; }
    .xl-margin-15px-top { margin-top:15px; }
    .xl-margin-20px-top { margin-top:20px; }
    .xl-margin-25px-top { margin-top:25px; }
    .xl-margin-30px-top { margin-top:30px; }
    .xl-margin-35px-top { margin-top:35px; }
    .xl-margin-40px-top { margin-top:40px; }
    .xl-margin-45px-top { margin-top:45px; }
    .xl-margin-50px-top { margin-top:50px; }
    .xl-margin-55px-top { margin-top:55px; }
    .xl-margin-60px-top { margin-top:60px; }
    .xl-margin-65px-top { margin-top:65px; }
    .xl-margin-70px-top { margin-top:70px; }
    .xl-margin-75px-top { margin-top:75px; }
    .xl-margin-80px-top { margin-top:80px; }
    .xl-margin-85px-top { margin-top:85px; }
    .xl-margin-90px-top { margin-top:90px; }
    .xl-margin-95px-top { margin-top:95px; }
    .xl-margin-100px-top { margin-top:100px; }
    .xl-margin-1-rem-top { margin-top: 1rem; }
    .xl-margin-1-half-rem-top { margin-top: 1.5rem; }
    .xl-margin-2-rem-top { margin-top: 2rem; }
    .xl-margin-2-half-rem-top { margin-top: 2.5rem; }
    .xl-margin-3-rem-top { margin-top: 3rem; }
    .xl-margin-3-half-rem-top { margin-top: 3.5rem; }
    .xl-margin-4-rem-top { margin-top: 4rem; }
    .xl-margin-4-half-rem-top { margin-top: 4.5rem; }
    .xl-margin-5-rem-top { margin-top: 5rem; }
    .xl-margin-5-half-rem-top { margin-top: 5.5rem; }
    .xl-margin-6-rem-top { margin-top: 6rem; }
    .xl-margin-6-half-rem-top { margin-top: 6.5rem; }
    .xl-margin-7-rem-top { margin-top: 7rem; }
    .xl-margin-7-half-rem-top { margin-top: 7.5rem; }
    .xl-margin-8-rem-top { margin-top: 8rem; }
    .xl-margin-8-half-rem-top { margin-top: 8.5rem; }
    .xl-margin-9-rem-top { margin-top: 9rem; }
    .xl-margin-9-half-rem-top { margin-top: 9.5rem; }
    .xl-margin-10-rem-top { margin-top: 10rem; }
    .xl-margin-10-half-rem-top { margin-top: 10.5rem; }

    /* margin bottom */
    .xl-margin-one-bottom { margin-bottom:1%; }
    .xl-margin-two-bottom { margin-bottom:2%; }
    .xl-margin-three-bottom { margin-bottom:3%; }
    .xl-margin-four-bottom { margin-bottom:4%; }
    .xl-margin-five-bottom { margin-bottom:5%; }
    .xl-margin-six-bottom { margin-bottom:6%; }
    .xl-margin-seven-bottom { margin-bottom:7%; }
    .xl-margin-eight-bottom { margin-bottom:8%; }
    .xl-margin-nine-bottom { margin-bottom:9%; }
    .xl-margin-ten-bottom { margin-bottom:10%; }
    .xl-margin-eleven-bottom { margin-bottom:11%; }
    .xl-margin-twelve-bottom { margin-bottom:12%; }
    .xl-margin-thirteen-bottom { margin-bottom:13%; }
    .xl-margin-fourteen-bottom { margin-bottom:14%; }
    .xl-margin-fifteen-bottom { margin-bottom:15%; }
    .xl-margin-sixteen-bottom { margin-bottom:16%; }
    .xl-margin-seventeen-bottom { margin-bottom:17%; }
    .xl-margin-eighteen-bottom { margin-bottom:18%; }
    .xl-margin-nineteen-bottom { margin-bottom:19%; }
    .xl-margin-twenty-bottom { margin-bottom:20%; }
    .xl-margin-5px-bottom { margin-bottom:5px; }
    .xl-margin-10px-bottom { margin-bottom:10px; }
    .xl-margin-15px-bottom { margin-bottom:15px; }
    .xl-margin-20px-bottom { margin-bottom:20px; }
    .xl-margin-25px-bottom { margin-bottom:25px; }
    .xl-margin-30px-bottom { margin-bottom:30px; }
    .xl-margin-35px-bottom { margin-bottom:35px; }
    .xl-margin-40px-bottom { margin-bottom:40px; }
    .xl-margin-45px-bottom { margin-bottom:45px; }
    .xl-margin-50px-bottom { margin-bottom:50px; }
    .xl-margin-55px-bottom { margin-bottom:55px; }
    .xl-margin-60px-bottom { margin-bottom:60px; }
    .xl-margin-65px-bottom { margin-bottom:65px; }
    .xl-margin-70px-bottom { margin-bottom:70px; }
    .xl-margin-75px-bottom { margin-bottom:75px; }
    .xl-margin-80px-bottom { margin-bottom:80px; }
    .xl-margin-85px-bottom { margin-bottom:85px; }
    .xl-margin-90px-bottom { margin-bottom:90px; }
    .xl-margin-95px-bottom { margin-bottom:95px; }
    .xl-margin-100px-bottom { margin-bottom:100px; }
    .xl-margin-1-rem-bottom { margin-bottom: 1rem; }
    .xl-margin-1-half-rem-bottom { margin-bottom: 1.5rem; }
    .xl-margin-2-rem-bottom { margin-bottom: 2rem; }
    .xl-margin-2-half-rem-bottom { margin-bottom: 2.5rem; }
    .xl-margin-3-rem-bottom { margin-bottom: 3rem; }
    .xl-margin-3-half-rem-bottom { margin-bottom: 3.5rem; }
    .xl-margin-4-rem-bottom { margin-bottom: 4rem; }
    .xl-margin-4-half-rem-bottom { margin-bottom: 4.5rem; }
    .xl-margin-5-rem-bottom { margin-bottom: 5rem; }
    .xl-margin-5-half-rem-bottom { margin-bottom: 5.5rem; }
    .xl-margin-6-rem-bottom { margin-bottom: 6rem; }
    .xl-margin-6-half-rem-bottom { margin-bottom: 6.5rem; }
    .xl-margin-7-rem-bottom { margin-bottom: 7rem; }
    .xl-margin-7-half-rem-bottom { margin-bottom: 7.5rem; }
    .xl-margin-8-rem-bottom { margin-bottom: 8rem; }
    .xl-margin-8-half-rem-bottom { margin-bottom: 8.5rem; }
    .xl-margin-9-rem-bottom { margin-bottom: 9rem; }
    .xl-margin-9-half-rem-bottom { margin-bottom: 9.5rem; }
    .xl-margin-10-rem-bottom { margin-bottom: 10rem; }
    .xl-margin-10-half-rem-bottom { margin-bottom: 10.5rem; }

    /* margin right */
    .xl-margin-one-right { margin-right:1%; }
    .xl-margin-two-right { margin-right:2%; }
    .xl-margin-three-right { margin-right:3%; }
    .xl-margin-four-right { margin-right:4%; }
    .xl-margin-five-right { margin-right:5%; }
    .xl-margin-six-right { margin-right:6%; }
    .xl-margin-seven-right { margin-right:7%; }
    .xl-margin-eight-right { margin-right:8%; }
    .xl-margin-nine-right { margin-right:9%; }
    .xl-margin-ten-right { margin-right:10%; }
    .xl-margin-eleven-right { margin-right:11%; }
    .xl-margin-twelve-right { margin-right:12%; }
    .xl-margin-thirteen-right { margin-right:13%; }
    .xl-margin-fourteen-right { margin-right:14%; }
    .xl-margin-fifteen-right { margin-right:15%; }
    .xl-margin-sixteen-right { margin-right:16%; }
    .xl-margin-seventeen-right { margin-right:17%; }
    .xl-margin-eighteen-right { margin-right:18%; }
    .xl-margin-nineteen-right { margin-right:19%; }
    .xl-margin-twenty-right { margin-right:20%; }
    .xl-margin-5px-right { margin-right:5px; }
    .xl-margin-10px-right { margin-right:10px; }
    .xl-margin-15px-right { margin-right:15px; }
    .xl-margin-20px-right { margin-right:20px; }
    .xl-margin-25px-right { margin-right:25px; }
    .xl-margin-30px-right { margin-right:30px; }
    .xl-margin-35px-right { margin-right:35px; }
    .xl-margin-40px-right { margin-right:40px; }
    .xl-margin-45px-right { margin-right:45px; }
    .xl-margin-50px-right { margin-right:50px; }
    .xl-margin-55px-right { margin-right:55px; }
    .xl-margin-60px-right { margin-right:60px; }
    .xl-margin-65px-right { margin-right:65px; }
    .xl-margin-70px-right { margin-right:70px; }
    .xl-margin-75px-right { margin-right:75px; }
    .xl-margin-80px-right { margin-right:80px; }
    .xl-margin-85px-right { margin-right:85px; }
    .xl-margin-90px-right { margin-right:90px; }
    .xl-margin-95px-right { margin-right:95px; }
    .xl-margin-100px-right { margin-right:100px; }
    .xl-margin-1-rem-right { margin-right: 1rem; }
    .xl-margin-1-half-rem-right { margin-right: 1.5rem; }
    .xl-margin-2-rem-right { margin-right: 2rem; }
    .xl-margin-2-half-rem-right { margin-right: 2.5rem; }
    .xl-margin-3-rem-right { margin-right: 3rem; }
    .xl-margin-3-half-rem-right { margin-right: 3.5rem; }
    .xl-margin-4-rem-right { margin-right: 4rem; }
    .xl-margin-4-half-rem-right { margin-right: 4.5rem; }
    .xl-margin-5-rem-right { margin-right: 5rem; }
    .xl-margin-5-half-rem-right { margin-right: 5.5rem; }
    .xl-margin-6-rem-right { margin-right: 6rem; }
    .xl-margin-6-half-rem-right { margin-right: 6.5rem; }
    .xl-margin-7-rem-right { margin-right: 7rem; }
    .xl-margin-7-half-rem-right { margin-right: 7.5rem; }
    .xl-margin-8-rem-right { margin-right: 8rem; }
    .xl-margin-8-half-rem-right { margin-right: 8.5rem; }
    .xl-margin-9-rem-right { margin-right: 9rem; }
    .xl-margin-9-half-rem-right { margin-right: 9.5rem; }
    .xl-margin-10-rem-right { margin-right: 10rem; }
    .xl-margin-10-half-rem-right { margin-right: 10.5rem; }

    /* margin left */
    .xl-margin-one-left { margin-left:1%; }
    .xl-margin-two-left { margin-left:2%; }
    .xl-margin-three-left { margin-left:3%; }
    .xl-margin-four-left { margin-left:4%; }
    .xl-margin-five-left { margin-left:5%; }
    .xl-margin-six-left { margin-left:6%; }
    .xl-margin-seven-left { margin-left:7%; }
    .xl-margin-eight-left { margin-left:8%; }
    .xl-margin-nine-left { margin-left:9%; }
    .xl-margin-ten-left { margin-left:10%; }
    .xl-margin-eleven-left { margin-left:11%; }
    .xl-margin-twelve-left { margin-left:12%; }
    .xl-margin-thirteen-left { margin-left:13%; }
    .xl-margin-fourteen-left { margin-left:14%; }
    .xl-margin-fifteen-left { margin-left:15%; }
    .xl-margin-sixteen-left { margin-left:16%; }
    .xl-margin-seventeen-left { margin-left:17%; }
    .xl-margin-eighteen-left { margin-left:18%; }
    .xl-margin-nineteen-left { margin-left:19%; }
    .xl-margin-twenty-left { margin-left:20%; }
    .xl-margin-5px-left { margin-left:5px; }
    .xl-margin-10px-left { margin-left:10px; }
    .xl-margin-15px-left { margin-left:15px; }
    .xl-margin-20px-left { margin-left:20px; }
    .xl-margin-25px-left { margin-left:25px; }
    .xl-margin-30px-left { margin-left:30px; }
    .xl-margin-35px-left { margin-left:35px; }
    .xl-margin-40px-left { margin-left:40px; }
    .xl-margin-45px-left { margin-left:45px; }
    .xl-margin-50px-left { margin-left:50px; }
    .xl-margin-55px-left { margin-left:55px; }
    .xl-margin-60px-left { margin-left:60px; }
    .xl-margin-65px-left { margin-left:65px; }
    .xl-margin-70px-left { margin-left:70px; }
    .xl-margin-75px-left { margin-left:75px; }
    .xl-margin-80px-left { margin-left:80px; }
    .xl-margin-85px-left { margin-left:85px; }
    .xl-margin-90px-left { margin-left:90px; }
    .xl-margin-95px-left { margin-left:95px; }
    .xl-margin-100px-left { margin-left:100px; }
    .xl-margin-1-rem-left { margin-left: 1rem; }
    .xl-margin-1-half-rem-left { margin-left: 1.5rem; }
    .xl-margin-2-rem-left { margin-left: 2rem; }
    .xl-margin-2-half-rem-left { margin-left: 2.5rem; }
    .xl-margin-3-rem-left { margin-left: 3rem; }
    .xl-margin-3-half-rem-left { margin-left: 3.5rem; }
    .xl-margin-4-rem-left { margin-left: 4rem; }
    .xl-margin-4-half-rem-left { margin-left: 4.5rem; }
    .xl-margin-5-rem-left { margin-left: 5rem; }
    .xl-margin-5-half-rem-left { margin-left: 5.5rem; }
    .xl-margin-6-rem-left { margin-left: 6rem; }
    .xl-margin-6-half-rem-left { margin-left: 6.5rem; }
    .xl-margin-7-rem-left { margin-left: 7rem; }
    .xl-margin-7-half-rem-left { margin-left: 7.5rem; }
    .xl-margin-8-rem-left { margin-left: 8rem; }
    .xl-margin-8-half-rem-left { margin-left: 8.5rem; }
    .xl-margin-9-rem-left { margin-left: 9rem; }
    .xl-margin-9-half-rem-left { margin-left: 9.5rem; }
    .xl-margin-10-rem-left { margin-left: 10rem; }
    .xl-margin-10-half-rem-left { margin-left: 10.5rem; }

    /* margin left right */
    .xl-margin-one-lr { margin-left:1%; margin-right:1%; }
    .xl-margin-two-lr { margin-left:2%; margin-right:2%; }
    .xl-margin-three-lr { margin-left:3%; margin-right:3%; }
    .xl-margin-four-lr { margin-left:4%; margin-right:4%; }
    .xl-margin-five-lr { margin-left:5%; margin-right:5%; }
    .xl-margin-six-lr { margin-left:6%; margin-right:6%; }
    .xl-margin-seven-lr { margin-left:7%; margin-right:7%; }
    .xl-margin-eight-lr { margin-left:8%; margin-right:8%; }
    .xl-margin-nine-lr { margin-left:9%; margin-right:9%; }
    .xl-margin-ten-lr { margin-left:10%; margin-right:10%; }
    .xl-margin-eleven-lr { margin-left:11%; margin-right:11%; }
    .xl-margin-twelve-lr { margin-left:12%; margin-right:12%; }
    .xl-margin-thirteen-lr { margin-left:13%; margin-right:13%; }
    .xl-margin-fourteen-lr { margin-left:14%; margin-right:14%; }
    .xl-margin-fifteen-lr { margin-left:15%; margin-right:15%; }
    .xl-margin-sixteen-lr { margin-left:16%; margin-right:16%; }
    .xl-margin-seventeen-lr { margin-left:17%; margin-right:17%; }
    .xl-margin-eighteen-lr { margin-left:18%; margin-right:18%; }
    .xl-margin-nineteen-lr { margin-left:19%; margin-right:19%; }
    .xl-margin-twenty-lr { margin-left:20%; margin-right:20%; }
    .xl-margin-5px-lr { margin-left:5px; margin-right:5px; }
    .xl-margin-10px-lr { margin-left:10px; margin-right:10px; }
    .xl-margin-15px-lr { margin-left:15px; margin-right:15px; }
    .xl-margin-20px-lr { margin-left:20px; margin-right:20px; }
    .xl-margin-25px-lr { margin-left:25px; margin-right:25px; }
    .xl-margin-30px-lr { margin-left:30px; margin-right:30px; }
    .xl-margin-35px-lr { margin-left:35px; margin-right:35px; }
    .xl-margin-40px-lr { margin-left:40px; margin-right:40px; }
    .xl-margin-45px-lr { margin-left:45px; margin-right:45px; }
    .xl-margin-50px-lr { margin-left:50px; margin-right:50px; }
    .xl-margin-55px-lr { margin-left:55px; margin-right:55px; }
    .xl-margin-60px-lr { margin-left:60px; margin-right:60px; }
    .xl-margin-65px-lr { margin-left:65px; margin-right:65px; }
    .xl-margin-70px-lr { margin-left:70px; margin-right:70px; }
    .xl-margin-75px-lr { margin-left:75px; margin-right:75px; }
    .xl-margin-80px-lr { margin-left:80px; margin-right:80px; }
    .xl-margin-85px-lr { margin-left:85px; margin-right:85px; }
    .xl-margin-90px-lr { margin-left:90px; margin-right:90px; }
    .xl-margin-95px-lr { margin-left:95px; margin-right:95px; }
    .xl-margin-100px-lr { margin-left:100px; margin-right:100px; }
    .xl-margin-1-rem-lr { margin-left: 1rem; margin-right: 1rem; }
    .xl-margin-1-half-rem-lr { margin-left: 1.5rem; margin-right: 1.5rem; }
    .xl-margin-2-rem-lr { margin-left: 2rem; margin-right: 2rem; }
    .xl-margin-2-half-rem-lr { margin-left: 2.5rem; margin-right: 2.5rem; }
    .xl-margin-3-rem-lr { margin-left: 3rem; margin-right: 3rem; }
    .xl-margin-3-half-rem-lr { margin-left: 3.5rem; margin-right: 3.5rem; }
    .xl-margin-4-rem-lr { margin-left: 4rem; margin-right: 4rem; }
    .xl-margin-4-half-rem-lr { margin-left: 4.5rem; margin-right: 4.5rem; }
    .xl-margin-5-rem-lr { margin-left: 5rem; margin-right: 5rem; }
    .xl-margin-5-half-rem-lr { margin-left: 5.5rem; margin-right: 5.5rem; }
    .xl-margin-6-rem-lr { margin-left: 6rem; margin-right: 6rem; }
    .xl-margin-6-half-rem-lr { margin-left: 6.5rem; margin-right: 6.5rem; }
    .xl-margin-7-rem-lr { margin-left: 7rem; margin-right: 7rem; }
    .xl-margin-7-half-rem-lr { margin-left: 7.5rem; margin-right: 7.5rem; }
    .xl-margin-8-rem-lr { margin-left: 8rem; margin-right: 8rem; }
    .xl-margin-8-half-rem-lr { margin-left: 8.5rem; margin-right: 8.5rem; }
    .xl-margin-9-rem-lr { margin-left: 9rem; margin-right: 9rem; }
    .xl-margin-9-half-rem-lr { margin-left: 9.5rem; margin-right: 9.5rem; }
    .xl-margin-10-rem-lr { margin-left: 10rem; margin-right: 10rem; }
    .xl-margin-10-half-rem-lr { margin-left: 10.5rem; margin-right: 10.5rem; }

    /* margin top bottom */
    .xl-margin-one-tb { margin-top:1%; margin-bottom:1%; }
    .xl-margin-two-tb { margin-top:2%; margin-bottom:2%; }
    .xl-margin-three-tb { margin-top:3%; margin-bottom:3%; }
    .xl-margin-four-tb { margin-top:4%; margin-bottom:4%; }
    .xl-margin-five-tb { margin-top:5%; margin-bottom:5%; }
    .xl-margin-six-tb { margin-top:6%; margin-bottom:6%; }
    .xl-margin-seven-tb { margin-top:7%; margin-bottom:7%; }
    .xl-margin-eight-tb { margin-top:8%; margin-bottom:8%; }
    .xl-margin-nine-tb { margin-top:9%; margin-bottom:9%; }
    .xl-margin-ten-tb { margin-top:10%; margin-bottom:10%; }
    .xl-margin-eleven-tb { margin-top:11%; margin-bottom:11%; }
    .xl-margin-twelve-tb { margin-top:12%; margin-bottom:12%; }
    .xl-margin-thirteen-tb { margin-top:13%; margin-bottom:13%; }
    .xl-margin-fourteen-tb { margin-top:14%; margin-bottom:14%; }
    .xl-margin-fifteen-tb { margin-top:15%; margin-bottom:15%; }
    .xl-margin-sixteen-tb { margin-top:16%; margin-bottom:16%; }
    .xl-margin-seventeen-tb { margin-top:17%; margin-bottom:17%; }
    .xl-margin-eighteen-tb { margin-top:18%; margin-bottom:18%; }
    .xl-margin-nineteen-tb { margin-top:19%; margin-bottom:19%; }
    .xl-margin-twenty-tb { margin-top:20%; margin-bottom:20%; }
    .xl-margin-5px-tb { margin-top:5px; margin-bottom:5px; }
    .xl-margin-10px-tb { margin-top:10px; margin-bottom:10px; }
    .xl-margin-15px-tb { margin-top:15px; margin-bottom:15px; }
    .xl-margin-20px-tb { margin-top:20px; margin-bottom:20px; }
    .xl-margin-25px-tb { margin-top:25px; margin-bottom:25px; }
    .xl-margin-30px-tb { margin-top:30px; margin-bottom:30px; }
    .xl-margin-35px-tb { margin-top:35px; margin-bottom:35px; }
    .xl-margin-40px-tb { margin-top:40px; margin-bottom:40px; }
    .xl-margin-45px-tb { margin-top:45px; margin-bottom:45px; }
    .xl-margin-50px-tb { margin-top:50px; margin-bottom:50px; }
    .xl-margin-55px-tb { margin-top:55px; margin-bottom:55px; }
    .xl-margin-60px-tb { margin-top:60px; margin-bottom:60px; }
    .xl-margin-65px-tb { margin-top:65px; margin-bottom:65px; }
    .xl-margin-70px-tb { margin-top:70px; margin-bottom:70px; }
    .xl-margin-75px-tb { margin-top:75px; margin-bottom:75px; }
    .xl-margin-80px-tb { margin-top:80px; margin-bottom:80px; }
    .xl-margin-85px-tb { margin-top:85px; margin-bottom:85px; }
    .xl-margin-90px-tb { margin-top:90px; margin-bottom:90px; }
    .xl-margin-95px-tb { margin-top:95px; margin-bottom:95px; }
    .xl-margin-100px-tb { margin-top:100px; margin-bottom:100px; }
    .xl-margin-1-rem-tb { margin-top: 1rem; margin-bottom: 1rem; }
    .xl-margin-1-half-rem-tb { margin-top: 1.5rem; margin-bottom: 1.5rem; }
    .xl-margin-2-rem-tb { margin-top: 2rem; margin-bottom: 2rem; }
    .xl-margin-2-half-rem-tb { margin-top: 2.5rem; margin-bottom: 2.5rem; }
    .xl-margin-3-rem-tb { margin-top: 3rem; margin-bottom: 3rem; }
    .xl-margin-3-half-rem-tb { margin-top: 3.5rem; margin-bottom: 3.5rem; }
    .xl-margin-4-rem-tb { margin-top: 4rem; margin-bottom: 4rem; }
    .xl-margin-4-half-rem-tb { margin-top: 4.5rem; margin-bottom: 4.5rem; }
    .xl-margin-5-rem-tb { margin-top: 5rem; margin-bottom: 5rem; }
    .xl-margin-5-half-rem-tb { margin-top: 5.5rem; margin-bottom: 5.5rem; }
    .xl-margin-6-rem-tb { margin-top: 6rem; margin-bottom: 6rem; }
    .xl-margin-6-half-rem-tb { margin-top: 6.5rem; margin-bottom: 6.5rem; }
    .xl-margin-7-rem-tb { margin-top: 7rem; margin-bottom: 7rem; }
    .xl-margin-7-half-rem-tb { margin-top: 7.5rem; margin-bottom: 7.5rem; }
    .xl-margin-8-rem-tb { margin-top: 8rem; margin-bottom: 8rem; }
    .xl-margin-8-half-rem-tb { margin-top: 8.5rem; margin-bottom: 8.5rem; }
    .xl-margin-9-rem-tb { margin-top: 9rem; margin-bottom: 9rem; }
    .xl-margin-9-half-rem-tb { margin-top: 9.5rem; margin-bottom: 9.5rem; }
    .xl-margin-10-rem-tb { margin-top: 10rem; margin-bottom: 10rem; }
    .xl-margin-10-half-rem-tb { margin-top: 10.5rem; margin-bottom: 10.5rem; }  

    .xl-margin-auto-lr { margin-left: auto !important; margin-right: auto !important; }
    .xl-margin-auto { margin: auto; }
    .xl-no-margin { margin: 0 !important; }
    .xl-no-margin-top { margin-top: 0 !important; }
    .xl-no-margin-bottom { margin-bottom: 0 !important; }
    .xl-no-margin-left { margin-left: 0 !important; }
    .xl-no-margin-right { margin-right: 0 !important; }
    .xl-no-margin-tb { margin-top: 0 !important; margin-bottom: 0 !important; }
    .xl-no-margin-lr { margin-right: 0 !important; margin-left: 0 !important; }

    /* padding */
    .xl-padding-one-all { padding:1%; }
    .xl-padding-two-all { padding:2%; }
    .xl-padding-three-all { padding:3%; }
    .xl-padding-four-all { padding:4%; }
    .xl-padding-five-all { padding:5%; }
    .xl-padding-six-all { padding:6%; }
    .xl-padding-seven-all { padding:7%; }
    .xl-padding-eight-all { padding:8%; }
    .xl-padding-nine-all { padding:9%; }
    .xl-padding-ten-all { padding:10%; }
    .xl-padding-eleven-all { padding:11%; }
    .xl-padding-twelve-all { padding:12%; }
    .xl-padding-thirteen-all { padding:13%; }
    .xl-padding-fourteen-all { padding:14%; }
    .xl-padding-fifteen-all { padding:15%; }
    .xl-padding-sixteen-all { padding:16%; }
    .xl-padding-seventeen-all { padding:17%; }
    .xl-padding-eighteen-all { padding:18%; }
    .xl-padding-nineteen-all { padding:19%; }
    .xl-padding-twenty-all { padding:20%; }
    .xl-padding-5px-all { padding:5px; }
    .xl-padding-10px-all { padding:10px; }
    .xl-padding-15px-all { padding:15px; }
    .xl-padding-20px-all { padding:20px; }
    .xl-padding-25px-all { padding:25px; }
    .xl-padding-30px-all { padding:30px; }
    .xl-padding-35px-all { padding:35px; }
    .xl-padding-40px-all { padding:40px; }
    .xl-padding-45px-all { padding:45px; }
    .xl-padding-50px-all { padding:50px; }
    .xl-padding-55px-all { padding:55px; }
    .xl-padding-60px-all { padding:60px; }
    .xl-padding-65px-all { padding:65px; }
    .xl-padding-70px-all { padding:70px; }
    .xl-padding-75px-all { padding:75px; }
    .xl-padding-80px-all { padding:80px; }
    .xl-padding-85px-all { padding:85px; }
    .xl-padding-90px-all { padding:90px; }
    .xl-padding-95px-all { padding:95px; }
    .xl-padding-100px-all { padding:100px; }
    .xl-padding-1-rem-all { padding: 1rem; }
    .xl-padding-1-half-rem-all { padding: 1.5rem; }
    .xl-padding-2-rem-all { padding: 2rem; }
    .xl-padding-2-half-rem-all { padding: 2.5rem; }
    .xl-padding-3-rem-all { padding: 3rem; }
    .xl-padding-3-half-rem-all { padding: 3.5rem; }
    .xl-padding-4-rem-all { padding: 4rem; }
    .xl-padding-4-half-rem-all { padding: 4.5rem; }
    .xl-padding-5-rem-all { padding: 5rem; }
    .xl-padding-5-half-rem-all { padding: 5.5rem; }
    .xl-padding-6-rem-all { padding: 6rem; }
    .xl-padding-6-half-rem-all { padding: 6.5rem; }
    .xl-padding-7-rem-all { padding: 7rem; }
    .xl-padding-7-half-rem-all { padding: 7.5rem; }
    .xl-padding-8-rem-all { padding: 8rem; }
    .xl-padding-8-half-rem-all { padding: 8.5rem; }
    .xl-padding-9-rem-all { padding: 9rem; }
    .xl-padding-9-half-rem-all { padding: 9.5rem; }
    .xl-padding-10-rem-all { padding: 10rem; }
    .xl-padding-10-half-rem-all { padding: 10.5rem; }

    /* padding top */
    .xl-padding-one-top { padding-top:1%; }
    .xl-padding-two-top { padding-top:2%; }
    .xl-padding-three-top { padding-top:3%; }
    .xl-padding-four-top { padding-top:4%; }
    .xl-padding-five-top { padding-top:5%; }
    .xl-padding-six-top { padding-top:6%; }
    .xl-padding-seven-top { padding-top:7%; }
    .xl-padding-eight-top { padding-top:8%; }
    .xl-padding-nine-top { padding-top:9%; }
    .xl-padding-ten-top { padding-top:10%; }
    .xl-padding-eleven-top { padding-top:11%; }
    .xl-padding-twelve-top { padding-top:12%; }
    .xl-padding-thirteen-top { padding-top:13%; }
    .xl-padding-fourteen-top { padding-top:14%; }
    .xl-padding-fifteen-top { padding-top:15%; }
    .xl-padding-sixteen-top { padding-top:16%; }
    .xl-padding-seventeen-top { padding-top:17%; }
    .xl-padding-eighteen-top { padding-top:18%; }
    .xl-padding-nineteen-top { padding-top:19%; }
    .xl-padding-twenty-top { padding-top:20%; }
    .xl-padding-5px-top { padding-top:5px; }
    .xl-padding-10px-top { padding-top:10px; }
    .xl-padding-15px-top { padding-top:15px; }
    .xl-padding-20px-top { padding-top:20px; }
    .xl-padding-25px-top { padding-top:25px; }
    .xl-padding-30px-top { padding-top:30px; }
    .xl-padding-35px-top { padding-top:35px; }
    .xl-padding-40px-top { padding-top:40px; }
    .xl-padding-45px-top { padding-top:45px; }
    .xl-padding-50px-top { padding-top:50px; }
    .xl-padding-55px-top { padding-top:55px; }
    .xl-padding-60px-top { padding-top:60px; }
    .xl-padding-65px-top { padding-top:65px; }
    .xl-padding-70px-top { padding-top:70px; }
    .xl-padding-75px-top { padding-top:75px; }
    .xl-padding-80px-top { padding-top:80px; }
    .xl-padding-85px-top { padding-top:85px; }
    .xl-padding-90px-top { padding-top:90px; }
    .xl-padding-95px-top { padding-top:95px; }
    .xl-padding-100px-top { padding-top:100px; }
    .xl-padding-1-rem-top { padding-top: 1rem; }
    .xl-padding-1-half-rem-top { padding-top: 1.5rem; }
    .xl-padding-2-rem-top { padding-top: 2rem; }
    .xl-padding-2-half-rem-top { padding-top: 2.5rem; }
    .xl-padding-3-rem-top { padding-top: 3rem; }
    .xl-padding-3-half-rem-top { padding-top: 3.5rem; }
    .xl-padding-4-rem-top { padding-top: 4rem; }
    .xl-padding-4-half-rem-top { padding-top: 4.5rem; }
    .xl-padding-5-rem-top { padding-top: 5rem; }
    .xl-padding-5-half-rem-top { padding-top: 5.5rem; }
    .xl-padding-6-rem-top { padding-top: 6rem; }
    .xl-padding-6-half-rem-top { padding-top: 6.5rem; }
    .xl-padding-7-rem-top { padding-top: 7rem; }
    .xl-padding-7-half-rem-top { padding-top: 7.5rem; }
    .xl-padding-8-rem-top { padding-top: 8rem; }
    .xl-padding-8-half-rem-top { padding-top: 8.5rem; }
    .xl-padding-9-rem-top { padding-top: 9rem; }
    .xl-padding-9-half-rem-top { padding-top: 9.5rem; }
    .xl-padding-10-rem-top { padding-top: 10rem; }
    .xl-padding-10-half-rem-top { padding-top: 10.5rem; }

    /* padding bottom */
    .xl-padding-one-bottom { padding-bottom:1%; }
    .xl-padding-two-bottom { padding-bottom:2%; }
    .xl-padding-three-bottom { padding-bottom:3%; }
    .xl-padding-four-bottom { padding-bottom:4%; }
    .xl-padding-five-bottom { padding-bottom:5%; }
    .xl-padding-six-bottom { padding-bottom:6%; }
    .xl-padding-seven-bottom { padding-bottom:7%; }
    .xl-padding-eight-bottom { padding-bottom:8%; }
    .xl-padding-nine-bottom { padding-bottom:9%; }
    .xl-padding-ten-bottom { padding-bottom:10%; }
    .xl-padding-eleven-bottom { padding-bottom:11%; }
    .xl-padding-twelve-bottom { padding-bottom:12%; }
    .xl-padding-thirteen-bottom { padding-bottom:13%; }
    .xl-padding-fourteen-bottom { padding-bottom:14%; }
    .xl-padding-fifteen-bottom { padding-bottom:15%; }
    .xl-padding-sixteen-bottom { padding-bottom:16%; }
    .xl-padding-seventeen-bottom { padding-bottom:17%; }
    .xl-padding-eighteen-bottom { padding-bottom:18%; }
    .xl-padding-nineteen-bottom { padding-bottom:19%; }
    .xl-padding-twenty-bottom { padding-bottom:20%; }
    .xl-padding-5px-bottom { padding-bottom:5px; }
    .xl-padding-10px-bottom { padding-bottom:10px; }
    .xl-padding-15px-bottom { padding-bottom:15px; }
    .xl-padding-20px-bottom { padding-bottom:20px; }
    .xl-padding-25px-bottom { padding-bottom:25px; }
    .xl-padding-30px-bottom { padding-bottom:30px; }
    .xl-padding-35px-bottom { padding-bottom:35px; }
    .xl-padding-40px-bottom { padding-bottom:40px; }
    .xl-padding-45px-bottom { padding-bottom:45px; }
    .xl-padding-50px-bottom { padding-bottom:50px; }
    .xl-padding-55px-bottom { padding-bottom:55px; }
    .xl-padding-60px-bottom { padding-bottom:60px; }
    .xl-padding-65px-bottom { padding-bottom:65px; }
    .xl-padding-70px-bottom { padding-bottom:70px; }
    .xl-padding-75px-bottom { padding-bottom:75px; }
    .xl-padding-80px-bottom { padding-bottom:80px; }
    .xl-padding-85px-bottom { padding-bottom:85px; }
    .xl-padding-90px-bottom { padding-bottom:90px; }
    .xl-padding-95px-bottom { padding-bottom:95px; }
    .xl-padding-100px-bottom { padding-bottom:100px; }
    .xl-padding-1-rem-bottom { padding-bottom: 1rem; }
    .xl-padding-1-half-rem-bottom { padding-bottom: 1.5rem; }
    .xl-padding-2-rem-bottom { padding-bottom: 2rem; }
    .xl-padding-2-half-rem-bottom { padding-bottom: 2.5rem; }
    .xl-padding-3-rem-bottom { padding-bottom: 3rem; }
    .xl-padding-3-half-rem-bottom { padding-bottom: 3.5rem; }
    .xl-padding-4-rem-bottom { padding-bottom: 4rem; }
    .xl-padding-4-half-rem-bottom { padding-bottom: 4.5rem; }
    .xl-padding-5-rem-bottom { padding-bottom: 5rem; }
    .xl-padding-5-half-rem-bottom { padding-bottom: 5.5rem; }
    .xl-padding-6-rem-bottom { padding-bottom: 6rem; }
    .xl-padding-6-half-rem-bottom { padding-bottom: 6.5rem; }
    .xl-padding-7-rem-bottom { padding-bottom: 7rem; }
    .xl-padding-7-half-rem-bottom { padding-bottom: 7.5rem; }
    .xl-padding-8-rem-bottom { padding-bottom: 8rem; }
    .xl-padding-8-half-rem-bottom { padding-bottom: 8.5rem; }
    .xl-padding-9-rem-bottom { padding-bottom: 9rem; }
    .xl-padding-9-half-rem-bottom { padding-bottom: 9.5rem; }
    .xl-padding-10-rem-bottom { padding-bottom: 10rem; }
    .xl-padding-10-half-rem-bottom { padding-bottom: 10.5rem; }

    /* padding right */
    .xl-padding-one-right { padding-right:1%; }
    .xl-padding-two-right { padding-right:2%; }
    .xl-padding-three-right { padding-right:3%; }
    .xl-padding-four-right { padding-right:4% }
    .xl-padding-five-right { padding-right:5%; }
    .xl-padding-six-right { padding-right:6%; }
    .xl-padding-seven-right { padding-right:7%; }
    .xl-padding-eight-right { padding-right:8%; }
    .xl-padding-nine-right { padding-right:9%; }
    .xl-padding-ten-right { padding-right:10%; }
    .xl-padding-eleven-right { padding-right:11%; }
    .xl-padding-twelve-right { padding-right:12%; }
    .xl-padding-thirteen-right { padding-right:13%; }
    .xl-padding-fourteen-right { padding-right:14%; }
    .xl-padding-fifteen-right { padding-right:15%; }
    .xl-padding-sixteen-right { padding-right:16%; }
    .xl-padding-seventeen-right { padding-right:17%; }
    .xl-padding-eighteen-right { padding-right:18%; }
    .xl-padding-nineteen-right { padding-right:19%; }
    .xl-padding-twenty-right { padding-right:20%; }
    .xl-padding-5px-right { padding-right:5px; }
    .xl-padding-10px-right { padding-right:10px; }
    .xl-padding-15px-right { padding-right:15px; }
    .xl-padding-20px-right { padding-right:20px; }
    .xl-padding-25px-right { padding-right:25px; }
    .xl-padding-30px-right { padding-right:30px; }
    .xl-padding-35px-right { padding-right:35px; }
    .xl-padding-40px-right { padding-right:40px; }
    .xl-padding-45px-right { padding-right:45px; }
    .xl-padding-50px-right { padding-right:50px; }
    .xl-padding-55px-right { padding-right:55px; }
    .xl-padding-60px-right { padding-right:60px; }
    .xl-padding-65px-right { padding-right:65px; }
    .xl-padding-70px-right { padding-right:70px; }
    .xl-padding-75px-right { padding-right:75px; }
    .xl-padding-80px-right { padding-right:80px; }
    .xl-padding-85px-right { padding-right:85px; }
    .xl-padding-90px-right { padding-right:90px; }
    .xl-padding-95px-right { padding-right:95px; }
    .xl-padding-100px-right { padding-right:100px; }
    .xl-padding-1-rem-right { padding-right: 1rem; }
    .xl-padding-1-half-rem-right { padding-right: 1.5rem; }
    .xl-padding-2-rem-right { padding-right: 2rem; }
    .xl-padding-2-half-rem-right { padding-right: 2.5rem; }
    .xl-padding-3-rem-right { padding-right: 3rem; }
    .xl-padding-3-half-rem-right { padding-right: 3.5rem; }
    .xl-padding-4-rem-right { padding-right: 4rem; }
    .xl-padding-4-half-rem-right { padding-right: 4.5rem; }
    .xl-padding-5-rem-right { padding-right: 5rem; }
    .xl-padding-5-half-rem-right { padding-right: 5.5rem; }
    .xl-padding-6-rem-right { padding-right: 6rem; }
    .xl-padding-6-half-rem-right { padding-right: 6.5rem; }
    .xl-padding-7-rem-right { padding-right: 7rem; }
    .xl-padding-7-half-rem-right { padding-right: 7.5rem; }
    .xl-padding-8-rem-right { padding-right: 8rem; }
    .xl-padding-8-half-rem-right { padding-right: 8.5rem; }
    .xl-padding-9-rem-right { padding-right: 9rem; }
    .xl-padding-9-half-rem-right { padding-right: 9.5rem; }
    .xl-padding-10-rem-right { padding-right: 10rem; }
    .xl-padding-10-half-rem-right { padding-right: 10.5rem; }

    /* padding left */
    .xl-padding-one-left { padding-left:1%; }
    .xl-padding-two-left { padding-left:2%; }
    .xl-padding-three-left { padding-left:3%; }
    .xl-padding-four-left { padding-left:4%; }
    .xl-padding-five-left { padding-left:5%; }
    .xl-padding-six-left { padding-left:6%; }
    .xl-padding-seven-left { padding-left:7%; }
    .xl-padding-eight-left { padding-left:8%; }
    .xl-padding-nine-left { padding-left:9%; }
    .xl-padding-ten-left { padding-left:10%; }
    .xl-padding-eleven-left { padding-left:11%; }
    .xl-padding-twelve-left { padding-left:12%; }
    .xl-padding-thirteen-left { padding-left:13%; }
    .xl-padding-fourteen-left { padding-left:14%; }
    .xl-padding-fifteen-left { padding-left:15%; }
    .xl-padding-sixteen-left { padding-left:16%; }
    .xl-padding-seventeen-left { padding-left:17%; }
    .xl-padding-eighteen-left { padding-left:18%; }
    .xl-padding-nineteen-left { padding-left:19%; }
    .xl-padding-twenty-left { padding-left:20%; }
    .xl-padding-5px-left { padding-left:5px; }
    .xl-padding-10px-left { padding-left:10px; }
    .xl-padding-15px-left { padding-left:15px; }
    .xl-padding-20px-left { padding-left:20px; }
    .xl-padding-25px-left { padding-left:25px; }
    .xl-padding-30px-left { padding-left:30px; }
    .xl-padding-35px-left { padding-left:35px; }
    .xl-padding-40px-left { padding-left:40px; }
    .xl-padding-45px-left { padding-left:45px; }
    .xl-padding-50px-left { padding-left:50px; }
    .xl-padding-55px-left { padding-left:55px; }
    .xl-padding-60px-left { padding-left:60px; }
    .xl-padding-65px-left { padding-left:65px; }
    .xl-padding-70px-left { padding-left:70px; }
    .xl-padding-75px-left { padding-left:75px; }
    .xl-padding-80px-left { padding-left:80px; }
    .xl-padding-85px-left { padding-left:85px; }
    .xl-padding-90px-left { padding-left:90px; }
    .xl-padding-95px-left { padding-left:95px; }
    .xl-padding-100px-left { padding-left:100px; }
    .xl-padding-1-rem-left { padding-left: 1rem; }
    .xl-padding-1-half-rem-left { padding-left: 1.5rem; }
    .xl-padding-2-rem-left { padding-left: 2rem; }
    .xl-padding-2-half-rem-left { padding-left: 2.5rem; }
    .xl-padding-3-rem-left { padding-left: 3rem; }
    .xl-padding-3-half-rem-left { padding-left: 3.5rem; }
    .xl-padding-4-rem-left { padding-left: 4rem; }
    .xl-padding-4-half-rem-left { padding-left: 4.5rem; }
    .xl-padding-5-rem-left { padding-left: 5rem; }
    .xl-padding-5-half-rem-left { padding-left: 5.5rem; }
    .xl-padding-6-rem-left { padding-left: 6rem; }
    .xl-padding-6-half-rem-left { padding-left: 6.5rem; }
    .xl-padding-7-rem-left { padding-left: 7rem; }
    .xl-padding-7-half-rem-left { padding-left: 7.5rem; }
    .xl-padding-8-rem-left { padding-left: 8rem; }
    .xl-padding-8-half-rem-left { padding-left: 8.5rem; }
    .xl-padding-9-rem-left { padding-left: 9rem; }
    .xl-padding-9-half-rem-left { padding-left: 9.5rem; }
    .xl-padding-10-rem-left { padding-left: 10rem; }
    .xl-padding-10-half-rem-left { padding-left: 10.5rem; }

    /* padding top bottom */
    .xl-padding-one-tb { padding-top:1%; padding-bottom:1%; }
    .xl-padding-two-tb { padding-top:2%; padding-bottom:2%; }
    .xl-padding-three-tb { padding-top:3%; padding-bottom:3%; }
    .xl-padding-four-tb { padding-top:4%; padding-bottom:4%; }
    .xl-padding-five-tb { padding-top:5%; padding-bottom:5%; }
    .xl-padding-six-tb { padding-top:6%; padding-bottom:6%; }
    .xl-padding-seven-tb { padding-top:7%; padding-bottom:7%; }
    .xl-padding-eight-tb { padding-top:8%; padding-bottom:8%; }
    .xl-padding-nine-tb { padding-top:9%; padding-bottom:9%; }
    .xl-padding-ten-tb { padding-top:10%; padding-bottom:10%; }
    .xl-padding-eleven-tb { padding-top:11%; padding-bottom:11%; }
    .xl-padding-twelve-tb { padding-top:12%; padding-bottom:12%; }
    .xl-padding-thirteen-tb { padding-top:13%; padding-bottom:13%; }
    .xl-padding-fourteen-tb { padding-top:14%; padding-bottom:14%; }
    .xl-padding-fifteen-tb { padding-top:15%; padding-bottom:15%; }
    .xl-padding-sixteen-tb { padding-top:16%; padding-bottom:16%; }
    .xl-padding-seventeen-tb { padding-top:17%; padding-bottom:17%; }
    .xl-padding-eighteen-tb { padding-top:18%; padding-bottom:18%; }
    .xl-padding-nineteen-tb { padding-top:19%; padding-bottom:19%; }
    .xl-padding-twenty-tb { padding-top:20%; padding-bottom:20%; }
    .xl-padding-5px-tb { padding-top:5px; padding-bottom:5px; }
    .xl-padding-10px-tb { padding-top:10px; padding-bottom:10px; }
    .xl-padding-15px-tb { padding-top:15px; padding-bottom:15px; }
    .xl-padding-20px-tb { padding-top:20px; padding-bottom:20px; }
    .xl-padding-25px-tb { padding-top:25px; padding-bottom:25px; }
    .xl-padding-30px-tb { padding-top:30px; padding-bottom:30px; }
    .xl-padding-35px-tb { padding-top:35px; padding-bottom:35px; }
    .xl-padding-40px-tb { padding-top:40px; padding-bottom:40px; }
    .xl-padding-45px-tb { padding-top:45px; padding-bottom:45px; }
    .xl-padding-50px-tb { padding-top:50px; padding-bottom:50px; }
    .xl-padding-55px-tb { padding-top:55px; padding-bottom:55px; }
    .xl-padding-60px-tb { padding-top:60px; padding-bottom:60px; }
    .xl-padding-65px-tb { padding-top:65px; padding-bottom:65px; }
    .xl-padding-70px-tb { padding-top:70px; padding-bottom:70px; }
    .xl-padding-75px-tb { padding-top:75px; padding-bottom:75px; }
    .xl-padding-80px-tb { padding-top:80px; padding-bottom:80px; }
    .xl-padding-85px-tb { padding-top:85px; padding-bottom:85px; }
    .xl-padding-90px-tb { padding-top:90px; padding-bottom:90px; }
    .xl-padding-95px-tb { padding-top:95px; padding-bottom:95px; }
    .xl-padding-100px-tb { padding-top:100px; padding-bottom:100px; }
    .xl-padding-1-rem-tb { padding-top: 1rem; padding-bottom: 1rem; }
    .xl-padding-1-half-rem-tb { padding-top: 1.5rem; padding-bottom: 1.5rem; }
    .xl-padding-2-rem-tb { padding-top: 2rem; padding-bottom: 2rem; }
    .xl-padding-2-half-rem-tb { padding-top: 2.5rem; padding-bottom: 2.5rem; }
    .xl-padding-3-rem-tb { padding-top: 3rem; padding-bottom: 3rem; }
    .xl-padding-3-half-rem-tb { padding-top: 3.5rem; padding-bottom: 3.5rem; }
    .xl-padding-4-rem-tb { padding-top: 4rem; padding-bottom: 4rem; }
    .xl-padding-4-half-rem-tb { padding-top: 4.5rem; padding-bottom: 4.5rem; }
    .xl-padding-5-rem-tb { padding-top: 5rem; padding-bottom: 5rem; }
    .xl-padding-5-half-rem-tb { padding-top: 5.5rem; padding-bottom: 5.5rem; }
    .xl-padding-6-rem-tb { padding-top: 6rem; padding-bottom: 6rem; }
    .xl-padding-6-half-rem-tb { padding-top: 6.5rem; padding-bottom: 6.5rem; }
    .xl-padding-7-rem-tb { padding-top: 7rem; padding-bottom: 7rem; }
    .xl-padding-7-half-rem-tb { padding-top: 7.5rem; padding-bottom: 7.5rem; }
    .xl-padding-8-rem-tb { padding-top: 8rem; padding-bottom: 8rem; }
    .xl-padding-8-half-rem-tb { padding-top: 8.5rem; padding-bottom: 8.5rem; }
    .xl-padding-9-rem-tb { padding-top: 9rem; padding-bottom: 9rem; }
    .xl-padding-9-half-rem-tb { padding-top: 9.5rem; padding-bottom: 9.5rem; }
    .xl-padding-10-rem-tb { padding-top: 10rem; padding-bottom: 10rem; }
    .xl-padding-10-half-rem-tb { padding-top: 10.5rem; padding-bottom: 10.5rem; }

    /* padding left right */
    .xl-padding-one-lr { padding-left:1%; padding-right:1%; }
    .xl-padding-two-lr { padding-left:2%; padding-right:2%; }
    .xl-padding-three-lr { padding-left:3%; padding-right:3%; }
    .xl-padding-four-lr { padding-left:4%; padding-right:4%; }
    .xl-padding-five-lr { padding-left:5%; padding-right:5%; }
    .xl-padding-six-lr { padding-left:6%; padding-right:6%; }
    .xl-padding-seven-lr { padding-left:7%; padding-right:7%; }
    .xl-padding-eight-lr { padding-left:8%; padding-right:8%; }
    .xl-padding-nine-lr { padding-left:9%; padding-right:9%; }
    .xl-padding-ten-lr { padding-left:10%; padding-right:10%; }
    .xl-padding-eleven-lr { padding-left:11%; padding-right:11%; }
    .xl-padding-twelve-lr { padding-left:12%; padding-right:12%; }
    .xl-padding-thirteen-lr { padding-left:13%; padding-right:13%; }
    .xl-padding-fourteen-lr { padding-left:14%; padding-right:14%; }
    .xl-padding-fifteen-lr { padding-left:15%; padding-right:15%; }
    .xl-padding-sixteen-lr { padding-left:16%; padding-right:16%; }
    .xl-padding-seventeen-lr { padding-left:17%; padding-right:17%; }
    .xl-padding-eighteen-lr { padding-left:18%; padding-right:18%; }
    .xl-padding-nineteen-lr { padding-left:19%; padding-right:19%; }
    .xl-padding-twenty-lr { padding-left:20%; padding-right:20%; }
    .xl-padding-5px-lr { padding-left:5px; padding-right:5px; }
    .xl-padding-10px-lr { padding-left:10px; padding-right:10px; }
    .xl-padding-15px-lr { padding-left:15px; padding-right:15px; }
    .xl-padding-20px-lr { padding-left:20px; padding-right:20px; }
    .xl-padding-25px-lr { padding-left:25px; padding-right:25px; }
    .xl-padding-30px-lr { padding-left:30px; padding-right:30px; }
    .xl-padding-35px-lr { padding-left:35px; padding-right:35px; }
    .xl-padding-40px-lr { padding-left:40px; padding-right:40px; }
    .xl-padding-45px-lr { padding-left:45px; padding-right:45px; }
    .xl-padding-50px-lr { padding-left:50px; padding-right:50px; }
    .xl-padding-55px-lr { padding-left:55px; padding-right:55px; }
    .xl-padding-60px-lr { padding-left:60px; padding-right:60px; }
    .xl-padding-65px-lr { padding-left:65px; padding-right:65px; }
    .xl-padding-70px-lr { padding-left:70px; padding-right:70px; }
    .xl-padding-75px-lr { padding-left:75px; padding-right:75px; }
    .xl-padding-80px-lr { padding-left:80px; padding-right:80px; }
    .xl-padding-85px-lr { padding-left:85px; padding-right:85px; }
    .xl-padding-90px-lr { padding-left:90px; padding-right:90px; }
    .xl-padding-95px-lr { padding-left:95px; padding-right:95px; }
    .xl-padding-100px-lr { padding-left:100px; padding-right:100px; }
    .xl-padding-1-rem-lr { padding-left: 1rem; padding-right: 1rem; }
    .xl-padding-1-half-rem-lr { padding-left: 1.5rem; padding-right: 1.5rem; }
    .xl-padding-2-rem-lr { padding-left: 2rem; padding-right: 2rem; }
    .xl-padding-2-half-rem-lr { padding-left: 2.5rem; padding-right: 2.5rem; }
    .xl-padding-3-rem-lr { padding-left: 3rem; padding-right: 3rem; }
    .xl-padding-3-half-rem-lr { padding-left: 3.5rem; padding-right: 3.5rem; }
    .xl-padding-4-rem-lr { padding-left: 4rem; padding-right: 4rem; }
    .xl-padding-4-half-rem-lr { padding-left: 4.5rem; padding-right: 4.5rem; }
    .xl-padding-5-rem-lr { padding-left: 5rem; padding-right: 5rem; }
    .xl-padding-5-half-rem-lr { padding-left: 5.5rem; padding-right: 5.5rem; }
    .xl-padding-6-rem-lr { padding-left: 6rem; padding-right: 6rem; }
    .xl-padding-6-half-rem-lr { padding-left: 6.5rem; padding-right: 6.5rem; }
    .xl-padding-7-rem-lr { padding-left: 7rem; padding-right: 7rem; }
    .xl-padding-7-half-rem-lr { padding-left: 7.5rem; padding-right: 7.5rem; }
    .xl-padding-8-rem-lr { padding-left: 8rem; padding-right: 8rem; }
    .xl-padding-8-half-rem-lr { padding-left: 8.5rem; padding-right: 8.5rem; }
    .xl-padding-9-rem-lr { padding-left: 9rem; padding-right: 9rem; }
    .xl-padding-9-half-rem-lr { padding-left: 9.5rem; padding-right: 9.5rem; }
    .xl-padding-10-rem-lr { padding-left: 10rem; padding-right: 10rem; }
    .xl-padding-10-half-rem-lr { padding-left: 10.5rem; padding-right: 10.5rem; }    

    .xl-no-padding { padding:0 !important; }
    .xl-no-padding-lr { padding-left: 0 !important; padding-right: 0 !important; }
    .xl-no-padding-tb { padding-top: 0 !important; padding-bottom: 0 !important; }
    .xl-no-padding-top { padding-top:0 !important; }
    .xl-no-padding-bottom { padding-bottom:0 !important; }
    .xl-no-padding-left { padding-left:0 !important; }
    .xl-no-padding-right { padding-right:0 !important; }

    /* display and overflow */
    .xl-d-initial { display: initial !important; }
    .xl-overflow-hidden { overflow:hidden !important; }
    .xl-overflow-visible { overflow:visible !important; }
    .xl-overflow-auto { overflow:auto !important; }

    /* position */
    .xl-position-relative { position: relative !important; }
    .xl-position-absolute { position: absolute !important; }
    .xl-position-fixed { position: fixed !important; }
    .xl-position-inherit { position: inherit !important; }
    .xl-position-initial { position: initial !important; }

    /* top */
    .xl-top-0px { top: 0; }
    .xl-top-1px { top: 1px; }
    .xl-top-2px { top: 2px; }
    .xl-top-3px { top: 3px; }
    .xl-top-4px { top: 4px; }
    .xl-top-5px { top: 5px; }
    .xl-top-6px { top: 6px; }
    .xl-top-7px { top: 7px; }
    .xl-top-8px { top: 8px; }
    .xl-top-9px { top: 9px; }
    .xl-top-10px { top: 10px; }
    .xl-top-15px { top: 15px; }
    .xl-top-20px { top: 20px; }
    .xl-top-25px { top: 25px; }
    .xl-top-30px { top: 30px; }
    .xl-top-35px { top: 35px; }
    .xl-top-40px { top: 40px; }
    .xl-top-45px { top: 45px; }
    .xl-top-50px { top: 50px; }
    .xl-top-auto { top:auto; }
    .xl-top-inherit { top:inherit; }

    /* top minus */
    .xl-top-minus-1px { top: -1px; }
    .xl-top-minus-2px { top: -2px; }
    .xl-top-minus-3px { top: -3px; }
    .xl-top-minus-4px { top: -4px; }
    .xl-top-minus-5px { top: -5px; }
    .xl-top-minus-6px { top: -6px; }
    .xl-top-minus-7px { top: -7px; }
    .xl-top-minus-8px { top: -8px; }
    .xl-top-minus-9px { top: -9px; }
    .xl-top-minus-10px { top: -10px; }
    .xl-top-minus-15px { top: -15px; }
    .xl-top-minus-20px { top: -20px; }
    .xl-top-minus-25px { top: -25px; }
    .xl-top-minus-30px { top: -30px; }
    .xl-top-minus-35px { top: -35px; }
    .xl-top-minus-40px { top: -40px; }
    .xl-top-minus-45px { top: -45px; }
    .xl-top-minus-50px { top: -50px; }

    /* bottom */
    .xl-bottom-0px { bottom:0; }
    .xl-bottom-1px { bottom:1px; }
    .xl-bottom-2px { bottom:2px; }
    .xl-bottom-3px { bottom:3px; }
    .xl-bottom-4px { bottom:4px; }
    .xl-bottom-5px { bottom:5px; }
    .xl-bottom-6px { bottom:6px; }
    .xl-bottom-7px { bottom:7px; }
    .xl-bottom-8px { bottom:8px; }
    .xl-bottom-9px { bottom:9px; }
    .xl-bottom-10px { bottom:10px; }
    .xl-bottom-15px { bottom:15px; }
    .xl-bottom-20px { bottom:20px; }
    .xl-bottom-25px { bottom:25px; }
    .xl-bottom-30px { bottom:30px; }
    .xl-bottom-35px { bottom:35px; }
    .xl-bottom-40px { bottom:40px; }
    .xl-bottom-45px { bottom:45px; }
    .xl-bottom-50px { bottom:50px; }
    .xl-bottom-55px { bottom:55px; }
    .xl-bottom-60px { bottom:60px; }
    .xl-bottom-auto { bottom: auto; }
    .xl-bottom-inherit { bottom: inherit; }

    /* bottom minus */
    .xl-bottom-minus-1px { bottom: -1px; }
    .xl-bottom-minus-2px { bottom: -2px; }
    .xl-bottom-minus-3px { bottom: -3px; }
    .xl-bottom-minus-4px { bottom: -4px; }
    .xl-bottom-minus-5px { bottom: -5px; }
    .xl-bottom-minus-6px { bottom: -6px; }
    .xl-bottom-minus-7px { bottom: -7px; }
    .xl-bottom-minus-8px { bottom: -8px; }
    .xl-bottom-minus-9px { bottom: -9px; }
    .xl-bottom-minus-10px { bottom: -10px; }
    .xl-bottom-minus-15px { bottom: -15px; }
    .xl-bottom-minus-20px { bottom: -20px; }
    .xl-bottom-minus-25px { bottom: -25px; }
    .xl-bottom-minus-30px { bottom: -30px; }
    .xl-bottom-minus-35px { bottom: -35px; }
    .xl-bottom-minus-40px { bottom: -40px; }
    .xl-bottom-minus-45px { bottom: -45px; }
    .xl-bottom-minus-50px { bottom: -50px; }

    /* right */
    .xl-right-0px { right: 0; }
    .xl-right-1px { right: 1px; }
    .xl-right-2px { right: 2px; }
    .xl-right-3px { right: 3px; }
    .xl-right-4px { right: 4px; }
    .xl-right-5px { right: 5px; }
    .xl-right-6px { right: 6px; }
    .xl-right-7px { right: 7px; }
    .xl-right-8px { right: 8px; }
    .xl-right-9px { right: 9px; }
    .xl-right-10px { right: 10px; }
    .xl-right-15px { right: 15px; }
    .xl-right-20px { right: 20px; }
    .xl-right-25px { right: 25px; }
    .xl-right-30px { right: 30px; }
    .xl-right-35px { right: 35px; }
    .xl-right-40px { right: 40px; }
    .xl-right-45px { right: 45px; }
    .xl-right-50px { right: 50px; }
    .xl-right-auto { right: auto; }
    .xl-right-inherit { right: inherit; }

    /* right minus */
    .xl-right-minus-1px { right: -1px; }
    .xl-right-minus-2px { right: -2px; }
    .xl-right-minus-3px { right: -3px; }
    .xl-right-minus-4px { right: -4px; }
    .xl-right-minus-5px { right: -5px; }
    .xl-right-minus-6px { right: -6px; }
    .xl-right-minus-7px { right: -7px; }
    .xl-right-minus-8px { right: -8px; }
    .xl-right-minus-9px { right: -9px; }
    .xl-right-minus-10px { right: -10px; }
    .xl-right-minus-15px { right: -15px; }
    .xl-right-minus-20px { right: -20px; }
    .xl-right-minus-25px { right: -25px; }
    .xl-right-minus-30px { right: -30px; }
    .xl-right-minus-35px { right: -35px; }
    .xl-right-minus-40px { right: -40px; }
    .xl-right-minus-45px { right: -45px; }
    .xl-right-minus-50px { right: -50px; }

    /* left */
    .xl-left-0px { left: 0; }
    .xl-left-1px { left: 1px; }
    .xl-left-2px { left: 2px; }
    .xl-left-3px { left: 3px; }
    .xl-left-4px { left: 4px; }
    .xl-left-5px { left: 5px; }
    .xl-left-6px { left: 6px; }
    .xl-left-7px { left: 7px; }
    .xl-left-8px { left: 8px; }
    .xl-left-9px { left: 9px; }
    .xl-left-10px { left: 10px; }
    .xl-left-15px { left: 15px; }
    .xl-left-20px { left: 20px; }
    .xl-left-25px { left: 25px; }
    .xl-left-30px { left: 30px; }
    .xl-left-35px { left: 35px; }
    .xl-left-40px { left: 40px; }
    .xl-left-45px { left: 45px; }
    .xl-left-50px { left: 50px; }
    .xl-left-55px { left: 55px; }
    .xl-left-60px { left: 60px; }
    .xl-left-auto { left: auto; }
    .xl-left-inherit { left: inherit; }

    /* left minus */
    .xl-left-0px { left: 0; }
    .xl-left-minus-1px { left: -1px; }
    .xl-left-minus-2px { left: -2px; }
    .xl-left-minus-3px { left: -3px; }
    .xl-left-minus-4px { left: -4px; }
    .xl-left-minus-5px { left: -5px; }
    .xl-left-minus-6px { left: -6px; }
    .xl-left-minus-7px { left: -7px; }
    .xl-left-minus-8px { left: -8px; }
    .xl-left-minus-9px { left: -9px; }
    .xl-left-minus-10px { left: -10px; }
    .xl-left-minus-15px { left: -15px; }
    .xl-left-minus-20px { left: -20px; }
    .xl-left-minus-25px { left: -25px; }
    .xl-left-minus-30px { left: -30px; }
    .xl-left-minus-35px { left: -35px; }
    .xl-left-minus-40px { left: -40px; }
    .xl-left-minus-45px { left: -45px; }
    .xl-left-minus-50px { left: -50px; }

    /* width */
    .xl-w-1px { width:1px !important; }
    .xl-w-2px { width:2px !important; }
    .xl-w-3px { width:3px !important; }
    .xl-w-4px { width:4px !important; }
    .xl-w-5px { width:5px !important; }
    .xl-w-6px { width:6px !important; }
    .xl-w-7px { width:7px !important; }
    .xl-w-8px { width:8px !important; }
    .xl-w-9px { width:9px !important; }
    .xl-w-10px { width:10px !important; }
    .xl-w-15px { width:15px !important; }
    .xl-w-20px { width:20px !important; }
    .xl-w-25px { width:25px !important; }
    .xl-w-30px { width:30px !important; }
    .xl-w-35px { width:35px !important; }
    .xl-w-40px { width:40px !important; }
    .xl-w-50px { width:50px !important; }
    .xl-w-55px { width:55px !important; }
    .xl-w-60px { width:60px !important; }
    .xl-w-65px { width:65px !important; }
    .xl-w-70px { width:70px !important; }
    .xl-w-75px { width:75px !important; }
    .xl-w-80px { width:80px !important; }
    .xl-w-85px { width:85px !important; }
    .xl-w-90px { width:90px !important; }
    .xl-w-95px { width:95px !important; }
    .xl-w-100px { width:100px !important; }
    .xl-w-110px { width:110px !important; }
    .xl-w-120px { width:120px !important; }
    .xl-w-130px { width:130px !important; }
    .xl-w-140px { width:140px !important; }
    .xl-w-150px { width:150px !important; }
    .xl-w-160px { width:160px !important; }
    .xl-w-170px { width:170px !important; }
    .xl-w-180px { width:180px !important; }
    .xl-w-190px { width:190px !important; }
    .xl-w-200px { width:200px !important; }
    .xl-w-250px { width:250px !important; }
    .xl-w-300px { width:300px !important; }
    .xl-w-350px { width:350px !important; }
    .xl-w-400px { width:400px !important; }
    .xl-w-450px { width:450px !important; }
    .xl-w-500px { width:500px !important; }
    .xl-w-550px { width:550px !important; }
    .xl-w-600px { width:600px !important; }
    .xl-w-650px { width:650px !important; }
    .xl-w-700px { width:700px !important; }
    .xl-w-750px { width:750px !important; }
    .xl-w-800px { width:800px !important; }
    .xl-w-850px { width:850px !important; }
    .xl-w-900px { width:900px !important; }
    .xl-w-950px { width:950px !important; }
    .xl-w-1000px { width:1000px !important; }
    .xl-w-10 { width: 10% !important; }
    .xl-w-15 { width: 15% !important; }
    .xl-w-20 { width: 20% !important; }
    .xl-w-25 { width: 25% !important; }
    .xl-w-30 { width: 30% !important; }
    .xl-w-35 { width: 35% !important; }
    .xl-w-40 { width: 40% !important; }
    .xl-w-45 { width: 45% !important; }
    .xl-w-50 { width: 50% !important; }
    .xl-w-55 { width: 55% !important; }
    .xl-w-60 { width: 60% !important; }
    .xl-w-65 { width: 65% !important; }
    .xl-w-70 { width: 70% !important; }
    .xl-w-75 { width: 75% !important; }
    .xl-w-80 { width: 80% !important; }
    .xl-w-85 { width: 85% !important; }
    .xl-w-90 { width: 90% !important; }
    .xl-w-95 { width: 95% !important; }
    .xl-w-100 { width: 100% !important; }
    .xl-w-auto { width:auto !important; }

    /* height */
    .xl-h-1px { height: 1px !important; }
    .xl-h-2px { height: 2px !important; }
    .xl-h-3px { height: 3px !important; }
    .xl-h-4px { height: 4px !important; }
    .xl-h-5px { height: 5px !important; }
    .xl-h-6px { height: 6px !important; }
    .xl-h-7px { height: 7px !important; }
    .xl-h-8px { height: 8px !important; }
    .xl-h-9px { height: 9px !important; }
    .xl-h-10px { height: 10px !important; }
    .xl-h-20px { height: 20px !important; }
    .xl-h-30px { height: 30px !important; }
    .xl-h-40px { height: 40px !important; }
    .xl-h-42px { height: 42px !important; }
    .xl-h-50px { height: 50px !important; }
    .xl-h-60px { height: 60px !important; }
    .xl-h-70px { height: 70px !important; }
    .xl-h-80px { height: 80px !important; }
    .xl-h-90px { height: 90px !important; }
    .xl-h-100px { height: 100px !important; }
    .xl-h-110px { height: 110px !important; }
    .xl-h-120px { height: 120px !important; }
    .xl-h-130px { height: 130px !important; }
    .xl-h-140px { height: 140px !important; }
    .xl-h-150px { height: 150px !important; }
    .xl-h-160px { height: 160px !important; }
    .xl-h-170px { height: 170px !important; }
    .xl-h-180px { height: 180px !important; }
    .xl-h-190px { height: 190px !important; }
    .xl-h-200px { height: 200px !important; }
    .xl-h-250px { height: 250px !important; }
    .xl-h-300px { height: 300px !important; }
    .xl-h-350px { height: 350px !important; }
    .xl-h-400px { height: 400px !important; }
    .xl-h-450px { height: 450px !important; }
    .xl-h-500px { height: 500px !important; }
    .xl-h-520px { height: 520px !important; }
    .xl-h-550px { height: 550px !important; }
    .xl-h-580px { height: 580px !important; }
    .xl-h-600px { height: 600px !important; }
    .xl-h-650px { height: 650px !important; }
    .xl-h-700px { height: 700px !important; }
    .xl-h-720px { height: 720px !important; }
    .xl-h-750px { height: 750px !important; }
    .xl-h-800px { height: 800px !important; }
    .xl-h-820px { height: 820px !important; }
    .xl-h-830px { height: 830px !important; }
    .xl-h-850px { height: 850px !important; }
    .xl-h-50 { height: 50% !important; }
    .xl-h-100 { height: 100% !important; }
    .xl-h-auto { height:auto !important; }        

    /* min-height */
    .xl-min-h-100px { min-height: 100px; }
    .xl-min-h-200px { min-height: 200px; }
    .xl-min-h-300px { min-height: 300px; }
    .xl-min-h-400px { min-height: 400px; }
    .xl-min-h-500px { min-height: 500px; }
    .xl-min-h-600px { min-height: 600px; }
    .xl-min-h-700px { min-height: 700px; }

    /* interactive banner style 09 */
    .interactive-banners-style-09 .interactive-banners-content .interactive-banners-hover-icon { left: 30px; bottom: 30px; }

    /* box layout */
    .box-layout { padding:0 45px; }
    .box-layout-large { padding:0 75px; }

    /* grid */
    .grid.xl-grid-6col li { width: 16.67%; }
    .grid.xl-grid-6col li.grid-item-double { width: 33.33%; }
    .grid.xl-grid-5col li { width: 20%; }
    .grid.xl-grid-5col li.grid-item-double { width: 40%; }
    .grid.xl-grid-4col li { width: 25%; }
    .grid.xl-grid-4col li.grid-item-double { width: 50%; }
    .grid.xl-grid-3col li { width: 33.33%; }
    .grid.xl-grid-3col li.grid-item-double { width: 66.67%; }
    .grid.xl-grid-2col li { width: 50%; }
    .grid.xl-grid-2col li.grid-item-double { width: 100%; }
    .grid.xl-grid-1col li { width: 100%; }

    /* architecture */
    .box-layout .navbar.navbar-boxed { padding-left: 30px; padding-right: 30px; }

    /* digital agency */
    .home-digital-agency .outside-box-text-right .text-extra-big-2 { font-size: 250px; }

    /* design agency */
    .home-design-agency .outside-box-left { margin-left: -40vw; }

    /* photography */
    .home-photography .interactive-banners-style-13 .interactive-banners-content { width: 40%; }

    /* landing page */
    .litho-parallax-bg { width: 800px; }
}

@media (max-width: 1500px) {
    /*  architecture */
    .home-architecture .tparrows.tp-leftarrow { transform: matrix(1, 0, 0, 1, -67, -328) !important; }
    .home-architecture .tparrows.tp-rightarrow { transform: matrix(1, 0, 0, 1, -67, -261) !important; }

    /* fashion shop */
    .home-fashion-shop .tp-tabs { left: 54% !important; }
}

@media (max-width: 1300px) {
    /* fashion shop */
    .home-fashion-shop .tp-tabs { left: 60% !important; }
}

@media (min-width: 1199px) {
    /* page container */
    .container, .container-lg, .container-md, .container-sm, .container-xl { max-width: 1170px; }
}

@media (max-width: 1199px) {
    /* reset */
    html { font-size: 13px; }
    .md-center-col { float:none; margin-left:auto; margin-right:auto; }
    section { padding: 90px 0 }
    section.big-section { padding:120px 0; }
    section.extra-big-section { padding:160px 0; }
    .center-col-style .custom-col { min-height: 0; }
    .parallax { background-attachment: inherit !important; background-position: center !important; }
    .lg-last-order { order: 10; }
    .html-video { height: 100%; }

    /* typography */
    h1, h2, h3, h4, h5, h6 { margin:0 0 15px; }

    /* text size */
    .title-large { font-size: 90px; line-height: 90px; }
    .text-extra-large { font-size: 18px; line-height: 24px; }
    .text-extra-big { font-size: 170px; line-height: 170px; }
    .text-big { font-size: 160px; line-height: 160px; }
    .title-extra-large { font-size: 110px; line-height: 100px; }

    /* lineheight */
    .lg-line-height-0px { line-height: 0px; }
    .lg-line-height-8px { line-height: 8px; }
    .lg-line-height-10px { line-height: 10px; }
    .lg-line-height-14px { line-height: 14px; }
    .lg-line-height-15px { line-height: 15px; }
    .lg-line-height-16px { line-height: 16px; }
    .lg-line-height-18px { line-height: 18px; }
    .lg-line-height-20px { line-height: 20px; }
    .lg-line-height-22px { line-height: 22px; }
    .lg-line-height-24px { line-height: 24px; }
    .lg-line-height-26px { line-height: 26px; }
    .lg-line-height-28px { line-height: 28px; }
    .lg-line-height-30px { line-height: 30px; }
    .lg-line-height-32px { line-height: 32px; }
    .lg-line-height-34px { line-height: 34px; }
    .lg-line-height-36px { line-height: 36px; }
    .lg-line-height-38px { line-height: 38px; }
    .lg-line-height-40px { line-height: 40px; }
    .lg-line-height-50px { line-height: 50px; }
    .lg-line-height-140px { line-height: 140px; }   
    .lg-line-height-normal { line-height: normal; }

    /* letter spacing minus */
    .lg-letter-spacing-minus-1-half { letter-spacing: -0.50px; }
    .lg-letter-spacing-minus-1px { letter-spacing: -1px; }
    .lg-letter-spacing-minus-2px { letter-spacing: -2px; }
    .lg-letter-spacing-minus-3px { letter-spacing: -3px; }
    .lg-letter-spacing-minus-4px { letter-spacing: -4px; }
    .lg-letter-spacing-minus-5px { letter-spacing: -5px; }

    /* absolute middle center */
    .lg-absolute-middle-center { left: 50%; top: 50%; position: absolute; -ms-transform: translateX(-50%) translateY(-50%); -moz-transform: translateX(-50%) translateY(-50%); -webkit-transform: translateX(-50%) translateY(-50%); transform: translateX(-50%) translateY(-50%); }

    /* background image */
    .lg-background-image-none { background: inherit !important; }
    .lg-background-position-left { background-position: left center; }
    .lg-background-position-right { background-position: right center; }
    .lg-background-position-top { background-position: right top; }
    .lg-background-position-center { background-position: center; }
    .lg-background-position-left-top { background-position: left top; }

    /* box shadow */
    .lg-box-shadow-none { box-shadow: none; }

    /* margin */
    .lg-margin-one-all { margin:1%; }
    .lg-margin-two-all { margin:2%; }
    .lg-margin-three-all { margin:3%; }
    .lg-margin-four-all { margin:4%; }
    .lg-margin-five-all { margin:5%; }
    .lg-margin-six-all { margin:6%; }
    .lg-margin-seven-all { margin:7%; }
    .lg-margin-eight-all { margin:8%; }
    .lg-margin-nine-all { margin:9%; }
    .lg-margin-ten-all { margin:10%; }
    .lg-margin-eleven-all { margin:11%; }
    .lg-margin-twelve-all { margin:12%; }
    .lg-margin-thirteen-all { margin:13%; }
    .lg-margin-fourteen-all { margin:14%; }
    .lg-margin-fifteen-all { margin:15%; }
    .lg-margin-sixteen-all { margin:16%; }
    .lg-margin-seventeen-all { margin:17%; }
    .lg-margin-eighteen-all { margin:18%; }
    .lg-margin-nineteen-all { margin:19%; }
    .lg-margin-twenty-all { margin:20%; }
    .lg-margin-5px-all { margin:5px; }
    .lg-margin-10px-all { margin:10px; }
    .lg-margin-15px-all { margin:15px; }
    .lg-margin-20px-all { margin:20px; }
    .lg-margin-25px-all { margin:25px; }
    .lg-margin-30px-all { margin:30px; }
    .lg-margin-35px-all { margin:35px; }
    .lg-margin-40px-all { margin:40px; }
    .lg-margin-45px-all { margin:45px; }
    .lg-margin-50px-all { margin:50px; }
    .lg-margin-55px-all { margin:55px; }
    .lg-margin-60px-all { margin:60px; }
    .lg-margin-65px-all { margin:65px; }
    .lg-margin-70px-all { margin:70px; }
    .lg-margin-75px-all { margin:75px; }
    .lg-margin-80px-all { margin:80px; }
    .lg-margin-85px-all { margin:85px; }
    .lg-margin-90px-all { margin:90px; }
    .lg-margin-95px-all { margin:95px; }
    .lg-margin-100px-all { margin:100px; }
    .lg-margin-1-rem-all { margin: 1rem; }
    .lg-margin-1-half-rem-all { margin: 1.5rem; }
    .lg-margin-2-rem-all { margin: 2rem; }
    .lg-margin-2-half-rem-all { margin: 2.5rem; }
    .lg-margin-3-rem-all { margin: 3rem; }
    .lg-margin-3-half-rem-all { margin: 3.5rem; }
    .lg-margin-4-rem-all { margin: 4rem; }
    .lg-margin-4-half-rem-all { margin: 4.5rem; }
    .lg-margin-5-rem-all { margin: 5rem; }
    .lg-margin-5-half-rem-all { margin: 5.5rem; }
    .lg-margin-6-rem-all { margin: 6rem; }
    .lg-margin-6-half-rem-all { margin: 6.5rem; }
    .lg-margin-7-rem-all { margin: 7rem; }
    .lg-margin-7-half-rem-all { margin: 7.5rem; }
    .lg-margin-8-rem-all { margin: 8rem; }
    .lg-margin-8-half-rem-all { margin: 8.5rem; }
    .lg-margin-9-rem-all { margin: 9rem; }
    .lg-margin-9-half-rem-all { margin: 9.5rem; }
    .lg-margin-10-rem-all { margin: 10rem; }
    .lg-margin-10-half-rem-all { margin: 10.5rem; }

    /* margin top */
    .lg-margin-one-top { margin-top:1%; }
    .lg-margin-two-top { margin-top:2%; }
    .lg-margin-three-top { margin-top:3%; }
    .lg-margin-four-top { margin-top:4%; }
    .lg-margin-five-top { margin-top:5%; }
    .lg-margin-six-top { margin-top:6%; }
    .lg-margin-seven-top { margin-top:7%; }
    .lg-margin-eight-top { margin-top:8%; }
    .lg-margin-nine-top { margin-top:9%; }
    .lg-margin-ten-top { margin-top:10%; }
    .lg-margin-eleven-top { margin-top:11%; }
    .lg-margin-twelve-top { margin-top:12%; }
    .lg-margin-thirteen-top { margin-top:13%; }
    .lg-margin-fourteen-top { margin-top:14%; }
    .lg-margin-fifteen-top { margin-top:15%; }
    .lg-margin-sixteen-top { margin-top:16%; }
    .lg-margin-seventeen-top { margin-top:17%; }
    .lg-margin-eighteen-top { margin-top:18%; }
    .lg-margin-nineteen-top { margin-top:19%; }
    .lg-margin-twenty-top { margin-top:20%; }
    .lg-margin-5px-top { margin-top:5px; }
    .lg-margin-10px-top { margin-top:10px; }
    .lg-margin-15px-top { margin-top:15px; }
    .lg-margin-20px-top { margin-top:20px; }
    .lg-margin-25px-top { margin-top:25px; }
    .lg-margin-30px-top { margin-top:30px; }
    .lg-margin-35px-top { margin-top:35px; }
    .lg-margin-40px-top { margin-top:40px; }
    .lg-margin-45px-top { margin-top:45px; }
    .lg-margin-50px-top { margin-top:50px; }
    .lg-margin-55px-top { margin-top:55px; }
    .lg-margin-60px-top { margin-top:60px; }
    .lg-margin-65px-top { margin-top:65px; }
    .lg-margin-70px-top { margin-top:70px; }
    .lg-margin-75px-top { margin-top:75px; }
    .lg-margin-80px-top { margin-top:80px; }
    .lg-margin-85px-top { margin-top:85px; }
    .lg-margin-90px-top { margin-top:90px; }
    .lg-margin-95px-top { margin-top:95px; }
    .lg-margin-100px-top { margin-top:100px; }
    .lg-margin-1-rem-top { margin-top: 1rem; }
    .lg-margin-1-half-rem-top { margin-top: 1.5rem; }
    .lg-margin-2-rem-top { margin-top: 2rem; }
    .lg-margin-2-half-rem-top { margin-top: 2.5rem; }
    .lg-margin-3-rem-top { margin-top: 3rem; }
    .lg-margin-3-half-rem-top { margin-top: 3.5rem; }
    .lg-margin-4-rem-top { margin-top: 4rem; }
    .lg-margin-4-half-rem-top { margin-top: 4.5rem; }
    .lg-margin-5-rem-top { margin-top: 5rem; }
    .lg-margin-5-half-rem-top { margin-top: 5.5rem; }
    .lg-margin-6-rem-top { margin-top: 6rem; }
    .lg-margin-6-half-rem-top { margin-top: 6.5rem; }
    .lg-margin-7-rem-top { margin-top: 7rem; }
    .lg-margin-7-half-rem-top { margin-top: 7.5rem; }
    .lg-margin-8-rem-top { margin-top: 8rem; }
    .lg-margin-8-half-rem-top { margin-top: 8.5rem; }
    .lg-margin-9-rem-top { margin-top: 9rem; }
    .lg-margin-9-half-rem-top { margin-top: 9.5rem; }
    .lg-margin-10-rem-top { margin-top: 10rem; }
    .lg-margin-10-half-rem-top { margin-top: 10.5rem; }

    /* margin bottom */
    .lg-margin-one-bottom { margin-bottom:1%; }
    .lg-margin-two-bottom { margin-bottom:2%; }
    .lg-margin-three-bottom { margin-bottom:3%; }
    .lg-margin-four-bottom { margin-bottom:4%; }
    .lg-margin-five-bottom { margin-bottom:5%; }
    .lg-margin-six-bottom { margin-bottom:6%; }
    .lg-margin-seven-bottom { margin-bottom:7%; }
    .lg-margin-eight-bottom { margin-bottom:8%; }
    .lg-margin-nine-bottom { margin-bottom:9%; }
    .lg-margin-ten-bottom { margin-bottom:10%; }
    .lg-margin-eleven-bottom { margin-bottom:11%; }
    .lg-margin-twelve-bottom { margin-bottom:12%; }
    .lg-margin-thirteen-bottom { margin-bottom:13%; }
    .lg-margin-fourteen-bottom { margin-bottom:14%; }
    .lg-margin-fifteen-bottom { margin-bottom:15%; }
    .lg-margin-sixteen-bottom { margin-bottom:16%; }
    .lg-margin-seventeen-bottom { margin-bottom:17%; }
    .lg-margin-eighteen-bottom { margin-bottom:18%; }
    .lg-margin-nineteen-bottom { margin-bottom:19%; }
    .lg-margin-twenty-bottom { margin-bottom:20%; }
    .lg-margin-5px-bottom { margin-bottom:5px; }
    .lg-margin-10px-bottom { margin-bottom:10px; }
    .lg-margin-15px-bottom { margin-bottom:15px; }
    .lg-margin-20px-bottom { margin-bottom:20px; }
    .lg-margin-25px-bottom { margin-bottom:25px; }
    .lg-margin-30px-bottom { margin-bottom:30px; }
    .lg-margin-35px-bottom { margin-bottom:35px; }
    .lg-margin-40px-bottom { margin-bottom:40px; }
    .lg-margin-45px-bottom { margin-bottom:45px; }
    .lg-margin-50px-bottom { margin-bottom:50px; }
    .lg-margin-55px-bottom { margin-bottom:55px; }
    .lg-margin-60px-bottom { margin-bottom:60px; }
    .lg-margin-65px-bottom { margin-bottom:65px; }
    .lg-margin-70px-bottom { margin-bottom:70px; }
    .lg-margin-75px-bottom { margin-bottom:75px; }
    .lg-margin-80px-bottom { margin-bottom:80px; }
    .lg-margin-85px-bottom { margin-bottom:85px; }
    .lg-margin-90px-bottom { margin-bottom:90px; }
    .lg-margin-95px-bottom { margin-bottom:95px; }
    .lg-margin-100px-bottom { margin-bottom:100px; }
    .lg-margin-1-rem-bottom { margin-bottom: 1rem; }
    .lg-margin-1-half-rem-bottom { margin-bottom: 1.5rem; }
    .lg-margin-2-rem-bottom { margin-bottom: 2rem; }
    .lg-margin-2-half-rem-bottom { margin-bottom: 2.5rem; }
    .lg-margin-3-rem-bottom { margin-bottom: 3rem; }
    .lg-margin-3-half-rem-bottom { margin-bottom: 3.5rem; }
    .lg-margin-4-rem-bottom { margin-bottom: 4rem; }
    .lg-margin-4-half-rem-bottom { margin-bottom: 4.5rem; }
    .lg-margin-5-rem-bottom { margin-bottom: 5rem; }
    .lg-margin-5-half-rem-bottom { margin-bottom: 5.5rem; }
    .lg-margin-6-rem-bottom { margin-bottom: 6rem; }
    .lg-margin-6-half-rem-bottom { margin-bottom: 6.5rem; }
    .lg-margin-7-rem-bottom { margin-bottom: 7rem; }
    .lg-margin-7-half-rem-bottom { margin-bottom: 7.5rem; }
    .lg-margin-8-rem-bottom { margin-bottom: 8rem; }
    .lg-margin-8-half-rem-bottom { margin-bottom: 8.5rem; }
    .lg-margin-9-rem-bottom { margin-bottom: 9rem; }
    .lg-margin-9-half-rem-bottom { margin-bottom: 9.5rem; }
    .lg-margin-10-rem-bottom { margin-bottom: 10rem; }
    .lg-margin-10-half-rem-bottom { margin-bottom: 10.5rem; }

    /* margin right */
    .lg-margin-one-right { margin-right:1%; }
    .lg-margin-two-right { margin-right:2%; }
    .lg-margin-three-right { margin-right:3%; }
    .lg-margin-four-right { margin-right:4%; }
    .lg-margin-five-right { margin-right:5%; }
    .lg-margin-six-right { margin-right:6%; }
    .lg-margin-seven-right { margin-right:7%; }
    .lg-margin-eight-right { margin-right:8%; }
    .lg-margin-nine-right { margin-right:9%; }
    .lg-margin-ten-right { margin-right:10%; }
    .lg-margin-eleven-right { margin-right:11%; }
    .lg-margin-twelve-right { margin-right:12%; }
    .lg-margin-thirteen-right { margin-right:13%; }
    .lg-margin-fourteen-right { margin-right:14%; }
    .lg-margin-fifteen-right { margin-right:15%; }
    .lg-margin-sixteen-right { margin-right:16%; }
    .lg-margin-seventeen-right { margin-right:17%; }
    .lg-margin-eighteen-right { margin-right:18%; }
    .lg-margin-nineteen-right { margin-right:19%; }
    .lg-margin-twenty-right { margin-right:20%; }
    .lg-margin-5px-right { margin-right:5px; }
    .lg-margin-10px-right { margin-right:10px; }
    .lg-margin-15px-right { margin-right:15px; }
    .lg-margin-20px-right { margin-right:20px; }
    .lg-margin-25px-right { margin-right:25px; }
    .lg-margin-30px-right { margin-right:30px; }
    .lg-margin-35px-right { margin-right:35px; }
    .lg-margin-40px-right { margin-right:40px; }
    .lg-margin-45px-right { margin-right:45px; }
    .lg-margin-50px-right { margin-right:50px; }
    .lg-margin-55px-right { margin-right:55px; }
    .lg-margin-60px-right { margin-right:60px; }
    .lg-margin-65px-right { margin-right:65px; }
    .lg-margin-70px-right { margin-right:70px; }
    .lg-margin-75px-right { margin-right:75px; }
    .lg-margin-80px-right { margin-right:80px; }
    .lg-margin-85px-right { margin-right:85px; }
    .lg-margin-90px-right { margin-right:90px; }
    .lg-margin-95px-right { margin-right:95px; }
    .lg-margin-100px-right { margin-right:100px; }
    .lg-margin-1-rem-right { margin-right: 1rem; }
    .lg-margin-1-half-rem-right { margin-right: 1.5rem; }
    .lg-margin-2-rem-right { margin-right: 2rem; }
    .lg-margin-2-half-rem-right { margin-right: 2.5rem; }
    .lg-margin-3-rem-right { margin-right: 3rem; }
    .lg-margin-3-half-rem-right { margin-right: 3.5rem; }
    .lg-margin-4-rem-right { margin-right: 4rem; }
    .lg-margin-4-half-rem-right { margin-right: 4.5rem; }
    .lg-margin-5-rem-right { margin-right: 5rem; }
    .lg-margin-5-half-rem-right { margin-right: 5.5rem; }
    .lg-margin-6-rem-right { margin-right: 6rem; }
    .lg-margin-6-half-rem-right { margin-right: 6.5rem; }
    .lg-margin-7-rem-right { margin-right: 7rem; }
    .lg-margin-7-half-rem-right { margin-right: 7.5rem; }
    .lg-margin-8-rem-right { margin-right: 8rem; }
    .lg-margin-8-half-rem-right { margin-right: 8.5rem; }
    .lg-margin-9-rem-right { margin-right: 9rem; }
    .lg-margin-9-half-rem-right { margin-right: 9.5rem; }
    .lg-margin-10-rem-right { margin-right: 10rem; }
    .lg-margin-10-half-rem-right { margin-right: 10.5rem; }

    /* margin left */
    .lg-margin-one-left { margin-left:1%; }
    .lg-margin-two-left { margin-left:2%; }
    .lg-margin-three-left { margin-left:3%; }
    .lg-margin-four-left { margin-left:4%; }
    .lg-margin-five-left { margin-left:5%; }
    .lg-margin-six-left { margin-left:6%; }
    .lg-margin-seven-left { margin-left:7%; }
    .lg-margin-eight-left { margin-left:8%; }
    .lg-margin-nine-left { margin-left:9%; }
    .lg-margin-ten-left { margin-left:10%; }
    .lg-margin-eleven-left { margin-left:11%; }
    .lg-margin-twelve-left { margin-left:12%; }
    .lg-margin-thirteen-left { margin-left:13%; }
    .lg-margin-fourteen-left { margin-left:14%; }
    .lg-margin-fifteen-left { margin-left:15%; }
    .lg-margin-sixteen-left { margin-left:16%; }
    .lg-margin-seventeen-left { margin-left:17%; }
    .lg-margin-eighteen-left { margin-left:18%; }
    .lg-margin-nineteen-left { margin-left:19%; }
    .lg-margin-twenty-left { margin-left:20%; }
    .lg-margin-5px-left { margin-left:5px; }
    .lg-margin-10px-left { margin-left:10px; }
    .lg-margin-15px-left { margin-left:15px; }
    .lg-margin-20px-left { margin-left:20px; }
    .lg-margin-25px-left { margin-left:25px; }
    .lg-margin-30px-left { margin-left:30px; }
    .lg-margin-35px-left { margin-left:35px; }
    .lg-margin-40px-left { margin-left:40px; }
    .lg-margin-45px-left { margin-left:45px; }
    .lg-margin-50px-left { margin-left:50px; }
    .lg-margin-55px-left { margin-left:55px; }
    .lg-margin-60px-left { margin-left:60px; }
    .lg-margin-65px-left { margin-left:65px; }
    .lg-margin-70px-left { margin-left:70px; }
    .lg-margin-75px-left { margin-left:75px; }
    .lg-margin-80px-left { margin-left:80px; }
    .lg-margin-85px-left { margin-left:85px; }
    .lg-margin-90px-left { margin-left:90px; }
    .lg-margin-95px-left { margin-left:95px; }
    .lg-margin-100px-left { margin-left:100px; }
    .lg-margin-1-rem-left { margin-left: 1rem; }
    .lg-margin-1-half-rem-left { margin-left: 1.5rem; }
    .lg-margin-2-rem-left { margin-left: 2rem; }
    .lg-margin-2-half-rem-left { margin-left: 2.5rem; }
    .lg-margin-3-rem-left { margin-left: 3rem; }
    .lg-margin-3-half-rem-left { margin-left: 3.5rem; }
    .lg-margin-4-rem-left { margin-left: 4rem; }
    .lg-margin-4-half-rem-left { margin-left: 4.5rem; }
    .lg-margin-5-rem-left { margin-left: 5rem; }
    .lg-margin-5-half-rem-left { margin-left: 5.5rem; }
    .lg-margin-6-rem-left { margin-left: 6rem; }
    .lg-margin-6-half-rem-left { margin-left: 6.5rem; }
    .lg-margin-7-rem-left { margin-left: 7rem; }
    .lg-margin-7-half-rem-left { margin-left: 7.5rem; }
    .lg-margin-8-rem-left { margin-left: 8rem; }
    .lg-margin-8-half-rem-left { margin-left: 8.5rem; }
    .lg-margin-9-rem-left { margin-left: 9rem; }
    .lg-margin-9-half-rem-left { margin-left: 9.5rem; }
    .lg-margin-10-rem-left { margin-left: 10rem; }
    .lg-margin-10-half-rem-left { margin-left: 10.5rem; }

    /* margin left right */
    .lg-margin-one-lr { margin-left:1%; margin-right:1%; }
    .lg-margin-two-lr { margin-left:2%; margin-right:2%; }
    .lg-margin-three-lr { margin-left:3%; margin-right:3%; }
    .lg-margin-four-lr { margin-left:4%; margin-right:4%; }
    .lg-margin-five-lr { margin-left:5%; margin-right:5%; }
    .lg-margin-six-lr { margin-left:6%; margin-right:6%; }
    .lg-margin-seven-lr { margin-left:7%; margin-right:7%; }
    .lg-margin-eight-lr { margin-left:8%; margin-right:8%; }
    .lg-margin-nine-lr { margin-left:9%; margin-right:9%; }
    .lg-margin-ten-lr { margin-left:10%; margin-right:10%; }
    .lg-margin-eleven-lr { margin-left:11%; margin-right:11%; }
    .lg-margin-twelve-lr { margin-left:12%; margin-right:12%; }
    .lg-margin-thirteen-lr { margin-left:13%; margin-right:13%; }
    .lg-margin-fourteen-lr { margin-left:14%; margin-right:14%; }
    .lg-margin-fifteen-lr { margin-left:15%; margin-right:15%; }
    .lg-margin-sixteen-lr { margin-left:16%; margin-right:16%; }
    .lg-margin-seventeen-lr { margin-left:17%; margin-right:17%; }
    .lg-margin-eighteen-lr { margin-left:18%; margin-right:18%; }
    .lg-margin-nineteen-lr { margin-left:19%; margin-right:19%; }
    .lg-margin-twenty-lr { margin-left:20%; margin-right:20%; }
    .lg-margin-5px-lr { margin-left:5px; margin-right:5px; }
    .lg-margin-10px-lr { margin-left:10px; margin-right:10px; }
    .lg-margin-15px-lr { margin-left:15px; margin-right:15px; }
    .lg-margin-20px-lr { margin-left:20px; margin-right:20px; }
    .lg-margin-25px-lr { margin-left:25px; margin-right:25px; }
    .lg-margin-30px-lr { margin-left:30px; margin-right:30px; }
    .lg-margin-35px-lr { margin-left:35px; margin-right:35px; }
    .lg-margin-40px-lr { margin-left:40px; margin-right:40px; }
    .lg-margin-45px-lr { margin-left:45px; margin-right:45px; }
    .lg-margin-50px-lr { margin-left:50px; margin-right:50px; }
    .lg-margin-55px-lr { margin-left:55px; margin-right:55px; }
    .lg-margin-60px-lr { margin-left:60px; margin-right:60px; }
    .lg-margin-65px-lr { margin-left:65px; margin-right:65px; }
    .lg-margin-70px-lr { margin-left:70px; margin-right:70px; }
    .lg-margin-75px-lr { margin-left:75px; margin-right:75px; }
    .lg-margin-80px-lr { margin-left:80px; margin-right:80px; }
    .lg-margin-85px-lr { margin-left:85px; margin-right:85px; }
    .lg-margin-90px-lr { margin-left:90px; margin-right:90px; }
    .lg-margin-95px-lr { margin-left:95px; margin-right:95px; }
    .lg-margin-100px-lr { margin-left:100px; margin-right:100px; }
    .lg-margin-1-rem-lr { margin-left: 1rem; margin-right: 1rem; }
    .lg-margin-1-half-rem-lr { margin-left: 1.5rem; margin-right: 1.5rem; }
    .lg-margin-2-rem-lr { margin-left: 2rem; margin-right: 2rem; }
    .lg-margin-2-half-rem-lr { margin-left: 2.5rem; margin-right: 2.5rem; }
    .lg-margin-3-rem-lr { margin-left: 3rem; margin-right: 3rem; }
    .lg-margin-3-half-rem-lr { margin-left: 3.5rem; margin-right: 3.5rem; }
    .lg-margin-4-rem-lr { margin-left: 4rem; margin-right: 4rem; }
    .lg-margin-4-half-rem-lr { margin-left: 4.5rem; margin-right: 4.5rem; }
    .lg-margin-5-rem-lr { margin-left: 5rem; margin-right: 5rem; }
    .lg-margin-5-half-rem-lr { margin-left: 5.5rem; margin-right: 5.5rem; }
    .lg-margin-6-rem-lr { margin-left: 6rem; margin-right: 6rem; }
    .lg-margin-6-half-rem-lr { margin-left: 6.5rem; margin-right: 6.5rem; }
    .lg-margin-7-rem-lr { margin-left: 7rem; margin-right: 7rem; }
    .lg-margin-7-half-rem-lr { margin-left: 7.5rem; margin-right: 7.5rem; }
    .lg-margin-8-rem-lr { margin-left: 8rem; margin-right: 8rem; }
    .lg-margin-8-half-rem-lr { margin-left: 8.5rem; margin-right: 8.5rem; }
    .lg-margin-9-rem-lr { margin-left: 9rem; margin-right: 9rem; }
    .lg-margin-9-half-rem-lr { margin-left: 9.5rem; margin-right: 9.5rem; }
    .lg-margin-10-rem-lr { margin-left: 10rem; margin-right: 10rem; }
    .lg-margin-10-half-rem-lr { margin-left: 10.5rem; margin-right: 10.5rem; }

    /* margin top bottom */
    .lg-margin-one-tb { margin-top:1%; margin-bottom:1%; }
    .lg-margin-two-tb { margin-top:2%; margin-bottom:2%; }
    .lg-margin-three-tb { margin-top:3%; margin-bottom:3%; }
    .lg-margin-four-tb { margin-top:4%; margin-bottom:4%; }
    .lg-margin-five-tb { margin-top:5%; margin-bottom:5%; }
    .lg-margin-six-tb { margin-top:6%; margin-bottom:6%; }
    .lg-margin-seven-tb { margin-top:7%; margin-bottom:7%; }
    .lg-margin-eight-tb { margin-top:8%; margin-bottom:8%; }
    .lg-margin-nine-tb { margin-top:9%; margin-bottom:9%; }
    .lg-margin-ten-tb { margin-top:10%; margin-bottom:10%; }
    .lg-margin-eleven-tb { margin-top:11%; margin-bottom:11%; }
    .lg-margin-twelve-tb { margin-top:12%; margin-bottom:12%; }
    .lg-margin-thirteen-tb { margin-top:13%; margin-bottom:13%; }
    .lg-margin-fourteen-tb { margin-top:14%; margin-bottom:14%; }
    .lg-margin-fifteen-tb { margin-top:15%; margin-bottom:15%; }
    .lg-margin-sixteen-tb { margin-top:16%; margin-bottom:16%; }
    .lg-margin-seventeen-tb { margin-top:17%; margin-bottom:17%; }
    .lg-margin-eighteen-tb { margin-top:18%; margin-bottom:18%; }
    .lg-margin-nineteen-tb { margin-top:19%; margin-bottom:19%; }
    .lg-margin-twenty-tb { margin-top:20%; margin-bottom:20%; }
    .lg-margin-5px-tb { margin-top:5px; margin-bottom:5px; }
    .lg-margin-10px-tb { margin-top:10px; margin-bottom:10px; }
    .lg-margin-15px-tb { margin-top:15px; margin-bottom:15px; }
    .lg-margin-20px-tb { margin-top:20px; margin-bottom:20px; }
    .lg-margin-25px-tb { margin-top:25px; margin-bottom:25px; }
    .lg-margin-30px-tb { margin-top:30px; margin-bottom:30px; }
    .lg-margin-35px-tb { margin-top:35px; margin-bottom:35px; }
    .lg-margin-40px-tb { margin-top:40px; margin-bottom:40px; }
    .lg-margin-45px-tb { margin-top:45px; margin-bottom:45px; }
    .lg-margin-50px-tb { margin-top:50px; margin-bottom:50px; }
    .lg-margin-55px-tb { margin-top:55px; margin-bottom:55px; }
    .lg-margin-60px-tb { margin-top:60px; margin-bottom:60px; }
    .lg-margin-65px-tb { margin-top:65px; margin-bottom:65px; }
    .lg-margin-70px-tb { margin-top:70px; margin-bottom:70px; }
    .lg-margin-75px-tb { margin-top:75px; margin-bottom:75px; }
    .lg-margin-80px-tb { margin-top:80px; margin-bottom:80px; }
    .lg-margin-85px-tb { margin-top:85px; margin-bottom:85px; }
    .lg-margin-90px-tb { margin-top:90px; margin-bottom:90px; }
    .lg-margin-95px-tb { margin-top:95px; margin-bottom:95px; }
    .lg-margin-100px-tb { margin-top:100px; margin-bottom:100px; }
    .lg-margin-1-rem-tb { margin-top: 1rem; margin-bottom: 1rem; }
    .lg-margin-1-half-rem-tb { margin-top: 1.5rem; margin-bottom: 1.5rem; }
    .lg-margin-2-rem-tb { margin-top: 2rem; margin-bottom: 2rem; }
    .lg-margin-2-half-rem-tb { margin-top: 2.5rem; margin-bottom: 2.5rem; }
    .lg-margin-3-rem-tb { margin-top: 3rem; margin-bottom: 3rem; }
    .lg-margin-3-half-rem-tb { margin-top: 3.5rem; margin-bottom: 3.5rem; }
    .lg-margin-4-rem-tb { margin-top: 4rem; margin-bottom: 4rem; }
    .lg-margin-4-half-rem-tb { margin-top: 4.5rem; margin-bottom: 4.5rem; }
    .lg-margin-5-rem-tb { margin-top: 5rem; margin-bottom: 5rem; }
    .lg-margin-5-half-rem-tb { margin-top: 5.5rem; margin-bottom: 5.5rem; }
    .lg-margin-6-rem-tb { margin-top: 6rem; margin-bottom: 6rem; }
    .lg-margin-6-half-rem-tb { margin-top: 6.5rem; margin-bottom: 6.5rem; }
    .lg-margin-7-rem-tb { margin-top: 7rem; margin-bottom: 7rem; }
    .lg-margin-7-half-rem-tb { margin-top: 7.5rem; margin-bottom: 7.5rem; }
    .lg-margin-8-rem-tb { margin-top: 8rem; margin-bottom: 8rem; }
    .lg-margin-8-half-rem-tb { margin-top: 8.5rem; margin-bottom: 8.5rem; }
    .lg-margin-9-rem-tb { margin-top: 9rem; margin-bottom: 9rem; }
    .lg-margin-9-half-rem-tb { margin-top: 9.5rem; margin-bottom: 9.5rem; }
    .lg-margin-10-rem-tb { margin-top: 10rem; margin-bottom: 10rem; }
    .lg-margin-10-half-rem-tb { margin-top: 10.5rem; margin-bottom: 10.5rem; }

    .lg-margin-auto-lr { margin-left: auto !important; margin-right: auto !important; }
    .lg-margin-auto { margin: auto; }
    .lg-no-margin { margin: 0 !important; }
    .lg-no-margin-top { margin-top: 0 !important; }
    .lg-no-margin-bottom { margin-bottom: 0 !important; }
    .lg-no-margin-left { margin-left: 0 !important; }
    .lg-no-margin-right { margin-right: 0 !important; }
    .lg-no-margin-tb { margin-top: 0 !important; margin-bottom: 0 !important; }
    .lg-no-margin-lr { margin-right: 0 !important; margin-left: 0 !important; }

    /* padding */
    .lg-padding-one-all { padding:1%; }
    .lg-padding-two-all { padding:2%; }
    .lg-padding-three-all { padding:3%; }
    .lg-padding-four-all { padding:4%; }
    .lg-padding-five-all { padding:5%; }
    .lg-padding-six-all { padding:6%; }
    .lg-padding-seven-all { padding:7%; }
    .lg-padding-eight-all { padding:8%; }
    .lg-padding-nine-all { padding:9%; }
    .lg-padding-ten-all { padding:10%; }
    .lg-padding-eleven-all { padding:11%; }
    .lg-padding-twelve-all { padding:12%; }
    .lg-padding-thirteen-all { padding:13%; }
    .lg-padding-fourteen-all { padding:14%; }
    .lg-padding-fifteen-all { padding:15%; }
    .lg-padding-sixteen-all { padding:16%; }
    .lg-padding-seventeen-all { padding:17%; }
    .lg-padding-eighteen-all { padding:18%; }
    .lg-padding-nineteen-all { padding:19%; }
    .lg-padding-twenty-all { padding:20%; }
    .lg-padding-5px-all { padding:5px; }
    .lg-padding-10px-all { padding:10px; }
    .lg-padding-15px-all { padding:15px; }
    .lg-padding-20px-all { padding:20px; }
    .lg-padding-25px-all { padding:25px; }
    .lg-padding-30px-all { padding:30px; }
    .lg-padding-35px-all { padding:35px; }
    .lg-padding-40px-all { padding:40px; }
    .lg-padding-45px-all { padding:45px; }
    .lg-padding-50px-all { padding:50px; }
    .lg-padding-55px-all { padding:55px; }
    .lg-padding-60px-all { padding:60px; }
    .lg-padding-65px-all { padding:65px; }
    .lg-padding-70px-all { padding:70px; }
    .lg-padding-75px-all { padding:75px; }
    .lg-padding-80px-all { padding:80px; }
    .lg-padding-85px-all { padding:85px; }
    .lg-padding-90px-all { padding:90px; }
    .lg-padding-95px-all { padding:95px; }
    .lg-padding-100px-all { padding:100px; }
    .lg-padding-1-rem-all { padding: 1rem; }
    .lg-padding-1-half-rem-all { padding: 1.5rem; }
    .lg-padding-2-rem-all { padding: 2rem; }
    .lg-padding-2-half-rem-all { padding: 2.5rem; }
    .lg-padding-3-rem-all { padding: 3rem; }
    .lg-padding-3-half-rem-all { padding: 3.5rem; }
    .lg-padding-4-rem-all { padding: 4rem; }
    .lg-padding-4-half-rem-all { padding: 4.5rem; }
    .lg-padding-5-rem-all { padding: 5rem; }
    .lg-padding-5-half-rem-all { padding: 5.5rem; }
    .lg-padding-6-rem-all { padding: 6rem; }
    .lg-padding-6-half-rem-all { padding: 6.5rem; }
    .lg-padding-7-rem-all { padding: 7rem; }
    .lg-padding-7-half-rem-all { padding: 7.5rem; }
    .lg-padding-8-rem-all { padding: 8rem; }
    .lg-padding-8-half-rem-all { padding: 8.5rem; }
    .lg-padding-9-rem-all { padding: 9rem; }
    .lg-padding-9-half-rem-all { padding: 9.5rem; }
    .lg-padding-10-rem-all { padding: 10rem; }
    .lg-padding-10-half-rem-all { padding: 10.5rem; }

    /* padding top */
    .lg-padding-one-top { padding-top:1%; }
    .lg-padding-two-top { padding-top:2%; }
    .lg-padding-three-top { padding-top:3%; }
    .lg-padding-four-top { padding-top:4%; }
    .lg-padding-five-top { padding-top:5%; }
    .lg-padding-six-top { padding-top:6%; }
    .lg-padding-seven-top { padding-top:7%; }
    .lg-padding-eight-top { padding-top:8%; }
    .lg-padding-nine-top { padding-top:9%; }
    .lg-padding-ten-top { padding-top:10%; }
    .lg-padding-eleven-top { padding-top:11%; }
    .lg-padding-twelve-top { padding-top:12%; }
    .lg-padding-thirteen-top { padding-top:13%; }
    .lg-padding-fourteen-top { padding-top:14%; }
    .lg-padding-fifteen-top { padding-top:15%; }
    .lg-padding-sixteen-top { padding-top:16%; }
    .lg-padding-seventeen-top { padding-top:17%; }
    .lg-padding-eighteen-top { padding-top:18%; }
    .lg-padding-nineteen-top { padding-top:19%; }
    .lg-padding-twenty-top { padding-top:20%; }
    .lg-padding-5px-top { padding-top:5px; }
    .lg-padding-10px-top { padding-top:10px; }
    .lg-padding-15px-top { padding-top:15px; }
    .lg-padding-20px-top { padding-top:20px; }
    .lg-padding-25px-top { padding-top:25px; }
    .lg-padding-30px-top { padding-top:30px; }
    .lg-padding-35px-top { padding-top:35px; }
    .lg-padding-40px-top { padding-top:40px; }
    .lg-padding-45px-top { padding-top:45px; }
    .lg-padding-50px-top { padding-top:50px; }
    .lg-padding-55px-top { padding-top:55px; }
    .lg-padding-60px-top { padding-top:60px; }
    .lg-padding-65px-top { padding-top:65px; }
    .lg-padding-70px-top { padding-top:70px; }
    .lg-padding-75px-top { padding-top:75px; }
    .lg-padding-80px-top { padding-top:80px; }
    .lg-padding-85px-top { padding-top:85px; }
    .lg-padding-90px-top { padding-top:90px; }
    .lg-padding-95px-top { padding-top:95px; }
    .lg-padding-100px-top { padding-top:100px; }
    .lg-padding-1-rem-top { padding-top: 1rem; }
    .lg-padding-1-half-rem-top { padding-top: 1.5rem; }
    .lg-padding-2-rem-top { padding-top: 2rem; }
    .lg-padding-2-half-rem-top { padding-top: 2.5rem; }
    .lg-padding-3-rem-top { padding-top: 3rem; }
    .lg-padding-3-half-rem-top { padding-top: 3.5rem; }
    .lg-padding-4-rem-top { padding-top: 4rem; }
    .lg-padding-4-half-rem-top { padding-top: 4.5rem; }
    .lg-padding-5-rem-top { padding-top: 5rem; }
    .lg-padding-5-half-rem-top { padding-top: 5.5rem; }
    .lg-padding-6-rem-top { padding-top: 6rem; }
    .lg-padding-6-half-rem-top { padding-top: 6.5rem; }
    .lg-padding-7-rem-top { padding-top: 7rem; }
    .lg-padding-7-half-rem-top { padding-top: 7.5rem; }
    .lg-padding-8-rem-top { padding-top: 8rem; }
    .lg-padding-8-half-rem-top { padding-top: 8.5rem; }
    .lg-padding-9-rem-top { padding-top: 9rem; }
    .lg-padding-9-half-rem-top { padding-top: 9.5rem; }
    .lg-padding-10-rem-top { padding-top: 10rem; }
    .lg-padding-10-half-rem-top { padding-top: 10.5rem; }

    /* padding bottom */
    .lg-padding-one-bottom { padding-bottom:1%; }
    .lg-padding-two-bottom { padding-bottom:2%; }
    .lg-padding-three-bottom { padding-bottom:3%; }
    .lg-padding-four-bottom { padding-bottom:4%; }
    .lg-padding-five-bottom { padding-bottom:5%; }
    .lg-padding-six-bottom { padding-bottom:6%; }
    .lg-padding-seven-bottom { padding-bottom:7%; }
    .lg-padding-eight-bottom { padding-bottom:8%; }
    .lg-padding-nine-bottom { padding-bottom:9%; }
    .lg-padding-ten-bottom { padding-bottom:10%; }
    .lg-padding-eleven-bottom { padding-bottom:11%; }
    .lg-padding-twelve-bottom { padding-bottom:12%; }
    .lg-padding-thirteen-bottom { padding-bottom:13%; }
    .lg-padding-fourteen-bottom { padding-bottom:14%; }
    .lg-padding-fifteen-bottom { padding-bottom:15%; }
    .lg-padding-sixteen-bottom { padding-bottom:16%; }
    .lg-padding-seventeen-bottom { padding-bottom:17%; }
    .lg-padding-eighteen-bottom { padding-bottom:18%; }
    .lg-padding-nineteen-bottom { padding-bottom:19%; }
    .lg-padding-twenty-bottom { padding-bottom:20%; }
    .lg-padding-5px-bottom { padding-bottom:5px; }
    .lg-padding-10px-bottom { padding-bottom:10px; }
    .lg-padding-15px-bottom { padding-bottom:15px; }
    .lg-padding-20px-bottom { padding-bottom:20px; }
    .lg-padding-25px-bottom { padding-bottom:25px; }
    .lg-padding-30px-bottom { padding-bottom:30px; }
    .lg-padding-35px-bottom { padding-bottom:35px; }
    .lg-padding-40px-bottom { padding-bottom:40px; }
    .lg-padding-45px-bottom { padding-bottom:45px; }
    .lg-padding-50px-bottom { padding-bottom:50px; }
    .lg-padding-55px-bottom { padding-bottom:55px; }
    .lg-padding-60px-bottom { padding-bottom:60px; }
    .lg-padding-65px-bottom { padding-bottom:65px; }
    .lg-padding-70px-bottom { padding-bottom:70px; }
    .lg-padding-75px-bottom { padding-bottom:75px; }
    .lg-padding-80px-bottom { padding-bottom:80px; }
    .lg-padding-85px-bottom { padding-bottom:85px; }
    .lg-padding-90px-bottom { padding-bottom:90px; }
    .lg-padding-95px-bottom { padding-bottom:95px; }
    .lg-padding-100px-bottom { padding-bottom:100px; }
    .lg-padding-1-rem-bottom { padding-bottom: 1rem; }
    .lg-padding-1-half-rem-bottom { padding-bottom: 1.5rem; }
    .lg-padding-2-rem-bottom { padding-bottom: 2rem; }
    .lg-padding-2-half-rem-bottom { padding-bottom: 2.5rem; }
    .lg-padding-3-rem-bottom { padding-bottom: 3rem; }
    .lg-padding-3-half-rem-bottom { padding-bottom: 3.5rem; }
    .lg-padding-4-rem-bottom { padding-bottom: 4rem; }
    .lg-padding-4-half-rem-bottom { padding-bottom: 4.5rem; }
    .lg-padding-5-rem-bottom { padding-bottom: 5rem; }
    .lg-padding-5-half-rem-bottom { padding-bottom: 5.5rem; }
    .lg-padding-6-rem-bottom { padding-bottom: 6rem; }
    .lg-padding-6-half-rem-bottom { padding-bottom: 6.5rem; }
    .lg-padding-7-rem-bottom { padding-bottom: 7rem; }
    .lg-padding-7-half-rem-bottom { padding-bottom: 7.5rem; }
    .lg-padding-8-rem-bottom { padding-bottom: 8rem; }
    .lg-padding-8-half-rem-bottom { padding-bottom: 8.5rem; }
    .lg-padding-9-rem-bottom { padding-bottom: 9rem; }
    .lg-padding-9-half-rem-bottom { padding-bottom: 9.5rem; }
    .lg-padding-10-rem-bottom { padding-bottom: 10rem; }
    .lg-padding-10-half-rem-bottom { padding-bottom: 10.5rem; }

    /* padding right */
    .lg-padding-one-right { padding-right:1%; }
    .lg-padding-two-right { padding-right:2%; }
    .lg-padding-three-right { padding-right:3%; }
    .lg-padding-four-right { padding-right:4% }
    .lg-padding-five-right { padding-right:5%; }
    .lg-padding-six-right { padding-right:6%; }
    .lg-padding-seven-right { padding-right:7%; }
    .lg-padding-eight-right { padding-right:8%; }
    .lg-padding-nine-right { padding-right:9%; }
    .lg-padding-ten-right { padding-right:10%; }
    .lg-padding-eleven-right { padding-right:11%; }
    .lg-padding-twelve-right { padding-right:12%; }
    .lg-padding-thirteen-right { padding-right:13%; }
    .lg-padding-fourteen-right { padding-right:14%; }
    .lg-padding-fifteen-right { padding-right:15%; }
    .lg-padding-sixteen-right { padding-right:16%; }
    .lg-padding-seventeen-right { padding-right:17%; }
    .lg-padding-eighteen-right { padding-right:18%; }
    .lg-padding-nineteen-right { padding-right:19%; }
    .lg-padding-twenty-right { padding-right:20%; }
    .lg-padding-5px-right { padding-right:5px; }
    .lg-padding-10px-right { padding-right:10px; }
    .lg-padding-15px-right { padding-right:15px; }
    .lg-padding-20px-right { padding-right:20px; }
    .lg-padding-25px-right { padding-right:25px; }
    .lg-padding-30px-right { padding-right:30px; }
    .lg-padding-35px-right { padding-right:35px; }
    .lg-padding-40px-right { padding-right:40px; }
    .lg-padding-45px-right { padding-right:45px; }
    .lg-padding-50px-right { padding-right:50px; }
    .lg-padding-55px-right { padding-right:55px; }
    .lg-padding-60px-right { padding-right:60px; }
    .lg-padding-65px-right { padding-right:65px; }
    .lg-padding-70px-right { padding-right:70px; }
    .lg-padding-75px-right { padding-right:75px; }
    .lg-padding-80px-right { padding-right:80px; }
    .lg-padding-85px-right { padding-right:85px; }
    .lg-padding-90px-right { padding-right:90px; }
    .lg-padding-95px-right { padding-right:95px; }
    .lg-padding-100px-right { padding-right:100px; }
    .lg-padding-1-rem-right { padding-right: 1rem; }
    .lg-padding-1-half-rem-right { padding-right: 1.5rem; }
    .lg-padding-2-rem-right { padding-right: 2rem; }
    .lg-padding-2-half-rem-right { padding-right: 2.5rem; }
    .lg-padding-3-rem-right { padding-right: 3rem; }
    .lg-padding-3-half-rem-right { padding-right: 3.5rem; }
    .lg-padding-4-rem-right { padding-right: 4rem; }
    .lg-padding-4-half-rem-right { padding-right: 4.5rem; }
    .lg-padding-5-rem-right { padding-right: 5rem; }
    .lg-padding-5-half-rem-right { padding-right: 5.5rem; }
    .lg-padding-6-rem-right { padding-right: 6rem; }
    .lg-padding-6-half-rem-right { padding-right: 6.5rem; }
    .lg-padding-7-rem-right { padding-right: 7rem; }
    .lg-padding-7-half-rem-right { padding-right: 7.5rem; }
    .lg-padding-8-rem-right { padding-right: 8rem; }
    .lg-padding-8-half-rem-right { padding-right: 8.5rem; }
    .lg-padding-9-rem-right { padding-right: 9rem; }
    .lg-padding-9-half-rem-right { padding-right: 9.5rem; }
    .lg-padding-10-rem-right { padding-right: 10rem; }
    .lg-padding-10-half-rem-right { padding-right: 10.5rem; }

    /* padding left */
    .lg-padding-one-left { padding-left:1%; }
    .lg-padding-two-left { padding-left:2%; }
    .lg-padding-three-left { padding-left:3%; }
    .lg-padding-four-left { padding-left:4%; }
    .lg-padding-five-left { padding-left:5%; }
    .lg-padding-six-left { padding-left:6%; }
    .lg-padding-seven-left { padding-left:7%; }
    .lg-padding-eight-left { padding-left:8%; }
    .lg-padding-nine-left { padding-left:9%; }
    .lg-padding-ten-left { padding-left:10%; }
    .lg-padding-eleven-left { padding-left:11%; }
    .lg-padding-twelve-left { padding-left:12%; }
    .lg-padding-thirteen-left { padding-left:13%; }
    .lg-padding-fourteen-left { padding-left:14%; }
    .lg-padding-fifteen-left { padding-left:15%; }
    .lg-padding-sixteen-left { padding-left:16%; }
    .lg-padding-seventeen-left { padding-left:17%; }
    .lg-padding-eighteen-left { padding-left:18%; }
    .lg-padding-nineteen-left { padding-left:19%; }
    .lg-padding-twenty-left { padding-left:20%; }
    .lg-padding-5px-left { padding-left:5px; }
    .lg-padding-10px-left { padding-left:10px; }
    .lg-padding-15px-left { padding-left:15px; }
    .lg-padding-20px-left { padding-left:20px; }
    .lg-padding-25px-left { padding-left:25px; }
    .lg-padding-30px-left { padding-left:30px; }
    .lg-padding-35px-left { padding-left:35px; }
    .lg-padding-40px-left { padding-left:40px; }
    .lg-padding-45px-left { padding-left:45px; }
    .lg-padding-50px-left { padding-left:50px; }
    .lg-padding-55px-left { padding-left:55px; }
    .lg-padding-60px-left { padding-left:60px; }
    .lg-padding-65px-left { padding-left:65px; }
    .lg-padding-70px-left { padding-left:70px; }
    .lg-padding-75px-left { padding-left:75px; }
    .lg-padding-80px-left { padding-left:80px; }
    .lg-padding-85px-left { padding-left:85px; }
    .lg-padding-90px-left { padding-left:90px; }
    .lg-padding-95px-left { padding-left:95px; }
    .lg-padding-100px-left { padding-left:100px; }
    .lg-padding-1-rem-left { padding-left: 1rem; }
    .lg-padding-1-half-rem-left { padding-left: 1.5rem; }
    .lg-padding-2-rem-left { padding-left: 2rem; }
    .lg-padding-2-half-rem-left { padding-left: 2.5rem; }
    .lg-padding-3-rem-left { padding-left: 3rem; }
    .lg-padding-3-half-rem-left { padding-left: 3.5rem; }
    .lg-padding-4-rem-left { padding-left: 4rem; }
    .lg-padding-4-half-rem-left { padding-left: 4.5rem; }
    .lg-padding-5-rem-left { padding-left: 5rem; }
    .lg-padding-5-half-rem-left { padding-left: 5.5rem; }
    .lg-padding-6-rem-left { padding-left: 6rem; }
    .lg-padding-6-half-rem-left { padding-left: 6.5rem; }
    .lg-padding-7-rem-left { padding-left: 7rem; }
    .lg-padding-7-half-rem-left { padding-left: 7.5rem; }
    .lg-padding-8-rem-left { padding-left: 8rem; }
    .lg-padding-8-half-rem-left { padding-left: 8.5rem; }
    .lg-padding-9-rem-left { padding-left: 9rem; }
    .lg-padding-9-half-rem-left { padding-left: 9.5rem; }
    .lg-padding-10-rem-left { padding-left: 10rem; }
    .lg-padding-10-half-rem-left { padding-left: 10.5rem; }

    /* padding top bottom */
    .lg-padding-one-tb { padding-top:1%; padding-bottom:1%; }
    .lg-padding-two-tb { padding-top:2%; padding-bottom:2%; }
    .lg-padding-three-tb { padding-top:3%; padding-bottom:3%; }
    .lg-padding-four-tb { padding-top:4%; padding-bottom:4%; }
    .lg-padding-five-tb { padding-top:5%; padding-bottom:5%; }
    .lg-padding-six-tb { padding-top:6%; padding-bottom:6%; }
    .lg-padding-seven-tb { padding-top:7%; padding-bottom:7%; }
    .lg-padding-eight-tb { padding-top:8%; padding-bottom:8%; }
    .lg-padding-nine-tb { padding-top:9%; padding-bottom:9%; }
    .lg-padding-ten-tb { padding-top:10%; padding-bottom:10%; }
    .lg-padding-eleven-tb { padding-top:11%; padding-bottom:11%; }
    .lg-padding-twelve-tb { padding-top:12%; padding-bottom:12%; }
    .lg-padding-thirteen-tb { padding-top:13%; padding-bottom:13%; }
    .lg-padding-fourteen-tb { padding-top:14%; padding-bottom:14%; }
    .lg-padding-fifteen-tb { padding-top:15%; padding-bottom:15%; }
    .lg-padding-sixteen-tb { padding-top:16%; padding-bottom:16%; }
    .lg-padding-seventeen-tb { padding-top:17%; padding-bottom:17%; }
    .lg-padding-eighteen-tb { padding-top:18%; padding-bottom:18%; }
    .lg-padding-nineteen-tb { padding-top:19%; padding-bottom:19%; }
    .lg-padding-twenty-tb { padding-top:20%; padding-bottom:20%; }
    .lg-padding-5px-tb { padding-top:5px; padding-bottom:5px; }
    .lg-padding-10px-tb { padding-top:10px; padding-bottom:10px; }
    .lg-padding-15px-tb { padding-top:15px; padding-bottom:15px; }
    .lg-padding-20px-tb { padding-top:20px; padding-bottom:20px; }
    .lg-padding-25px-tb { padding-top:25px; padding-bottom:25px; }
    .lg-padding-30px-tb { padding-top:30px; padding-bottom:30px; }
    .lg-padding-35px-tb { padding-top:35px; padding-bottom:35px; }
    .lg-padding-40px-tb { padding-top:40px; padding-bottom:40px; }
    .lg-padding-45px-tb { padding-top:45px; padding-bottom:45px; }
    .lg-padding-50px-tb { padding-top:50px; padding-bottom:50px; }
    .lg-padding-55px-tb { padding-top:55px; padding-bottom:55px; }
    .lg-padding-60px-tb { padding-top:60px; padding-bottom:60px; }
    .lg-padding-65px-tb { padding-top:65px; padding-bottom:65px; }
    .lg-padding-70px-tb { padding-top:70px; padding-bottom:70px; }
    .lg-padding-75px-tb { padding-top:75px; padding-bottom:75px; }
    .lg-padding-80px-tb { padding-top:80px; padding-bottom:80px; }
    .lg-padding-85px-tb { padding-top:85px; padding-bottom:85px; }
    .lg-padding-90px-tb { padding-top:90px; padding-bottom:90px; }
    .lg-padding-95px-tb { padding-top:95px; padding-bottom:95px; }
    .lg-padding-100px-tb { padding-top:100px; padding-bottom:100px; }
    .lg-padding-1-rem-tb { padding-top: 1rem; padding-bottom: 1rem; }
    .lg-padding-1-half-rem-tb { padding-top: 1.5rem; padding-bottom: 1.5rem; }
    .lg-padding-2-rem-tb { padding-top: 2rem; padding-bottom: 2rem; }
    .lg-padding-2-half-rem-tb { padding-top: 2.5rem; padding-bottom: 2.5rem; }
    .lg-padding-3-rem-tb { padding-top: 3rem; padding-bottom: 3rem; }
    .lg-padding-3-half-rem-tb { padding-top: 3.5rem; padding-bottom: 3.5rem; }
    .lg-padding-4-rem-tb { padding-top: 4rem; padding-bottom: 4rem; }
    .lg-padding-4-half-rem-tb { padding-top: 4.5rem; padding-bottom: 4.5rem; }
    .lg-padding-5-rem-tb { padding-top: 5rem; padding-bottom: 5rem; }
    .lg-padding-5-half-rem-tb { padding-top: 5.5rem; padding-bottom: 5.5rem; }
    .lg-padding-6-rem-tb { padding-top: 6rem; padding-bottom: 6rem; }
    .lg-padding-6-half-rem-tb { padding-top: 6.5rem; padding-bottom: 6.5rem; }
    .lg-padding-7-rem-tb { padding-top: 7rem; padding-bottom: 7rem; }
    .lg-padding-7-half-rem-tb { padding-top: 7.5rem; padding-bottom: 7.5rem; }
    .lg-padding-8-rem-tb { padding-top: 8rem; padding-bottom: 8rem; }
    .lg-padding-8-half-rem-tb { padding-top: 8.5rem; padding-bottom: 8.5rem; }
    .lg-padding-9-rem-tb { padding-top: 9rem; padding-bottom: 9rem; }
    .lg-padding-9-half-rem-tb { padding-top: 9.5rem; padding-bottom: 9.5rem; }
    .lg-padding-10-rem-tb { padding-top: 10rem; padding-bottom: 10rem; }
    .lg-padding-10-half-rem-tb { padding-top: 10.5rem; padding-bottom: 10.5rem; }

    /* padding left right */
    .lg-padding-one-lr { padding-left:1%; padding-right:1%; }
    .lg-padding-two-lr { padding-left:2%; padding-right:2%; }
    .lg-padding-three-lr { padding-left:3%; padding-right:3%; }
    .lg-padding-four-lr { padding-left:4%; padding-right:4%; }
    .lg-padding-five-lr { padding-left:5%; padding-right:5%; }
    .lg-padding-six-lr { padding-left:6%; padding-right:6%; }
    .lg-padding-seven-lr { padding-left:7%; padding-right:7%; }
    .lg-padding-eight-lr { padding-left:8%; padding-right:8%; }
    .lg-padding-nine-lr { padding-left:9%; padding-right:9%; }
    .lg-padding-ten-lr { padding-left:10%; padding-right:10%; }
    .lg-padding-eleven-lr { padding-left:11%; padding-right:11%; }
    .lg-padding-twelve-lr { padding-left:12%; padding-right:12%; }
    .lg-padding-thirteen-lr { padding-left:13%; padding-right:13%; }
    .lg-padding-fourteen-lr { padding-left:14%; padding-right:14%; }
    .lg-padding-fifteen-lr { padding-left:15%; padding-right:15%; }
    .lg-padding-sixteen-lr { padding-left:16%; padding-right:16%; }
    .lg-padding-seventeen-lr { padding-left:17%; padding-right:17%; }
    .lg-padding-eighteen-lr { padding-left:18%; padding-right:18%; }
    .lg-padding-nineteen-lr { padding-left:19%; padding-right:19%; }
    .lg-padding-twenty-lr { padding-left:20%; padding-right:20%; }
    .lg-padding-5px-lr { padding-left:5px; padding-right:5px; }
    .lg-padding-10px-lr { padding-left:10px; padding-right:10px; }
    .lg-padding-15px-lr { padding-left:15px; padding-right:15px; }
    .lg-padding-20px-lr { padding-left:20px; padding-right:20px; }
    .lg-padding-25px-lr { padding-left:25px; padding-right:25px; }
    .lg-padding-30px-lr { padding-left:30px; padding-right:30px; }
    .lg-padding-35px-lr { padding-left:35px; padding-right:35px; }
    .lg-padding-40px-lr { padding-left:40px; padding-right:40px; }
    .lg-padding-45px-lr { padding-left:45px; padding-right:45px; }
    .lg-padding-50px-lr { padding-left:50px; padding-right:50px; }
    .lg-padding-55px-lr { padding-left:55px; padding-right:55px; }
    .lg-padding-60px-lr { padding-left:60px; padding-right:60px; }
    .lg-padding-65px-lr { padding-left:65px; padding-right:65px; }
    .lg-padding-70px-lr { padding-left:70px; padding-right:70px; }
    .lg-padding-75px-lr { padding-left:75px; padding-right:75px; }
    .lg-padding-80px-lr { padding-left:80px; padding-right:80px; }
    .lg-padding-85px-lr { padding-left:85px; padding-right:85px; }
    .lg-padding-90px-lr { padding-left:90px; padding-right:90px; }
    .lg-padding-95px-lr { padding-left:95px; padding-right:95px; }
    .lg-padding-100px-lr { padding-left:100px; padding-right:100px; }
    .lg-padding-1-rem-lr { padding-left: 1rem; padding-right: 1rem; }
    .lg-padding-1-half-rem-lr { padding-left: 1.5rem; padding-right: 1.5rem; }
    .lg-padding-2-rem-lr { padding-left: 2rem; padding-right: 2rem; }
    .lg-padding-2-half-rem-lr { padding-left: 2.5rem; padding-right: 2.5rem; }
    .lg-padding-3-rem-lr { padding-left: 3rem; padding-right: 3rem; }
    .lg-padding-3-half-rem-lr { padding-left: 3.5rem; padding-right: 3.5rem; }
    .lg-padding-4-rem-lr { padding-left: 4rem; padding-right: 4rem; }
    .lg-padding-4-half-rem-lr { padding-left: 4.5rem; padding-right: 4.5rem; }
    .lg-padding-5-rem-lr { padding-left: 5rem; padding-right: 5rem; }
    .lg-padding-5-half-rem-lr { padding-left: 5.5rem; padding-right: 5.5rem; }
    .lg-padding-6-rem-lr { padding-left: 6rem; padding-right: 6rem; }
    .lg-padding-6-half-rem-lr { padding-left: 6.5rem; padding-right: 6.5rem; }
    .lg-padding-7-rem-lr { padding-left: 7rem; padding-right: 7rem; }
    .lg-padding-7-half-rem-lr { padding-left: 7.5rem; padding-right: 7.5rem; }
    .lg-padding-8-rem-lr { padding-left: 8rem; padding-right: 8rem; }
    .lg-padding-8-half-rem-lr { padding-left: 8.5rem; padding-right: 8.5rem; }
    .lg-padding-9-rem-lr { padding-left: 9rem; padding-right: 9rem; }
    .lg-padding-9-half-rem-lr { padding-left: 9.5rem; padding-right: 9.5rem; }
    .lg-padding-10-rem-lr { padding-left: 10rem; padding-right: 10rem; }
    .lg-padding-10-half-rem-lr { padding-left: 10.5rem; padding-right: 10.5rem; }

    .lg-no-padding { padding:0 !important; }
    .lg-no-padding-lr { padding-left: 0 !important; padding-right: 0 !important; }
    .lg-no-padding-tb { padding-top: 0 !important; padding-bottom: 0 !important; }
    .lg-no-padding-top { padding-top:0 !important; }
    .lg-no-padding-bottom { padding-bottom:0 !important; }
    .lg-no-padding-left { padding-left:0 !important; }
    .lg-no-padding-right { padding-right:0 !important; }

    /* display and overflow */
    .lg-d-initial { display: initial !important; }
    .lg-overflow-hidden { overflow:hidden !important; }
    .lg-overflow-visible { overflow:visible !important; }
    .lg-overflow-auto { overflow:auto !important; }

    /* position */
    .lg-position-relative { position: relative !important; }
    .lg-position-absolute { position: absolute !important; }
    .lg-position-fixed { position: fixed !important; }
    .lg-position-inherit { position: inherit !important; }
    .lg-position-initial { position: initial !important; }

    /* top */
    .lg-top-0px { top: 0; }
    .lg-top-1px { top: 1px; }
    .lg-top-2px { top: 2px; }
    .lg-top-3px { top: 3px; }
    .lg-top-4px { top: 4px; }
    .lg-top-5px { top: 5px; }
    .lg-top-6px { top: 6px; }
    .lg-top-7px { top: 7px; }
    .lg-top-8px { top: 8px; }
    .lg-top-9px { top: 9px; }
    .lg-top-10px { top: 10px; }
    .lg-top-15px { top: 15px; }
    .lg-top-20px { top: 20px; }
    .lg-top-25px { top: 25px; }
    .lg-top-30px { top: 30px; }
    .lg-top-35px { top: 35px; }
    .lg-top-40px { top: 40px; }
    .lg-top-45px { top: 45px; }
    .lg-top-50px { top: 50px; }
    .lg-top-auto { top:auto; }
    .lg-top-inherit { top:inherit; }

    /* top minus */
    .lg-top-minus-1px { top: -1px; }
    .lg-top-minus-2px { top: -2px; }
    .lg-top-minus-3px { top: -3px; }
    .lg-top-minus-4px { top: -4px; }
    .lg-top-minus-5px { top: -5px; }
    .lg-top-minus-6px { top: -6px; }
    .lg-top-minus-7px { top: -7px; }
    .lg-top-minus-8px { top: -8px; }
    .lg-top-minus-9px { top: -9px; }
    .lg-top-minus-10px { top: -10px; }
    .lg-top-minus-15px { top: -15px; }
    .lg-top-minus-20px { top: -20px; }
    .lg-top-minus-25px { top: -25px; }
    .lg-top-minus-30px { top: -30px; }
    .lg-top-minus-35px { top: -35px; }
    .lg-top-minus-40px { top: -40px; }
    .lg-top-minus-45px { top: -45px; }
    .lg-top-minus-50px { top: -50px; }

    /* bottom */
    .lg-bottom-0px { bottom:0; }
    .lg-bottom-1px { bottom:1px; }
    .lg-bottom-2px { bottom:2px; }
    .lg-bottom-3px { bottom:3px; }
    .lg-bottom-4px { bottom:4px; }
    .lg-bottom-5px { bottom:5px; }
    .lg-bottom-6px { bottom:6px; }
    .lg-bottom-7px { bottom:7px; }
    .lg-bottom-8px { bottom:8px; }
    .lg-bottom-9px { bottom:9px; }
    .lg-bottom-10px { bottom:10px; }
    .lg-bottom-15px { bottom:15px; }
    .lg-bottom-20px { bottom:20px; }
    .lg-bottom-25px { bottom:25px; }
    .lg-bottom-30px { bottom:30px; }
    .lg-bottom-35px { bottom:35px; }
    .lg-bottom-40px { bottom:40px; }
    .lg-bottom-45px { bottom:45px; }
    .lg-bottom-50px { bottom:50px; }
    .lg-bottom-55px { bottom:55px; }
    .lg-bottom-60px { bottom:60px; }
    .lg-bottom-auto { bottom: auto; }
    .lg-bottom-inherit { bottom: inherit; }

    /* bottom minus */
    .lg-bottom-minus-1px { bottom: -1px; }
    .lg-bottom-minus-2px { bottom: -2px; }
    .lg-bottom-minus-3px { bottom: -3px; }
    .lg-bottom-minus-4px { bottom: -4px; }
    .lg-bottom-minus-5px { bottom: -5px; }
    .lg-bottom-minus-6px { bottom: -6px; }
    .lg-bottom-minus-7px { bottom: -7px; }
    .lg-bottom-minus-8px { bottom: -8px; }
    .lg-bottom-minus-9px { bottom: -9px; }
    .lg-bottom-minus-10px { bottom: -10px; }
    .lg-bottom-minus-15px { bottom: -15px; }
    .lg-bottom-minus-20px { bottom: -20px; }
    .lg-bottom-minus-25px { bottom: -25px; }
    .lg-bottom-minus-30px { bottom: -30px; }
    .lg-bottom-minus-35px { bottom: -35px; }
    .lg-bottom-minus-40px { bottom: -40px; }
    .lg-bottom-minus-45px { bottom: -45px; }
    .lg-bottom-minus-50px { bottom: -50px; }

    /* right */
    .lg-right-0px { right: 0; }
    .lg-right-1px { right: 1px; }
    .lg-right-2px { right: 2px; }
    .lg-right-3px { right: 3px; }
    .lg-right-4px { right: 4px; }
    .lg-right-5px { right: 5px; }
    .lg-right-6px { right: 6px; }
    .lg-right-7px { right: 7px; }
    .lg-right-8px { right: 8px; }
    .lg-right-9px { right: 9px; }
    .lg-right-10px { right: 10px; }
    .lg-right-15px { right: 15px; }
    .lg-right-20px { right: 20px; }
    .lg-right-25px { right: 25px; }
    .lg-right-30px { right: 30px; }
    .lg-right-35px { right: 35px; }
    .lg-right-40px { right: 40px; }
    .lg-right-45px { right: 45px; }
    .lg-right-50px { right: 50px; }
    .lg-right-auto { right: auto; }
    .lg-right-inherit { right: inherit; }

    /* right minus */
    .lg-right-minus-1px { right: -1px; }
    .lg-right-minus-2px { right: -2px; }
    .lg-right-minus-3px { right: -3px; }
    .lg-right-minus-4px { right: -4px; }
    .lg-right-minus-5px { right: -5px; }
    .lg-right-minus-6px { right: -6px; }
    .lg-right-minus-7px { right: -7px; }
    .lg-right-minus-8px { right: -8px; }
    .lg-right-minus-9px { right: -9px; }
    .lg-right-minus-10px { right: -10px; }
    .lg-right-minus-15px { right: -15px; }
    .lg-right-minus-20px { right: -20px; }
    .lg-right-minus-25px { right: -25px; }
    .lg-right-minus-30px { right: -30px; }
    .lg-right-minus-35px { right: -35px; }
    .lg-right-minus-40px { right: -40px; }
    .lg-right-minus-45px { right: -45px; }
    .lg-right-minus-50px { right: -50px; }

    /* left */
    .lg-left-0px { left: 0; }
    .lg-left-1px { left: 1px; }
    .lg-left-2px { left: 2px; }
    .lg-left-3px { left: 3px; }
    .lg-left-4px { left: 4px; }
    .lg-left-5px { left: 5px; }
    .lg-left-6px { left: 6px; }
    .lg-left-7px { left: 7px; }
    .lg-left-8px { left: 8px; }
    .lg-left-9px { left: 9px; }
    .lg-left-10px { left: 10px; }
    .lg-left-15px { left: 15px; }
    .lg-left-20px { left: 20px; }
    .lg-left-25px { left: 25px; }
    .lg-left-30px { left: 30px; }
    .lg-left-35px { left: 35px; }
    .lg-left-40px { left: 40px; }
    .lg-left-45px { left: 45px; }
    .lg-left-50px { left: 50px; }
    .lg-left-55px { left: 55px; }
    .lg-left-60px { left: 60px; }
    .lg-left-auto { left: auto; }
    .lg-left-inherit { left: inherit; }

    /* left minus */
    .lg-left-minus-1px { left: -1px; }
    .lg-left-minus-2px { left: -2px; }
    .lg-left-minus-3px { left: -3px; }
    .lg-left-minus-4px { left: -4px; }
    .lg-left-minus-5px { left: -5px; }
    .lg-left-minus-6px { left: -6px; }
    .lg-left-minus-7px { left: -7px; }
    .lg-left-minus-8px { left: -8px; }
    .lg-left-minus-9px { left: -9px; }
    .lg-left-minus-10px { left: -10px; }
    .lg-left-minus-15px { left: -15px; }
    .lg-left-minus-20px { left: -20px; }
    .lg-left-minus-25px { left: -25px; }
    .lg-left-minus-30px { left: -30px; }
    .lg-left-minus-35px { left: -35px; }
    .lg-left-minus-40px { left: -40px; }
    .lg-left-minus-45px { left: -45px; }
    .lg-left-minus-50px { left: -50px; }

    /* width */
    .lg-w-1px { width:1px !important; }
    .lg-w-2px { width:2px !important; }
    .lg-w-3px { width:3px !important; }
    .lg-w-4px { width:4px !important; }
    .lg-w-5px { width:5px !important; }
    .lg-w-6px { width:6px !important; }
    .lg-w-7px { width:7px !important; }
    .lg-w-8px { width:8px !important; }
    .lg-w-9px { width:9px !important; }
    .lg-w-10px { width:10px !important; }
    .lg-w-15px { width:15px !important; }
    .lg-w-20px { width:20px !important; }
    .lg-w-25px { width:25px !important; }
    .lg-w-30px { width:30px !important; }
    .lg-w-35px { width:35px !important; }
    .lg-w-40px { width:40px !important; }
    .lg-w-50px { width:50px !important; }
    .lg-w-55px { width:55px !important; }
    .lg-w-60px { width:60px !important; }
    .lg-w-65px { width:65px !important; }
    .lg-w-70px { width:70px !important; }
    .lg-w-75px { width:75px !important; }
    .lg-w-80px { width:80px !important; }
    .lg-w-85px { width:85px !important; }
    .lg-w-90px { width:90px !important; }
    .lg-w-95px { width:95px !important; }
    .lg-w-100px { width:100px !important; }
    .lg-w-110px { width:110px !important; }
    .lg-w-120px { width:120px !important; }
    .lg-w-130px { width:130px !important; }
    .lg-w-140px { width:140px !important; }
    .lg-w-150px { width:150px !important; }
    .lg-w-160px { width:160px !important; }
    .lg-w-170px { width:170px !important; }
    .lg-w-180px { width:180px !important; }
    .lg-w-190px { width:190px !important; }
    .lg-w-200px { width:200px !important; }
    .lg-w-250px { width:250px !important; }
    .lg-w-300px { width:300px !important; }
    .lg-w-350px { width:350px !important; }
    .lg-w-400px { width:400px !important; }
    .lg-w-450px { width:450px !important; }
    .lg-w-500px { width:500px !important; }
    .lg-w-550px { width:550px !important; }
    .lg-w-600px { width:600px !important; }
    .lg-w-650px { width:650px !important; }
    .lg-w-700px { width:700px !important; }
    .lg-w-750px { width:750px !important; }
    .lg-w-800px { width:800px !important; }
    .lg-w-850px { width:850px !important; }
    .lg-w-900px { width:900px !important; }
    .lg-w-950px { width:950px !important; }
    .lg-w-1000px { width:1000px !important; }
    .lg-w-10 { width: 10% !important; }
    .lg-w-15 { width: 15% !important; }
    .lg-w-20 { width: 20% !important; }
    .lg-w-25 { width: 25% !important; }
    .lg-w-30 { width: 30% !important; }
    .lg-w-35 { width: 35% !important; }
    .lg-w-40 { width: 40% !important; }
    .lg-w-45 { width: 45% !important; }
    .lg-w-50 { width: 50% !important; }
    .lg-w-55 { width: 55% !important; }
    .lg-w-60 { width: 60% !important; }
    .lg-w-65 { width: 65% !important; }
    .lg-w-70 { width: 70% !important; }
    .lg-w-75 { width: 75% !important; }
    .lg-w-80 { width: 80% !important; }
    .lg-w-85 { width: 85% !important; }
    .lg-w-90 { width: 90% !important; }
    .lg-w-95 { width: 95% !important; }
    .lg-w-100 { width: 100% !important; }
    .lg-w-auto { width:auto !important; }

    /* height */
    .lg-h-1px { height: 1px !important; }
    .lg-h-2px { height: 2px !important; }
    .lg-h-3px { height: 3px !important; }
    .lg-h-4px { height: 4px !important; }
    .lg-h-5px { height: 5px !important; }
    .lg-h-6px { height: 6px !important; }
    .lg-h-7px { height: 7px !important; }
    .lg-h-8px { height: 8px !important; }
    .lg-h-9px { height: 9px !important; }
    .lg-h-10px { height: 10px !important; }
    .lg-h-20px { height: 20px !important; }
    .lg-h-30px { height: 30px !important; }
    .lg-h-40px { height: 40px !important; }
    .lg-h-42px { height: 42px !important; }
    .lg-h-50px { height: 50px !important; }
    .lg-h-60px { height: 60px !important; }
    .lg-h-70px { height: 70px !important; }
    .lg-h-80px { height: 80px !important; }
    .lg-h-90px { height: 90px !important; }
    .lg-h-100px { height: 100px !important; }
    .lg-h-110px { height: 110px !important; }
    .lg-h-120px { height: 120px !important; }
    .lg-h-130px { height: 130px !important; }
    .lg-h-140px { height: 140px !important; }
    .lg-h-150px { height: 150px !important; }
    .lg-h-160px { height: 160px !important; }
    .lg-h-170px { height: 170px !important; }
    .lg-h-180px { height: 180px !important; }
    .lg-h-190px { height: 190px !important; }
    .lg-h-200px { height: 200px !important; }
    .lg-h-250px { height: 250px !important; }
    .lg-h-300px { height: 300px !important; }
    .lg-h-350px { height: 350px !important; }
    .lg-h-400px { height: 400px !important; }
    .lg-h-450px { height: 450px !important; }
    .lg-h-500px { height: 500px !important; }
    .lg-h-520px { height: 520px !important; }
    .lg-h-550px { height: 550px !important; }
    .lg-h-580px { height: 580px !important; }
    .lg-h-600px { height: 600px !important; }
    .lg-h-650px { height: 650px !important; }
    .lg-h-700px { height: 700px !important; }
    .lg-h-720px { height: 720px !important; }
    .lg-h-750px { height: 750px !important; }
    .lg-h-800px { height: 800px !important; }
    .lg-h-820px { height: 820px !important; }
    .lg-h-830px { height: 830px !important; }
    .lg-h-850px { height: 850px !important; }
    .lg-h-50 { height: 50% !important; }
    .lg-h-100 { height: 100% !important; }
    .lg-h-auto { height:auto !important; }

    /* min-height */
    .lg-min-h-100px { min-height: 100px; }
    .lg-min-h-200px { min-height: 200px; }
    .lg-min-h-300px { min-height: 300px; }
    .lg-min-h-400px { min-height: 400px; }
    .lg-min-h-500px { min-height: 500px; }
    .lg-min-h-600px { min-height: 600px; }
    .lg-min-h-700px { min-height: 700px; }

    /* screen height */
    .one-fifth-screen { height:750px; }

    /* letter spacing */
    .lg-letter-spacing-normal { letter-spacing: normal; }
    .lg-letter-spacing-1-half { letter-spacing: 0.50px; }
    .lg-letter-spacing-1px { letter-spacing: 1px; }
    .lg-letter-spacing-2px { letter-spacing: 2px; }
    .lg-letter-spacing-3px { letter-spacing: 3px; }
    .lg-letter-spacing-4px { letter-spacing: 4px; }
    .lg-letter-spacing-5px { letter-spacing: 5px; }

    /* interactive banner style 02 */
    .interactive-banners-style-02 .category-name { left: 40px; }
    .interactive-banners-style-02 .category-content { padding: 20px 40px 40px; }

    /* interactive banner style 05 */
    .interactive-banners-style-05 .interactive-banners-content, .interactive-banners-style-05 .interactive-banners-overlayer { transform: translateY(calc(100% - 125px)); -webkit-transform: translateY(calc(100% - 125px)); -moz-transform: translateY(calc(100% - 125px)); -ms-transform: translateY(calc(100% - 125px)); }

    /* interactive banner style 09 */
    .interactive-banners-style-09 .interactive-banners-content .interactive-banners-hover-icon { left: 50px; bottom: 50px; }

    /* process step style 04 */
    .process-step-style-04 .process-step-item { padding-left: 15px; padding-right: 15px; }

    /* accordion style 04 */
    .accordion-style-04 .panel .panel-body { width: 45%; }

    /* time table */
    .time-table .panel { padding: 20px 40px; }
    .time-table .panel .panel-time { min-width: 170px; }
    .time-table .panel .panel-body { width: calc(100% - 350px); }

    /* tab style 05 */
    .tab-style-05 .nav-tabs li a { padding: 8px 30px; }

    /* tab style 07 */
    .tab-style-07 .nav-tabs > li.nav-item { padding: 0 10px; }
    .tab-style-07 .nav-tabs > li.nav-item > a.nav-link { padding: 30px 30px 28px 30px; }

    /* no border */
    .lg-no-border-top { border-top:0 !important }
    .lg-no-border-bottom { border-bottom:0 !important }
    .lg-no-border-right { border-right:0 !important }
    .lg-no-border-left { border-left:0 !important }
    .lg-no-border-all { border: 0 !important }

    /* border width */
    .lg-border-width-1px { border-width:1px !important; }
    .lg-border-width-2px { border-width:2px !important; }
    .lg-border-width-3px { border-width:3px !important; }
    .lg-border-width-4px { border-width:4px !important; }
    .lg-border-width-5px { border-width:5px !important; }
    .lg-border-width-6px { border-width:6px !important; }
    .lg-border-width-7px { border-width:7px !important; }
    .lg-border-width-8px { border-width:8px !important; }
    .lg-border-width-9px { border-width:9px !important; }
    .lg-border-width-10px { border-width:10px !important; }
    .lg-border-width-11px { border-width:11px !important; }
    .lg-border-width-12px { border-width:12px !important; }
    .lg-border-width-13px { border-width:13px !important; }
    .lg-border-width-14px { border-width:14px !important; }
    .lg-border-width-15px { border-width:15px !important; }
    .lg-border-width-16px { border-width:16px !important; }
    .lg-border-width-17px { border-width:17px !important; }
    .lg-border-width-18px { border-width:18px !important; }
    .lg-border-width-19px { border-width:19px !important; }
    .lg-border-width-20px { border-width:20px !important; }

    /* border */
    .lg-border-all { border: 1px solid; }
    .lg-border-top { border-top: 1px solid; }
    .lg-border-bottom { border-bottom: 1px solid; }
    .lg-border-left { border-left: 1px solid; }
    .lg-border-right { border-right: 1px solid; }
    .lg-border-lr { border-left: 1px solid; border-right: 1px solid; }
    .lg-border-tb { border-top: 1px solid; border-bottom: 1px solid; }

    /* border color */
    .lg-border-color-white { border-color: #fff; }
    .lg-border-color-black { border-color: #000; }
    .lg-border-color-sky-blue { border-color: #2e94eb; }
    .lg-border-color-extra-dark-gray { border-color: #232323; }
    .lg-border-color-medium-dark-gray { border-color: #363636; }
    .lg-border-color-dark-gray { border-color: #939393; }
    .lg-border-color-extra-medium-gray { border-color: #dbdbdb; }
    .lg-border-color-medium-gray { border-color: #e4e4e4; }
    .lg-border-color-extra-light-gray { border-color: #ededed; }
    .lg-border-color-light-gray { border-color: #f5f5f5; }
    .lg-border-color-light-pink { border-color: #862237; }
    .lg-border-color-deep-pink { border-color: #ff214f; }
    .lg-border-color-pink { border-color: #ff357c; }
    .lg-border-color-fast-blue { border-color: #0038e3; }
    .lg-border-color-orange { border-color: #ff6437; }
    .lg-border-color-green { border-color: #45d690; }
    .lg-border-color-golden { border-color: #d0ba6d; }
    .lg-border-color-persian-blue { border-color: #0039CC; }
    .lg-border-color-purple { border-color: #7342ac; }
    .lg-border-color-parrot-green { border-color: #cee002; }
    .lg-border-color-dark-red { border-color: #e12837; }

    /* transparent border */
    .lg-border-color-transparent { border-color: transparent; }
    .lg-border-color-black-transparent { border-color: rgba(0,0,0,.1); }
    .lg-border-color-white-transparent { border-color: rgba(255,255,255,.1); }
    .lg-border-color-golden-transparent { border-color: rgba(208, 186, 109, 0.2); }
    .lg-border-color-pink-transparent { border-color: rgba(255, 33, 79, 0.45); }
    .lg-border-color-dark-white-transparent { border-color: rgba(255,255,255,0.2); }
    .lg-border-color-medium-white-transparent { border-color: rgba(255,255,255,0.4); }
    .lg-border-color-full-dark-white-transparent { border-color: rgba(255,255,255,0.05); }
    .lg-border-color-light-white-transparent { border-color: rgba(255,255,255,0.1); }
    .lg-border-color-nero-transparent { border-color: rgba(25,25,25,0.1); }
    .lg-border-color-extra-medium-gray-transparent { border-color: rgba(219,219,219,.04); }

    /* border style */
    .lg-border-dotted { border-style: dotted !important; }
    .lg-border-dashed { border-style: dashed !important; }
    .lg-border-solid { border-style: solid !important; }
    .lg-border-double { border-style: double !important; }
    .lg-border-groove { border-style: groove !important; }
    .lg-border-ridge { border-style: ridge !important; }
    .lg-border-inset { border-style: inset !important; }
    .lg-border-outset { border-style: outset !important; }
    .lg-border-none { border-style: none !important; }
    .lg-border-hidden { border-style: hidden !important; }
    .lg-border-transperent { border-color: transparent !important; }

    /* box layout */
    .box-layout { padding:0; }
    .box-layout-large { padding:0; }

    /* navigation */
    .nav-item.dropdown.megamenu .menu-back-div { padding: 35px 50px 40px; }
    .nav-item.dropdown.megamenu .menu-back-div .col, .nav-item.dropdown.megamenu .menu-back-div ul[class *="d-"] { padding-right: 75px; }
    .nav-item.dropdown.megamenu .menu-back-div .col:last-child, .nav-item.dropdown.megamenu .menu-back-div ul[class *="d-"]:last-child { padding-right: 50px; }

    /* slider navigation style 02 */
    .slider-navigation-style-02.swiper-button-prev { left: -15px;}
    .slider-navigation-style-02.swiper-button-next { right: -15px;}

    /* slider navigation style 04 */
    .slider-navigation-style-04.swiper-button-prev { left: -20%; }
    .slider-navigation-style-04.swiper-button-next { right: -20%; }

    /* slider navigation style 07 */
    .slider-navigation-style-07.swiper-button-prev.light { left: -5px;}
    .slider-navigation-style-07.swiper-button-next.light { right: -5px;}

    /* grid */
    .grid.lg-grid-6col li { width: 16.67%; }
    .grid.lg-grid-6col li.grid-item-double { width: 33.33%; }
    .grid.lg-grid-5col li { width: 20%; }
    .grid.lg-grid-5col li.grid-item-double { width: 40%; }
    .grid.lg-grid-4col li { width: 25%; }
    .grid.lg-grid-4col li.grid-item-double { width: 50%; }
    .grid.lg-grid-3col li { width: 33.33%; }
    .grid.lg-grid-3col li.grid-item-double { width: 66.67%; }
    .grid.lg-grid-2col li { width: 50%; }
    .grid.lg-grid-2col li.grid-item-double { width: 100%; }
    .grid.lg-grid-1col li { width: 100%; }

    /* portfolio scattered */
    .portfolio-scattered.row-cols-xl-4 .col:nth-child(4n+0) .portfolio-box, .portfolio-scattered.row-cols-xl-4 .col:nth-child(4n+1) .portfolio-box { padding: 15% 0; }
    .portfolio-scattered.row-cols-xl-4 .col:nth-child(4n+2) .portfolio-box, .portfolio-scattered.row-cols-xl-4 .col:nth-child(4n+3) .portfolio-box { padding: 0 15%; }
    .portfolio-scattered.row-cols-xl-4 .col:nth-child(8n+0) .portfolio-box, .portfolio-scattered.row-cols-xl-4 .col:nth-child(8n+1) .portfolio-box, .portfolio-scattered.row-cols-xl-4 .col:nth-child(8n+3) .portfolio-box, .portfolio-scattered.row-cols-xl-4 .col:nth-child(8n+6) .portfolio-box{ padding: 15% 0; }
    .portfolio-scattered.row-cols-xl-4 .col:nth-child(8n+2) .portfolio-box, .portfolio-scattered.row-cols-xl-4 .col:nth-child(8n+4) .portfolio-box, .portfolio-scattered.row-cols-xl-4 .col:nth-child(8n+5) .portfolio-box, .portfolio-scattered.row-cols-xl-4 .col:nth-child(8n+7) .portfolio-box { padding: 0 15% 15%; }

    /* justified gallery */
    .justified-gallery > a > .caption, .justified-gallery > div > .caption, .justified-gallery > figure > .caption { bottom: 10px !important; left: 10px !important; right: 10px; box-shadow: none; white-space: normal; top: initial !important; width: calc(100% - 20px); display: block !important;}

    /* table style 01 */
    .table-style-01 {  overflow-x: scroll; }
    .table-style-01 table { width: 900px; }

    /* blog modern */
    .blog-modern .post-details { left: 20px; width: calc(100% - 40px); }

    /* sidebar latest post */
    .latest-post-sidebar li figure { width: 100px; }
    .latest-post-sidebar li div { padding-left: 15px; }

    /* architecture */
    .box-layout .navbar.navbar-boxed { padding-left: 0px; padding-right: 0px; }
    .home-architecture .navbar.bg-transparent, .home-architecture.sticky .header-dark.header-always-fixed-scroll { background-color: transparent !important; }
    .home-architecture .navbar-dark.bg-transparent .push-button > span, .home-architecture.sticky .navbar-dark.bg-transparent .push-button > span { background-color: #FFFFFF !important; }
    .home-architecture .tparrows.tp-rightarrow { transform: matrix(1, 0, 0, 1, -67, -240) !important; }
    .home-architecture .tparrows.tp-leftarrow { transform: matrix(1, 0, 0, 1, -67, -307) !important; }

    /* application */
    .banner-bottom-right-images > img { bottom: -65px; }

    /* digital agency */
    .home-digital-agency .outside-box-text-right .text-extra-big-2 { font-size: 200px; }

    /* marketing agency */
    footer.home-marketing-agency .footer-horizontal-link li { margin-right: 20px; }
    footer.home-marketing-agency .footer-horizontal-link li:last-child { margin-right: 0; }

    /* vertical portfolio */
    .home-vertical-portfolio .navbar { padding: 0 4.5rem;}

    /* interactive list style */
    .fullscreen-hover-list .hover-list-item .interactive-title { font-size: 70px; line-height: 70px; padding: 20px 35px; }
    .fullscreen-hover-list .hover-list-item .interactive-title:after { bottom: 30px; }
    .fullscreen-hover-list .hover-list-item.active .interactive-title:after { width: calc(100% - 60px); }

    /* content box image */
    .content-box-image { height: 225px; }

    /* landing page */
    .litho-parallax-bg { width: 620px; }
    .customer-bg-section { top: 80px; bottom: inherit; }
    .landing-page-auto-slider .swiper-container.swiper-auto-slide .swiper-slide { width: 40% !important; }

    /* for buy and demo button */
    .theme-demos { display: none !important; }
    
    /* magnific popup */
    button.mfp-close, .mfp-image-holder button.mfp-close, .mfp-iframe-holder button.mfp-close, .mfp-close:active { top: 20px; right: 20px; }
}

@media (max-width: 1024px) {
    /*main content*/
    .main-content { margin-bottom: 0 !important; position: inherit; }

    /* background image */
    .fix-background { background-attachment: unset !important; }

    /* swiper vertical */
    .slider-vertical .swiper-number-pagination { left: 73px; bottom: 115px; }

    /* home decor */
    .home-decor .zeus { transform: matrix(1, 0, 0, 1, -470, -59) !important; }
    .home-decor .collection-btn span{ width: 48px !important; height: 48px !important; top: 50% !important; transform: translateY(-50%) !important; }
    .home-decor .collection-btn:hover span{ width: 100% !important; height: 48px; }
    .home-decor .tp-bullet { height: 8px !important; width: 8px !important; }

    /* fashion shop */
    .home-fashion-shop .tp-tabs { left: 605px !important; }

    /* home-architecture */
    .home-architecture .tparrows.tp-rightarrow { transform: matrix(1, 0, 0, 1, -67, -240) !important; }
    .home-architecture .tparrows.tp-leftarrow { transform: matrix(1, 0, 0, 1, -67, -307) !important; }

    /* interactive list style */
    .fullscreen-hover-list .hover-list-item.active .interactive-icon { opacity: 1; visibility: visible; transition-delay: .8s; transition-duration: 1s;}


    /* scroll to top */
    .scroll-top-arrow { display: none !important}

    /*footer sticky*/
    .footer-sticky { position: relative !important; z-index: 0 !important; }

}

@media only screen and (max-width:1024px) and (min-width: 778px) {
    /* spa salon */
    .home-spa-salon .ares { top: 55.5% !important; }
}

/* Navigation breakpoint start */
@media (max-width: 991px) {
    .menu-list-wrapper.mCS_no_scrollbar .mCSB_inside>.mCSB_container .menu-list { padding-right: 0;}
    .menu-list-wrapper.mCS_no_scrollbar .mCSB_inside>.mCSB_container .menu-list-wrapper .menu-list { padding-right: 15px;}
    .navbar-expand-lg>.container, .navbar-expand-lg>.container-fluid, .navbar-expand-lg>.container-lg, .navbar-expand-lg>.container-md, .navbar-expand-lg>.container-sm, .navbar-expand-lg>.container-xl { padding-right: 15px; padding-left: 15px; }

    /* header default */
    .navbar-nav { padding:15px 15px 25px }
    .navbar-collapse { position: absolute; top: 100%; left: 0; width: 100%; background: #fff; overflow: hidden; box-shadow: 0 20px 15px 0 rgba(23,23,23,.05); max-height: calc(100vh - 65px);}
    .navbar-collapse.show {overflow-y: auto !important; -webkit-overflow-scrolling: touch;}
    .navbar.navbar-dark .navbar-nav .nav-link,.navbar.navbar-light .navbar-nav .nav-link, .sticky .navbar.navbar-dark .navbar-nav .nav-link, .sticky .navbar.navbar-light .navbar-nav .nav-link, header.sticky .navbar.top-logo .navbar-nav .nav-link { color: #000; padding: 9px 15px; margin: 0; }
    .navbar .navbar-nav .nav-link {font-size: 15px}
    .navbar.navbar-dark .navbar-nav > .dropdown.active > a, .sticky .navbar.header-dark .navbar-nav > .dropdown.active > a { color: rgba(0,0,0,0.6) !important; }
    .navbar.navbar-boxed { padding-left: 0; padding-right: 0; }
    .navbar-toggler { margin: 30px 0 28px 10px; transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -webkit-transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out;}
    .sticky .navbar.responsive-sticky .navbar-toggler { margin-top: 21px; margin-bottom: 20px; }
    .dropdown-toggle:after { display: none;}
    .menu-order { order: 5; position: inherit !important }
    .logo-order { order: 5}
    .navbar-collapse-show .sticky .header-reverse-scroll { -webkit-transform: translateY(0); -moz-transform: translateY(0); -ms-transform: translateY(0); -o-transform: translateY(0); transform: translateY(0); -webkit-transition-duration: 0.3s;}

    /* top bar */
    .top-bar-contact .top-bar-contact-list:last-child {border-right: none; padding-right: 0}

    /* center logo */
    .menu-logo-center .menu-logo { position: relative; left: auto; text-align: left; margin: 0; transform: translateY(0); -webkit-transform: translateY(0); -moz-transform: translateY(0); -ms-transform: translateY(0); -o-transform: translateY(0); }
    .menu-logo-center .navbar-brand { padding: 18px 0;}
    .navbar-left, .navbar-right { width: 100%; }
    .sticky .navbar.header-dark .navbar-nav .nav-link { color: #000 !important; }
    .navbar-nav .nav-item:last-child .menu-back-div, .navbar-nav .nav-item:last-child .menu-back-div ul:last-child { padding-bottom: 0 !important; margin-bottom: 0 !important; }
    .nav-item.dropdown.megamenu , .nav-item.dropdown.simple-dropdown { position: relative; }
    .nav-item.dropdown.megamenu .menu-back-div { display: none; }
    .nav-item.dropdown.megamenu.show .menu-back-div { display: block; }
    .nav-item.dropdown.megamenu .menu-back-div, .nav-item.dropdown.simple-dropdown .dropdown-menu { width: 100% !important; position: relative; box-shadow: none; padding:0 15px 20px; top: 0; border-radius: 0; background-color: transparent; }
    .nav-item.dropdown.megamenu .menu-back-div .col, .nav-item.dropdown.megamenu .menu-back-div [class *="col-"] { padding: 0; margin-bottom: 8px; }
    .nav-item.dropdown.megamenu .menu-back-div .col, .nav-item.dropdown.megamenu .menu-back-div ul[class *="d-"] { padding: 15px 0 0 20px; }
    .nav-item.dropdown.megamenu .menu-back-div .col:last-child, .nav-item.dropdown.megamenu .menu-back-div ul[class *="d-"]:last-child { padding-right: 0;}
    .nav-item.dropdown.megamenu .menu-back-div ul.small-gap[class *="d-"] { padding-right: 0;}
    .dropdown-menu.megamenu-content li.dropdown-header { margin: 23px 0 10px 0; font-size: 14px; font-weight: 500; }
    .dropdown-menu.megamenu-content li.dropdown-header:first-child { margin-top: 10px;}
    .nav-item.dropdown.megamenu .menu-back-div [class *="col-"] li.dropdown-header:first-child,
    .nav-item.dropdown.megamenu .menu-back-div ul[class *="d-"]:first-child li.dropdown-header:first-child { margin-top: 0;}
    .nav-item.dropdown.simple-dropdown .dropdown-menu .dropdown .dropdown-menu { margin:0 0 5px; left: 0; top: 0;}
    .nav-item.dropdown.simple-dropdown .dropdown-menu .dropdown > a { margin-bottom: 5px;}
    .nav-item > .dropdown-toggle { display: block; width: 48px; height: 48px; right: 0px; position: absolute; top: 0; text-align: center; line-height: 50px; }
    .nav-item.show > .dropdown-toggle { -ms-transform: rotate(-180deg); -webkit-transform: rotate(-180deg); transform: rotate(-180deg); }
    .nav-item.dropdown.simple-dropdown .dropdown-menu { position: relative; width: 100%; box-shadow: none; display: none; top: 0; }
    .nav-item.dropdown.simple-dropdown .dropdown-menu .dropdown-menu { padding: 0;}
    .nav-item.dropdown.simple-dropdown.show .dropdown-menu { display: block; }
    .nav-item.dropdown.simple-dropdown .dropdown-menu .dropdown a { padding: 3px 15px 3px; font-size: 14px; margin-bottom: 5px; }
    .nav-item.dropdown.simple-dropdown .dropdown-menu > .dropdown > a { color: #000; margin: 15px 0 7px 0; font-size: 14px; font-weight: 500; }
    .megamenu-dropdown-dark { background-color: #232323; }
    .navbar.navbar-light .navbar-nav.megamenu-dropdown-dark .nav-item.dropdown > a { color: #fff !important; }
    .navbar.navbar-light .navbar-nav.megamenu-dropdown-dark .nav-item.dropdown.active > a { color: rgba(255,255,255,0.6) !important; }   
    .navbar.navbar-light .navbar-nav.megamenu-dropdown-dark .nav-item.dropdown:hover > a, .navbar.navbar-light .navbar-nav.megamenu-dropdown-dark .nav-item.dropdown.megamenu:hover > a, .navbar.navbar-light .navbar-nav.megamenu-dropdown-dark .nav-item.dropdown.simple-dropdown:hover > a, .navbar.navbar-light .navbar-nav.megamenu-dropdown-dark .nav-item.dropdown > a:hover { color: #fff !important; }
    .megamenu-dropdown-dark .dropdown-menu.megamenu-content li.active a { color: rgba(255,255,255,0.3)!important; }
    .megamenu-dropdown-dark .nav-item.dropdown.simple-dropdown .dropdown-menu > .dropdown > a { color: #fff; }
    .megamenu-dropdown-dark .nav-item.dropdown.simple-dropdown > .dropdown-menu { padding-top: 15px; }
    .megamenu-dropdown-dark .nav-item.dropdown.simple-dropdown .dropdown-menu > .dropdown > a { margin-top: 0; }
    .dropdown-menu.megamenu-content li a, .dropdown-menu.megamenu-content li.dropdown-header { line-height: normal}
    .nav-item.dropdown.simple-dropdown .dropdown-menu > .dropdown { margin-bottom: 20px;}
    .nav-item.dropdown.simple-dropdown .dropdown-menu > .dropdown:last-child { margin-bottom: 0;}
    .simple-dropdown .dropdown-menu li { padding: 0; }
    .simple-dropdown .dropdown-menu .dropdown a .dropdown-toggle { display: none;}
    .dropdown-menu.megamenu-content li { padding-bottom: 5px;}
    .dropdown-menu.megamenu-content li a { padding-top: 3px; font-size: 14px}

    /* header search form */
    .form-wrapper .search-form-box { width: 60%; }

    /* header sidebar */
    .sidebar-wrapper, .page-wrapper { padding-left: 0; }
    .sidebar-nav-action { width: 100%; height: auto; padding: 15px 30px; }
    .sidebar-nav-action-main { flex-flow: row; align-items: center; }
    .side-menu-header  { position: fixed; left: 0; top: 0; text-align: left !important; width: 100%; background-color: #fff; border-bottom: 1px solid #f3f3f3; padding: 15px 30px; z-index: 9; }
    .side-menu-header .navbar-brand { z-index: 1; position: relative; }
    .side-menu-header-bottom, header.side-menu-nav { left:-290px; height: 100%; top: 0; padding-top: 60px; -webkit-box-align: start; -ms-flex-align: start; align-items: start; -webkit-transition-duration: 0.3s; -moz-transition-duration: 0.3s; -ms-ransition-duration: 0.3s; -o-transition-duration: 0.3s; transition-duration: 0.3s; }
    .side-menu-header-bottom { padding-top: 0; }
    .show-menu .side-menu-header-bottom, .show-menu header.side-menu-nav { left: 0; }
    .side-menu-header .side-menu-button { display: inline-block; right: 30px; }
    .left-sidebar-wrapper{ padding-left: 0; }
    .sidebar-nav-menu.left-sidebar-nav { left: -290px; overflow: visible; height: 100%; top: 0 !important;}    
    .sidebar-nav-menu.left-sidebar-nav .side-menu-header-bottom { height: 100%; overflow: auto;}
    .show-menu .sidebar-nav-menu.left-sidebar-nav { left: 0}
    .menu-toggle { top: 20px; }
    .menu-list li a { line-height: 40px; padding: 0; }
    .side-menu-button a { top: 1px; }
    .side-menu .sidebar-nav-menu .menu-list-wrapper { padding-bottom: 20px; }
    .sidebar-social-icon li { display: inline-block; margin: 0; padding: 0 7px; }
    .sidebar-social-icon li:last-child { padding-right: 0; }
    .navbar-left-sidebar,.navbar-left-sidebar .dropdown:hover > .dropdown-menu { width: auto; }
    .show-menu .menu-style-2 .sidebar-nav-menu { left: 0; }
    .menu-style-2 .sidebar-nav-menu .menu-list li { padding: 5px 0;}
    .menu-style-2 .sidebar-nav-menu .menu-list li ul li { padding-top: 0; padding-bottom: 0;}
    .menu-style-2 .sidebar-nav-menu .menu-list li .menu-toggle { top: 27px; }
    .menu-style-2 .sidebar-nav-menu .menu-list li ul li .menu-toggle { top: 15px; }

    /* sidebar nav menu */
    .navbar-left-sidebar > li > a { font-size: 14px; line-height: 20px; }
    .navbar-left-sidebar > li.dropdown > a > i { display: block; position: absolute; top: 15px; right: 5px; font-size: 16px; }
    .navbar-left-sidebar li a { padding: 10px 0; margin: 0; border-bottom: 0; font-size: 14px; line-height: 20px; }
    .menu-style-2 .navbar-left-sidebar .dropdown:hover > .dropdown-menu.second-level { left: 0; position: relative; background-color: #fff; }
    .navbar-left-sidebar .dropdown > .dropdown-menu.second-level li > a { border-bottom: 0; padding: 6px 0 5px 0 }
    .navbar-left-sidebar .dropdown-menu.second-level li > a:hover, .navbar-left-sidebar li > a:hover { background-color: transparent; }
    .top-logo .navbar-brand { padding-top: .3125rem; padding-bottom: .3125rem; }

    /* hamburger menu half */
    .hamburger-menu-half .menu-list > li { padding: 7px 0; }
    .hamburger-menu-half .menu-list li .menu-toggle { top: 29px; }
    .hamburger-menu-half .menu-list li ul li .menu-toggle { top: 15px; }
    .hamburger-menu-half .menu-list .menu-list-item > .sub-menu-item { padding-top: 5px;}
    .hamburger-menu-half .menu-list .menu-list-item .sub-menu-item .sub-menu-item { padding-top: 0}
    .hamburger-menu-half .sub-menu-item .menu-toggle:before, .hamburger-menu-half .sub-menu-item .menu-toggle:after { right: 8px; }

    /* hamburger menu full width */
    .hamburger-menu.full-width .menu-list > li { padding: 5px 0; }
    .hamburger-menu.full-width .menu-list > li .menu-toggle { top: 27px; }
    .hamburger-menu.full-width .menu-list li ul li .menu-toggle { top: 15px; }
    .hamburger-menu.full-width .menu-list .menu-list-item > .sub-menu-item { padding-top: 5px;}
    .hamburger-menu.full-width .menu-list .menu-list-item .sub-menu-item .sub-menu-item { padding-top: 0}

    /* center logo */
    .menu-logo-center .navbar-collapse > .navbar-nav:first-child { padding-bottom: 0;}
    .menu-logo-center .navbar-collapse > .navbar-nav:last-child { padding-top: 0;}

    /* hamburger menu big text */
    .hamburger-menu-big-font .menu-list li .sub-menu-item { padding: 20px 0 0 15px; }
    .hamburger-menu-big-font .menu-list li .menu-toggle { top: 34px; }
    .hamburger-menu-big-font .menu-list li a { font-size: 30px; line-height: 38px; }

    /* header navigation white */
    .navbar.navbar-dark.bg-transparent .navbar-nav .nav-link.active, .navbar.navbar-dark .navbar-nav .nav-link.active, .navbar-dark .navbar-nav .nav-link.active:hover,.navbar-dark .navbar-nav .nav-link.active:focus { color: rgba(0,0,0,.6) !important; }
    .navbar.navbar-dark .navbar-nav > .dropdown.active > a, .sticky .navbar.header-dark .navbar-nav > .dropdown.active > a, .navbar.navbar-dark .navbar-nav > .nav-item.dropdown.megamenu.active:hover > a, .navbar.navbar-dark .navbar-nav > .nav-item.dropdown.simple-dropdown.active:hover > a { color: rgba(0,0,0,0.6) !important; }
    .navbar.navbar-dark .navbar-nav > .nav-item.dropdown.megamenu:hover > a, .navbar.navbar-dark .navbar-nav > .nav-item.dropdown.simple-dropdown:hover > a { color: rgba(0,0,0,1) !important; }
    .navbar.navbar-light .navbar-nav > .nav-item.dropdown.megamenu:hover > a, .navbar.navbar-light .navbar-nav > .nav-item.dropdown.simple-dropdown:hover > a, .sticky .navbar.navbar-dark.header-light .navbar-nav > .nav-item.dropdown.megamenu:hover > a, .sticky .navbar.navbar-dark.header-light .navbar-nav > .nav-item.dropdown.simple-dropdown:hover > a { color: rgba(0,0,0,1) !important; }

    /* header logo */
    header .navbar .navbar-brand .default-logo, header.sticky .navbar-brand .alt-logo, header.sticky .navbar .navbar-brand .default-logo, header .navbar-brand .alt-logo, header .sidebar-nav-menu .navbar-brand .default-logo { visibility: hidden; opacity: 0; width: 0; }
    header .navbar .navbar-brand .mobile-logo, header .sidebar-nav-menu .navbar-brand .mobile-logo  { visibility: visible; opacity: 1; width: auto; }

    /* header with top logo */
    .top-logo .navbar-brand + div[class*="col-"] div:first-child { padding-left: 0;}

    /* header toggle button */
    .navbar-dark.bg-transparent .navbar-toggler-line { overflow: hidden; background: #232323; }
    header.sticky .navbar.bg-transparent.hader-light .navbar-toggler-line { overflow: hidden; background: #fff; }
    .navbar-dark.bg-transparent .push-button > span, .sticky .navbar-dark.bg-transparent .push-button > span { background: #232323; }

    /* header transparent */
    .navbar.bg-transparent { background-color: #fff!important; }
    .navbar.navbar-dark .header-social-icon, .sticky .navbar.header-dark .header-social-icon { border-color: rgba(0,0,0,.45); }
    .navbar.navbar-dark.bg-transparent .navbar-nav .nav-link, .navbar.navbar-dark.bg-transparent .header-search-icon > a, .navbar.navbar-dark.bg-transparent .header-social-icon > a, .navbar.navbar-dark.bg-transparent .header-cart-icon > a, .navbar.navbar-dark.bg-transparent .header-language > a, .navbar.navbar-dark .nav-bar-contact { color: #232323 !important;}
    header.sticky .navbar.navbar-dark.bg-transparent.header-dark .header-social-icon > a { color: #fff !important;}
    .navbar.navbar-dark.bg-transparent .header-search-icon > a:hover, .navbar.navbar-dark.bg-transparent .header-social-icon > a:hover, .navbar.navbar-dark.bg-transparent .header-cart-icon > a:hover, .navbar.navbar-dark.bg-transparent .header-language > a:hover, .sticky .navbar-dark.bg-transparent .header-search-icon > a:hover, .sticky .navbar-dark.bg-transparent .header-social-icon > a:hover, .sticky .navbar-dark.bg-transparent .header-cart-icon > a:hover, .sticky .navbar-dark.bg-transparent .header-language > a:hover { color: rgba(0,0,0,.6) !important;}
    .navbar.bg-transparent .header-button .btn.btn-white { background: #232323; border-color: #232323; color: #ffffff;}

    /* menu classic */
    [data-mobile-nav-style=classic] .navbar-nav { display: block;}

    /* menu modern */  
    [data-mobile-nav-style=modern] .page-layout { background-color: #fff;}
    [data-mobile-nav-style=modern] header .navbar-collapse { display: none !important; }
    [data-mobile-nav-style=modern] .navbar-modern-inner .navbar-nav { width: 100%; padding: 0; }
    [data-mobile-nav-style=modern] .navbar-modern-inner .navbar-collapse.show { height: 100%;  }
    [data-mobile-nav-style=modern] .navbar-modern-inner .navbar-toggler-line { background-color: #fff; }
    [data-mobile-nav-style=modern] .navbar-modern-inner .nav-item a, [data-mobile-nav-style=modern] .navbar-modern-inner .nav-item i, [data-mobile-nav-style=modern] .navbar-modern-inner .simple-dropdown .dropdown-menu .dropdown a.active, [data-mobile-nav-style=modern] .navbar-modern-inner .nav-item.dropdown.simple-dropdown .dropdown-menu > .dropdown > a { color: #fff; display: inline-block; right: 0; font-size: 17px; font-weight: 500;}
    [data-mobile-nav-style=modern] .navbar-modern-inner .simple-dropdown .dropdown-menu .dropdown a.active { text-decoration: underline;}
    [data-mobile-nav-style=modern] .navbar-modern-inner .nav-item i { font-size: 14px;}
    [data-mobile-nav-style=modern] .navbar-modern-inner .nav-item i.dropdown-toggle { font-size: 17px; font-weight: 600;}
    [data-mobile-nav-style=modern] .navbar-modern-inner .nav-item .megamenu-content a, [data-mobile-nav-style=modern] .navbar-modern-inner .nav-item.dropdown.simple-dropdown .dropdown-menu > .dropdown > a { font-size: 13px; padding: 0}
    [data-mobile-nav-style=modern] .navbar-modern-inner .nav-item.dropdown.megamenu .menu-back-div, [data-mobile-nav-style=modern] .dropdown-menu.megamenu-content li.dropdown-header { color: #fff; position: inherit !important; margin-bottom: 15px !important; margin-top: 6px; padding: 0 !important; right: 0; }
    [data-mobile-nav-style=modern] .dropdown-menu.megamenu-content li.dropdown-header {opacity: .7; margin-top: 20px !important; font-weight: 500 }
    [data-mobile-nav-style=modern] .dropdown-menu.megamenu-content li.dropdown-header:first-child { margin-top: 0 !important}
    [data-mobile-nav-style=modern] .navbar-modern-inner .nav-item.dropdown.megamenu .menu-back-div ul { margin-bottom: 20px; padding: 0;}
    [data-mobile-nav-style=modern] .navbar-modern-inner .nav-item.dropdown.megamenu .menu-back-div ul:last-child { margin-bottom: 0;}
    [data-mobile-nav-style=modern] .navbar-modern-inner .dropdown-menu.megamenu-content li, [data-mobile-nav-style=modern] .navbar-modern-inner .nav-item.dropdown.simple-dropdown .dropdown-menu .dropdown .dropdown-menu {line-height: normal; padding-bottom: 5px; font-size: 15px}
    [data-mobile-nav-style=modern] .navbar-modern-inner .nav-item.dropdown.megamenu .menu-back-div, [data-mobile-nav-style=modern] .navbar-modern-inner .simple-dropdown > .dropdown-menu { border-radius: 0; background-color: transparent; transform: translate3d(0, 0, 0px) !important; position: inherit !important; padding: 8px 15px !important; margin-bottom: 0 !important }
    [data-mobile-nav-style=modern] .navbar-modern-inner .nav-item.dropdown.simple-dropdown .dropdown-menu > .dropdown { margin-bottom: 0; }
    [data-mobile-nav-style=modern] .navbar-modern-inner .simple-dropdown > .dropdown-menu { padding-top: 14px !important; padding-bottom: 0 !important; }
    [data-mobile-nav-style=modern] .navbar-modern-inner .simple-dropdown > .dropdown-menu li:last-child > ul { margin-bottom: 0 !important;}
    [data-mobile-nav-style=modern] .navbar-modern-inner .nav-item.dropdown.simple-dropdown .dropdown-menu .dropdown .dropdown-menu li { padding: 0; }
    [data-mobile-nav-style=modern] .navbar-modern-inner .nav-item.dropdown.simple-dropdown .dropdown-menu .dropdown .dropdown-menu { margin-bottom: 10px;}
    [data-mobile-nav-style=modern] .navbar-modern-inner .nav-item.dropdown.simple-dropdown .dropdown-menu .dropdown > a { opacity: .7; font-size: 14px; margin-bottom: 10px; margin-top: 0; }
    [data-mobile-nav-style=modern] .navbar-modern-inner .nav-item.dropdown.simple-dropdown .dropdown-menu .dropdown .dropdown-menu li > a { margin-bottom: 9px; font-size: 13px;}
    [data-mobile-nav-style=modern] .navbar-modern-inner .simple-dropdown .dropdown-menu .dropdown:hover > a, [data-mobile-nav-style=modern] .navbar-modern-inner .simple-dropdown .dropdown-menu .dropdown a:hover, [data-mobile-nav-style=modern] .navbar-modern-inner .simple-dropdown .dropdown-menu .dropdown a.active, [data-mobile-nav-style=modern] .navbar-modern-inner .simple-dropdown .dropdown-menu .dropdown a:focus, [data-mobile-nav-style=modern] .navbar-modern-inner .simple-dropdown .dropdown-menu .dropdown.active > a { color: rgba(255,255,255,0.6); }
    [data-mobile-nav-style=modern] .navbar-modern-inner .mCustomScrollBox { height: auto; width: 100%;}
    [data-mobile-nav-style=modern] .navbar-modern-inner .mCSB_inside>.mCSB_container { margin-right: 0; }
    [data-mobile-nav-style=modern] .navbar-modern-inner .mCSB_container.mCS_no_scrollbar_y.mCS_y_hidden { margin-right: 0; }
    [data-mobile-nav-style=modern] .navbar-modern-inner .nav-item.dropdown.simple-dropdown .dropdown-menu .dropdown a { padding: 0;}
    [data-mobile-nav-style=modern] .navbar-modern-inner .simple-dropdown .dropdown-menu .dropdown a .dropdown-toggle { display: none; right: 13px; top: 4px; transform: translateY(0); -webkit-transform: translateY(0); -moz-transform: translateY(0); -o-transform: translateY(0); -ms-transform: translateY(0); }
    [data-mobile-nav-style=modern] .navbar-modern-inner .dropdown-menu.megamenu-content li.active a, [data-mobile-nav-style=modern] .navbar-modern-inner .dropdown-menu.megamenu-content li a:hover { color: rgba(255,255,255,0.6); }
    .navbar-collapse-show[data-mobile-nav-style=modern] { overflow: hidden; padding-top: 0;}
    [data-mobile-nav-style=modern] .navbar-modern-inner { opacity: 0; visibility: hidden; overflow: visible !important; width: 70vw; height: 100vh !important; position: fixed; top: 0; right: -40vw; z-index: 90; display: -ms-flexbox !important; display: -webkit-box !important; display: flex !important; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; -webkit-transition-duration: 0.65s; transition-duration: 0.65s; -webkit-transition-timing-function: cubic-bezier(0.23, 1, 0.32, 1); transition-timing-function: cubic-bezier(0.23, 1, 0.32, 1); -webkit-transform: translate3d(25vw, 0, 0); transform: translate3d(25vw, 0, 0); }
    .navbar-collapse-show[data-mobile-nav-style=modern] .navbar-modern-inner { right: 0; opacity: 1; visibility: visible !important; display: -ms-flexbox !important; display: -webkit-box !important; display: flex !important; -webkit-transition-delay: 0.1s; transition-delay: 0.1s; -webkit-transform: translate3d(0, 0, 0); transform: translate3d(0, 0, 0); }
    [data-mobile-nav-style=modern] .navbar-show-modern-bg { display: inline-block; width: 100vw; height: 100vh; position: fixed; top: 0; left: 0; z-index: -1; opacity: 0; background-image: linear-gradient(to right top, #0039e3, #4132e0, #5e28dd, #741bd9, #8600d4); -webkit-transform: scale(1.75); transform: scale(1.75); transition: opacity .3s,-webkit-transform .3s; transition: opacity .3s,transform .3s; transition: opacity .3s,transform .3s,-webkit-transform .3s; -webkit-transition-delay: 0.4s; -o-transition-delay: 0.4s; transition-delay: 0.4s; }
    .navbar-collapse-show[data-mobile-nav-style=modern] .navbar-show-modern-bg { -webkit-transform: scale(1); transform: scale(1); opacity: 1; -webkit-transition-delay: 0s; -o-transition-delay: 0s; transition-delay: 0s; }
    [data-mobile-nav-style=modern] .navbar, [data-mobile-nav-style=modern] .sticky.header-appear .header-reverse-scroll, [data-mobile-nav-style=modern] header .top-bar + .navbar.fixed-top { -webkit-transition-duration: 0.75s; -moz-transition-duration: 0.75s; -ms-transition-duration: 0.75s; -o-transition-duration: 0.75s; transition-duration: 0.75s;}
    [data-mobile-nav-style=modern] .navbar, [data-mobile-nav-style=modern] .page-layout, [data-mobile-nav-style=modern] .top-bar { -webkit-transition: width 0.3s, -webkit-transform 0.75s cubic-bezier(0.23, 1, 0.32, 1); transition: width 0.3s, -webkit-transform 0.75s cubic-bezier(0.23, 1, 0.32, 1); transition: transform 0.75s cubic-bezier(0.23, 1, 0.32, 1), width 0.3s; transition: transform 0.75s cubic-bezier(0.23, 1, 0.32, 1), width 0.3s, -webkit-transform 0.75s cubic-bezier(0.23, 1, 0.32, 1);}
    .navbar-collapse-show[data-mobile-nav-style=modern] .navbar, .navbar-collapse-show[data-mobile-nav-style=modern] .page-layout, .navbar-collapse-show[data-mobile-nav-style=modern] .top-bar { -webkit-transform: translate3d(-70vw, 0, 0); transform: translate3d(-70vw, 0, 0); }
    [data-mobile-nav-trigger-alignment=right][data-mobile-nav-style=modern] .navbar-modern-inner .navbar-toggler { display: none; }
    [data-mobile-nav-trigger-alignment=left][data-mobile-nav-style=modern] .navbar-modern-inner .navbar-toggler { position: absolute; top: 0; right: 25px; }
    [data-mobile-nav-style=modern] .navbar-modern-inner .navbar-collapse { position: static; left: 0; top: 0; width: 100%; height: 100%; background: transparent; padding: 100px 12vw; box-shadow: none; max-height: 100%; display:flex !important; -ms-flex-pack: center!important; justify-content: center!important; }
    .navbar-collapse-show[data-mobile-nav-trigger-alignment=left][data-mobile-nav-style=modern] .navbar-modern-inner .navbar-toggler .navbar-collapse-show[data-mobile-nav-trigger-alignment=left][data-mobile-nav-style=modern] .navbar { position: absolute; }
    .navbar-collapse-show[data-mobile-nav-trigger-alignment=left][data-mobile-nav-style=modern] .navbar, .navbar-collapse-show[data-mobile-nav-trigger-alignment=left][data-mobile-nav-style=modern] .page-layout, .navbar-collapse-show[data-mobile-nav-trigger-alignment=left][data-mobile-nav-style=modern] .top-bar { -webkit-transform: translate3d(80vw, 0, 0); transform: translate3d(80vw, 0, 0); }
    [data-mobile-nav-trigger-alignment=left] .navbar-modern-inner { width: 80vw; right: inherit; left: -30vw; -webkit-transform: translate3d(-25vw, 0, 0); transform: translate3d(-25vw, 0, 0); }
    .navbar-collapse-show[data-mobile-nav-trigger-alignment=left] .navbar-modern-inner { left: 0; right: inherit; }
    [data-mobile-nav-trigger-alignment=left] .navbar-modern-inner .navbar-collapse {  right: 0; left: inherit; padding-right: 10vw; padding-left: 10vw; }
    [data-mobile-nav-trigger-alignment=left][data-mobile-nav-style=modern] .parallax {  background-attachment: scroll !important;}
    [data-mobile-nav-style=modern] .navbar-nav > .nav-item { border-bottom: 1px solid rgba(255,255,255,.1); padding-top: 10px; padding-bottom: 12px;}
    [data-mobile-nav-style=modern] .navbar-nav > .nav-item:last-child { border-bottom: 0; }
    [data-mobile-nav-style=modern] .nav-item > .dropdown-toggle { top: 7px; }

    /* full-screen-menu */
    .navbar-collapse-show[data-mobile-nav-style=full-screen-menu] { overflow: hidden;}
    .navbar-collapse-show[data-mobile-nav-style=full-screen-menu] .navbar .navbar-nav { padding: 0;}
    [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner { background-image: linear-gradient(to right top, #0039e3, #4132e0, #5e28dd, #741bd9, #8600d4); visibility: hidden; overflow: hidden !important; width: 100vw; height: 100vh !important; position: fixed; top: -100vh; left: 0; z-index: 9999; display: -ms-flexbox !important; display: -webkit-box !important; display: flex !important; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; -webkit-transition: all 0.4s ease-ou; transition: all 0.4s ease-out; -webkit-transition-delay: 0.6s; transition-delay: 0.6s;}
    .navbar-collapse-show[data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner { height: 100vh !important; top: 0; visibility: visible !important; -webkit-transition: all 0.2s ease-in; transition: all 0.2s ease-in; -webkit-transition-delay: 0.20s; transition-delay: 0.20s;}
    [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .navbar-collapse { position: inherit; left: 0; top:0; width: 100%; height: 100%; padding: 100px 0; max-height: 100%; box-shadow: none; background: transparent; display: -ms-flexbox !important; display: -webkit-box !important; display: flex !important; -ms-flex-pack: center!important; justify-content: center!important; }
    [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .navbar-nav { padding: 0; }
    [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .nav-item.dropdown.megamenu .menu-back-div, [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .simple-dropdown > .dropdown-menu { border-radius: 0; background-color: transparent; transform: translate3d(0, 0, 0px) !important; position: inherit !important; padding: 8px 15px !important; margin-bottom: 0 !important; margin-top: 6px; }
    [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .mCustomScrollBox { height: auto; width: 75%;}    
    [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .nav-item a, [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .nav-item i, [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .nav-item.dropdown.simple-dropdown .dropdown-menu > .dropdown > a { color: #fff; font-size: 17px; font-weight: 500;}
    [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .nav-item i { font-weight: 600}
    [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .nav-item.dropdown.megamenu .menu-back-div, [data-mobile-nav-style=full-screen-menu] .dropdown-menu.megamenu-content li.dropdown-header { color: #fff; line-height: normal; padding-bottom: 5px; font-size: 15px;  }
    [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .dropdown-menu.megamenu-content li.active a, [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .dropdown-menu.megamenu-content li a:hover { color: rgba(255,255,255,0.6); }
    [data-mobile-nav-style=full-screen-menu] .dropdown-menu.megamenu-content li.dropdown-header { opacity: .7; margin-top: 20px !important; font-weight: 500; margin-bottom: 4px; }
    [data-mobile-nav-style=full-screen-menu] .dropdown-menu.megamenu-content li.dropdown-header:first-child { margin-top: 0 !important}
    [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .nav-item.dropdown.megamenu .menu-back-div ul { margin-bottom: 20px; padding: 0;}
    [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .nav-item.dropdown.megamenu .menu-back-div ul:last-child { margin-bottom: 0;}
    [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .dropdown-menu.megamenu-content li, [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .nav-item.dropdown.simple-dropdown .dropdown-menu .dropdown .dropdown-menu { line-height: normal; padding-bottom: 12px; font-size: 15px; }
    [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .nav-item .megamenu-content a, [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .nav-item.dropdown.simple-dropdown .dropdown-menu > .dropdown > a { font-size: 13px; padding: 0; }
    [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .simple-dropdown > .dropdown-menu { padding-bottom: 0 !important; }
    [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .nav-item.dropdown.simple-dropdown .dropdown-menu > .dropdown { margin-bottom: 0; }
    [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .nav-item.dropdown.simple-dropdown .dropdown-menu .dropdown a { padding: 3px 0 3px; font-size: 13px; margin-bottom: 6px; margin-top: 0; }
    [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .nav-item.dropdown.simple-dropdown .dropdown-menu .dropdown > a { opacity: .7; margin-bottom: 7px !important; font-size: 14px; padding-top: 2px; }
    [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .simple-dropdown > .dropdown-menu li:last-child > ul { margin-bottom: 0 !important; padding-bottom: 5px !important; }
    [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .simple-dropdown .dropdown-menu .dropdown:hover > a, [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .simple-dropdown .dropdown-menu .dropdown a:hover, [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .simple-dropdown .dropdown-menu .dropdown a.active, [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .simple-dropdown .dropdown-menu .dropdown a:focus, [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .simple-dropdown .dropdown-menu .dropdown.active > a { color: rgba(255,255,255,0.6); }
    [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .navbar-toggler { margin: 0; position: absolute; right: 35px; top: 35px; opacity: 0; -webkit-transition: all 0.4s ease-ou; transition: all 0.4s ease-out; -webkit-transition-delay: 0.6s; transition-delay: 0.6s;}
    .navbar-collapse-show[data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .navbar-toggler { opacity: 1}
    [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .navbar-toggler-line { background-color: #fff; }
    [data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li.nav-item > .dropdown-toggle { top: 8px; right: 0; }
    [data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li.nav-item { border-bottom: 1px solid rgba(255,255,255,.1); padding-top: 10px; padding-bottom: 12px; -webkit-transform: scale(1.15) translateY(-30px); transform: scale(1.15) translateY(-30px); opacity: 0; -webkit-transition: opacity 0.5s cubic-bezier(0.4, 0.01, 0.165, 0.99), -webkit-transform 0.5s cubic-bezier(0.4, 0.01, 0.165, 0.99); transition: opacity 0.6s cubic-bezier(0.4, 0.01, 0.165, 0.99), -webkit-transform 0.5s cubic-bezier(0.4, 0.01, 0.165, 0.99); transition: transform 0.5s cubic-bezier(0.4, 0.01, 0.165, 0.99), opacity 0.6s cubic-bezier(0.4, 0.01, 0.165, 0.99); transition: transform 0.5s cubic-bezier(0.4, 0.01, 0.165, 0.99), opacity 0.6s cubic-bezier(0.4, 0.01, 0.165, 0.99), -webkit-transform 0.5s cubic-bezier(0.4, 0.01, 0.165, 0.99); }
    [data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li.nav-item:last-child { border-bottom: 0;}
    .navbar-collapse-show[data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li { -webkit-transform: scale(1) translateY(0px); transform: scale(1) translateY(0px); opacity: 1; }
    [data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li:nth-child(1) { -webkit-transition-delay: 0.49s; transition-delay: 0.49s; }
    [data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li:nth-child(2) { -webkit-transition-delay: 0.42s; transition-delay: 0.42s; }
    [data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li:nth-child(3) { -webkit-transition-delay: 0.35s; transition-delay: 0.35s; }
    [data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li:nth-child(4) { -webkit-transition-delay: 0.28s; transition-delay: 0.28s; }
    [data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li:nth-child(5) { -webkit-transition-delay: 0.21s; transition-delay: 0.21s; }
    [data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li:nth-child(6) { -webkit-transition-delay: 0.14s; transition-delay: 0.14s; }
    [data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li:nth-child(7) { -webkit-transition-delay: 0.07s; transition-delay: 0.07s; }
    [data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li:nth-child(8) { -webkit-transition-delay: 0s; transition-delay: 0s; }
    [data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li:nth-child(9) { -webkit-transition-delay: -0.07s; transition-delay: -0.07s; }
    [data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li:nth-child(10) { -webkit-transition-delay: -0.14s; transition-delay: -0.14s; }
    [data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li:nth-child(11) { -webkit-transition-delay: -0.21s; transition-delay: -0.21s; }
    [data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li:nth-child(12) { -webkit-transition-delay: -0.28s; transition-delay: -0.28s; }
    [data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li:nth-child(13) { -webkit-transition-delay: -0.35s; transition-delay: -0.35s; }
    [data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li:nth-child(14) { -webkit-transition-delay: -0.42s; transition-delay: -0.42s; }
    [data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li:nth-child(15) { -webkit-transition-delay: -0.49s; transition-delay: -0.49s; }
    .navbar-collapse-show[data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li:nth-child(1) { -webkit-transition-delay: 0.27s; transition-delay: 0.27s; }
    .navbar-collapse-show[data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li:nth-child(2) { -webkit-transition-delay: 0.34s; transition-delay: 0.34s; }
    .navbar-collapse-show[data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li:nth-child(3) { -webkit-transition-delay: 0.41s; transition-delay: 0.41s; }
    .navbar-collapse-show[data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li:nth-child(4) { -webkit-transition-delay: 0.48s; transition-delay: 0.48s; }
    .navbar-collapse-show[data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li:nth-child(5) { -webkit-transition-delay: 0.55s; transition-delay: 0.55s; }
    .navbar-collapse-show[data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li:nth-child(6) { -webkit-transition-delay: 0.62s; transition-delay: 0.62s; }
    .navbar-collapse-show[data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li:nth-child(7) { -webkit-transition-delay: 0.69s; transition-delay: 0.69s; }
    .navbar-collapse-show[data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li:nth-child(8) { -webkit-transition-delay: 0.76s; transition-delay: 0.76s; }
    .navbar-collapse-show[data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li:nth-child(9) { -webkit-transition-delay: 0.83s; transition-delay: 0.83s; }
    .navbar-collapse-show[data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li:nth-child(10) { -webkit-transition-delay: 0.9s; transition-delay: 0.9s; }
    .navbar-collapse-show[data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li:nth-child(11) { -webkit-transition-delay: 0.97s; transition-delay: 0.97s; }
    .navbar-collapse-show[data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li:nth-child(12) { -webkit-transition-delay: 1.04s; transition-delay: 1.04s; }
    .navbar-collapse-show[data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li:nth-child(13) { -webkit-transition-delay: 1.11s; transition-delay: 1.11s; }
    .navbar-collapse-show[data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li:nth-child(14) { -webkit-transition-delay: 1.18s; transition-delay: 1.18s; }
    .navbar-collapse-show[data-mobile-nav-style=full-screen-menu] ul.navbar-nav > li:nth-child(15) { -webkit-transition-delay: 1.25s; transition-delay: 1.25s; }
    [data-mobile-nav-style=full-screen-menu] .navbar-collapse.collapsing .mCSB_scrollTools { opacity: 0; }
    [data-mobile-nav-style=full-screen-menu] .navbar-collapse.collapse .mCSB_scrollTools { opacity: 0; }
    [data-mobile-nav-style=full-screen-menu] .navbar-collapse.collapse.show .mCSB_scrollTools { opacity: 1; }

    /* header cart and language */
    .header-cart-icon-mobile-left .cart-item-list, .header-language-mobile-left .dropdown-menu { left: 0; right: auto; }

    /* push menu */
    .push-menu { display: none; }
}
/* Navigation breakpoint end */

@media (max-width: 991px) {
    /* reset */
    html { font-size: 12px }
    section { padding: 75px 0; }
    section.big-section { padding:95px 0; }
    section.extra-big-section { padding: 110px 0; }
    section.half-section { padding:40px 0; }
    .md-no-overlap-section { height: auto !important}
    .md-no-overlap-section ~ section .overlap-section { margin-top: 0 !important}
    .md-last-order { order: 10; }

    /* typography */
    p { margin: 0 0 20px; }

    /* text size */
    .text-large { font-size:16px; line-height:22px; }
    .text-extra-large { font-size: 18px; line-height:26px; }
    .title-small { font-size: 30px; line-height: 30px; }
    .title-large { font-size: 70px; line-height: 65px; }
    .title-extra-large { font-size: 90px; line-height:85px }
    .title-extra-large-heavy { font-size: 9rem; line-height: 11rem; }
    .text-big { font-size: 120px; line-height: 120px; }

    /* lineheight */
    .md-line-height-0px { line-height: 0px; }
    .md-line-height-8px { line-height: 8px; }
    .md-line-height-10px { line-height: 10px; }
    .md-line-height-14px { line-height: 14px; }
    .md-line-height-15px { line-height: 15px; }
    .md-line-height-16px { line-height: 16px; }
    .md-line-height-18px { line-height: 18px; }
    .md-line-height-20px { line-height: 20px; }
    .md-line-height-22px { line-height: 22px; }
    .md-line-height-24px { line-height: 24px; }
    .md-line-height-26px { line-height: 26px; }
    .md-line-height-28px { line-height: 28px; }
    .md-line-height-30px { line-height: 30px; }
    .md-line-height-32px { line-height: 32px; }
    .md-line-height-34px { line-height: 34px; }
    .md-line-height-36px { line-height: 36px; }
    .md-line-height-38px { line-height: 38px; }
    .md-line-height-40px { line-height: 40px; }
    .md-line-height-50px { line-height: 50px; }
    .md-line-height-140px { line-height: 140px; }
    .md-line-height-normal { line-height: normal; }

    /* letter spacing minus */
    .md-letter-spacing-minus-1-half { letter-spacing: -0.50px; }
    .md-letter-spacing-minus-1px { letter-spacing: -1px; }
    .md-letter-spacing-minus-2px { letter-spacing: -2px; }
    .md-letter-spacing-minus-3px { letter-spacing: -3px; }
    .md-letter-spacing-minus-4px { letter-spacing: -4px; }
    .md-letter-spacing-minus-5px { letter-spacing: -5px; }

    /* absolute middle center */
    .md-absolute-middle-center { left: 50%; top: 50%; position: absolute; -ms-transform: translateX(-50%) translateY(-50%); -moz-transform: translateX(-50%) translateY(-50%); -webkit-transform: translateX(-50%) translateY(-50%); transform: translateX(-50%) translateY(-50%); }

    /* background image */
    .md-background-image-none { background: inherit !important; }
    .md-background-position-left { background-position: left center; }
    .md-background-position-right { background-position: right center; }
    .md-background-position-top { background-position: right top; }
    .md-background-position-center { background-position: center; }
    .md-background-position-left-top { background-position: left top; }

    /* blockquote */
    blockquote { padding: 2px 30px; }

    /* box shadow */
    .md-box-shadow-none { box-shadow: none; }

    /* margin */
    .md-margin-one-all { margin:1%; }
    .md-margin-two-all { margin:2%; }
    .md-margin-three-all { margin:3%; }
    .md-margin-four-all { margin:4%; }
    .md-margin-five-all { margin:5%; }
    .md-margin-six-all { margin:6%; }
    .md-margin-seven-all { margin:7%; }
    .md-margin-eight-all { margin:8%; }
    .md-margin-nine-all { margin:9%; }
    .md-margin-ten-all { margin:10%; }
    .md-margin-eleven-all { margin:11%; }
    .md-margin-twelve-all { margin:12%; }
    .md-margin-thirteen-all { margin:13%; }
    .md-margin-fourteen-all { margin:14%; }
    .md-margin-fifteen-all { margin:15%; }
    .md-margin-sixteen-all { margin:16%; }
    .md-margin-seventeen-all { margin:17%; }
    .md-margin-eighteen-all { margin:18%; }
    .md-margin-nineteen-all { margin:19%; }
    .md-margin-twenty-all { margin:20%; }
    .md-margin-5px-all { margin:5px; }
    .md-margin-10px-all { margin:10px; }
    .md-margin-15px-all { margin:15px; }
    .md-margin-20px-all { margin:20px; }
    .md-margin-25px-all { margin:25px; }
    .md-margin-30px-all { margin:30px; }
    .md-margin-35px-all { margin:35px; }
    .md-margin-40px-all { margin:40px; }
    .md-margin-45px-all { margin:45px; }
    .md-margin-50px-all { margin:50px; }
    .md-margin-55px-all { margin:55px; }
    .md-margin-60px-all { margin:60px; }
    .md-margin-65px-all { margin:65px; }
    .md-margin-70px-all { margin:70px; }
    .md-margin-75px-all { margin:75px; }
    .md-margin-80px-all { margin:80px; }
    .md-margin-85px-all { margin:85px; }
    .md-margin-90px-all { margin:90px; }
    .md-margin-95px-all { margin:95px; }
    .md-margin-100px-all { margin:100px; }
    .md-margin-1-rem-all { margin: 1rem; }
    .md-margin-1-half-rem-all { margin: 1.5rem; }
    .md-margin-2-rem-all { margin: 2rem; }
    .md-margin-2-half-rem-all { margin: 2.5rem; }
    .md-margin-3-rem-all { margin: 3rem; }
    .md-margin-3-half-rem-all { margin: 3.5rem; }
    .md-margin-4-rem-all { margin: 4rem; }
    .md-margin-4-half-rem-all { margin: 4.5rem; }
    .md-margin-5-rem-all { margin: 5rem; }
    .md-margin-5-half-rem-all { margin: 5.5rem; }
    .md-margin-6-rem-all { margin: 6rem; }
    .md-margin-6-half-rem-all { margin: 6.5rem; }
    .md-margin-7-rem-all { margin: 7rem; }
    .md-margin-7-half-rem-all { margin: 7.5rem; }
    .md-margin-8-rem-all { margin: 8rem; }
    .md-margin-8-half-rem-all { margin: 8.5rem; }
    .md-margin-9-rem-all { margin: 9rem; }
    .md-margin-9-half-rem-all { margin: 9.5rem; }
    .md-margin-10-rem-all { margin: 10rem; }
    .md-margin-10-half-rem-all { margin: 10.5rem; }

    /* margin top */
    .md-margin-one-top { margin-top:1%; }
    .md-margin-two-top { margin-top:2%; }
    .md-margin-three-top { margin-top:3%; }
    .md-margin-four-top { margin-top:4%; }
    .md-margin-five-top { margin-top:5%; }
    .md-margin-six-top { margin-top:6%; }
    .md-margin-seven-top { margin-top:7%; }
    .md-margin-eight-top { margin-top:8%; }
    .md-margin-nine-top { margin-top:9%; }
    .md-margin-ten-top { margin-top:10%; }
    .md-margin-eleven-top { margin-top:11%; }
    .md-margin-twelve-top { margin-top:12%; }
    .md-margin-thirteen-top { margin-top:13%; }
    .md-margin-fourteen-top { margin-top:14%; }
    .md-margin-fifteen-top { margin-top:15%; }
    .md-margin-sixteen-top { margin-top:16%; }
    .md-margin-seventeen-top { margin-top:17%; }
    .md-margin-eighteen-top { margin-top:18%; }
    .md-margin-nineteen-top { margin-top:19%; }
    .md-margin-twenty-top { margin-top:20%; }
    .md-margin-5px-top { margin-top:5px; }
    .md-margin-10px-top { margin-top:10px; }
    .md-margin-15px-top { margin-top:15px; }
    .md-margin-20px-top { margin-top:20px; }
    .md-margin-25px-top { margin-top:25px; }
    .md-margin-30px-top { margin-top:30px; }
    .md-margin-35px-top { margin-top:35px; }
    .md-margin-40px-top { margin-top:40px; }
    .md-margin-45px-top { margin-top:45px; }
    .md-margin-50px-top { margin-top:50px; }
    .md-margin-55px-top { margin-top:55px; }
    .md-margin-60px-top { margin-top:60px; }
    .md-margin-65px-top { margin-top:65px; }
    .md-margin-70px-top { margin-top:70px; }
    .md-margin-75px-top { margin-top:75px; }
    .md-margin-80px-top { margin-top:80px; }
    .md-margin-85px-top { margin-top:85px; }
    .md-margin-90px-top { margin-top:90px; }
    .md-margin-95px-top { margin-top:95px; }
    .md-margin-100px-top { margin-top:100px; }
    .md-margin-1-rem-top { margin-top: 1rem; }
    .md-margin-1-half-rem-top { margin-top: 1.5rem; }
    .md-margin-2-rem-top { margin-top: 2rem; }
    .md-margin-2-half-rem-top { margin-top: 2.5rem; }
    .md-margin-3-rem-top { margin-top: 3rem; }
    .md-margin-3-half-rem-top { margin-top: 3.5rem; }
    .md-margin-4-rem-top { margin-top: 4rem; }
    .md-margin-4-half-rem-top { margin-top: 4.5rem; }
    .md-margin-5-rem-top { margin-top: 5rem; }
    .md-margin-5-half-rem-top { margin-top: 5.5rem; }
    .md-margin-6-rem-top { margin-top: 6rem; }
    .md-margin-6-half-rem-top { margin-top: 6.5rem; }
    .md-margin-7-rem-top { margin-top: 7rem; }
    .md-margin-7-half-rem-top { margin-top: 7.5rem; }
    .md-margin-8-rem-top { margin-top: 8rem; }
    .md-margin-8-half-rem-top { margin-top: 8.5rem; }
    .md-margin-9-rem-top { margin-top: 9rem; }
    .md-margin-9-half-rem-top { margin-top: 9.5rem; }
    .md-margin-10-rem-top { margin-top: 10rem; }
    .md-margin-10-half-rem-top { margin-top: 10.5rem; }

    /* margin bottom */
    .md-margin-one-bottom { margin-bottom:1%; }
    .md-margin-two-bottom { margin-bottom:2%; }
    .md-margin-three-bottom { margin-bottom:3%; }
    .md-margin-four-bottom { margin-bottom:4%; }
    .md-margin-five-bottom { margin-bottom:5%; }
    .md-margin-six-bottom { margin-bottom:6%; }
    .md-margin-seven-bottom { margin-bottom:7%; }
    .md-margin-eight-bottom { margin-bottom:8%; }
    .md-margin-nine-bottom { margin-bottom:9%; }
    .md-margin-ten-bottom { margin-bottom:10%; }
    .md-margin-eleven-bottom { margin-bottom:11%; }
    .md-margin-twelve-bottom { margin-bottom:12%; }
    .md-margin-thirteen-bottom { margin-bottom:13%; }
    .md-margin-fourteen-bottom { margin-bottom:14%; }
    .md-margin-fifteen-bottom { margin-bottom:15%; }
    .md-margin-sixteen-bottom { margin-bottom:16%; }
    .md-margin-seventeen-bottom { margin-bottom:17%; }
    .md-margin-eighteen-bottom { margin-bottom:18%; }
    .md-margin-nineteen-bottom { margin-bottom:19%; }
    .md-margin-twenty-bottom { margin-bottom:20%; }
    .md-margin-5px-bottom { margin-bottom:5px; }
    .md-margin-10px-bottom { margin-bottom:10px; }
    .md-margin-15px-bottom { margin-bottom:15px; }
    .md-margin-20px-bottom { margin-bottom:20px; }
    .md-margin-25px-bottom { margin-bottom:25px; }
    .md-margin-30px-bottom { margin-bottom:30px; }
    .md-margin-35px-bottom { margin-bottom:35px; }
    .md-margin-40px-bottom { margin-bottom:40px; }
    .md-margin-45px-bottom { margin-bottom:45px; }
    .md-margin-50px-bottom { margin-bottom:50px; }
    .md-margin-55px-bottom { margin-bottom:55px; }
    .md-margin-60px-bottom { margin-bottom:60px; }
    .md-margin-65px-bottom { margin-bottom:65px; }
    .md-margin-70px-bottom { margin-bottom:70px; }
    .md-margin-75px-bottom { margin-bottom:75px; }
    .md-margin-80px-bottom { margin-bottom:80px; }
    .md-margin-85px-bottom { margin-bottom:85px; }
    .md-margin-90px-bottom { margin-bottom:90px; }
    .md-margin-95px-bottom { margin-bottom:95px; }
    .md-margin-100px-bottom { margin-bottom:100px; }
    .md-margin-1-rem-bottom { margin-bottom: 1rem; }
    .md-margin-1-half-rem-bottom { margin-bottom: 1.5rem; }
    .md-margin-2-rem-bottom { margin-bottom: 2rem; }
    .md-margin-2-half-rem-bottom { margin-bottom: 2.5rem; }
    .md-margin-3-rem-bottom { margin-bottom: 3rem; }
    .md-margin-3-half-rem-bottom { margin-bottom: 3.5rem; }
    .md-margin-4-rem-bottom { margin-bottom: 4rem; }
    .md-margin-4-half-rem-bottom { margin-bottom: 4.5rem; }
    .md-margin-5-rem-bottom { margin-bottom: 5rem; }
    .md-margin-5-half-rem-bottom { margin-bottom: 5.5rem; }
    .md-margin-6-rem-bottom { margin-bottom: 6rem; }
    .md-margin-6-half-rem-bottom { margin-bottom: 6.5rem; }
    .md-margin-7-rem-bottom { margin-bottom: 7rem; }
    .md-margin-7-half-rem-bottom { margin-bottom: 7.5rem; }
    .md-margin-8-rem-bottom { margin-bottom: 8rem; }
    .md-margin-8-half-rem-bottom { margin-bottom: 8.5rem; }
    .md-margin-9-rem-bottom { margin-bottom: 9rem; }
    .md-margin-9-half-rem-bottom { margin-bottom: 9.5rem; }
    .md-margin-10-rem-bottom { margin-bottom: 10rem; }
    .md-margin-10-half-rem-bottom { margin-bottom: 10.5rem; }

    /* margin right */
    .md-margin-one-right { margin-right:1%; }
    .md-margin-two-right { margin-right:2%; }
    .md-margin-three-right { margin-right:3%; }
    .md-margin-four-right { margin-right:4%; }
    .md-margin-five-right { margin-right:5%; }
    .md-margin-six-right { margin-right:6%; }
    .md-margin-seven-right { margin-right:7%; }
    .md-margin-eight-right { margin-right:8%; }
    .md-margin-nine-right { margin-right:9%; }
    .md-margin-ten-right { margin-right:10%; }
    .md-margin-eleven-right { margin-right:11%; }
    .md-margin-twelve-right { margin-right:12%; }
    .md-margin-thirteen-right { margin-right:13%; }
    .md-margin-fourteen-right { margin-right:14%; }
    .md-margin-fifteen-right { margin-right:15%; }
    .md-margin-sixteen-right { margin-right:16%; }
    .md-margin-seventeen-right { margin-right:17%; }
    .md-margin-eighteen-right { margin-right:18%; }
    .md-margin-nineteen-right { margin-right:19%; }
    .md-margin-twenty-right { margin-right:20%; }
    .md-margin-5px-right { margin-right:5px; }
    .md-margin-10px-right { margin-right:10px; }
    .md-margin-15px-right { margin-right:15px; }
    .md-margin-20px-right { margin-right:20px; }
    .md-margin-25px-right { margin-right:25px; }
    .md-margin-30px-right { margin-right:30px; }
    .md-margin-35px-right { margin-right:35px; }
    .md-margin-40px-right { margin-right:40px; }
    .md-margin-45px-right { margin-right:45px; }
    .md-margin-50px-right { margin-right:50px; }
    .md-margin-55px-right { margin-right:55px; }
    .md-margin-60px-right { margin-right:60px; }
    .md-margin-65px-right { margin-right:65px; }
    .md-margin-70px-right { margin-right:70px; }
    .md-margin-75px-right { margin-right:75px; }
    .md-margin-80px-right { margin-right:80px; }
    .md-margin-85px-right { margin-right:85px; }
    .md-margin-90px-right { margin-right:90px; }
    .md-margin-95px-right { margin-right:95px; }
    .md-margin-100px-right { margin-right:100px; }
    .md-margin-1-rem-right { margin-right: 1rem; }
    .md-margin-1-half-rem-right { margin-right: 1.5rem; }
    .md-margin-2-rem-right { margin-right: 2rem; }
    .md-margin-2-half-rem-right { margin-right: 2.5rem; }
    .md-margin-3-rem-right { margin-right: 3rem; }
    .md-margin-3-half-rem-right { margin-right: 3.5rem; }
    .md-margin-4-rem-right { margin-right: 4rem; }
    .md-margin-4-half-rem-right { margin-right: 4.5rem; }
    .md-margin-5-rem-right { margin-right: 5rem; }
    .md-margin-5-half-rem-right { margin-right: 5.5rem; }
    .md-margin-6-rem-right { margin-right: 6rem; }
    .md-margin-6-half-rem-right { margin-right: 6.5rem; }
    .md-margin-7-rem-right { margin-right: 7rem; }
    .md-margin-7-half-rem-right { margin-right: 7.5rem; }
    .md-margin-8-rem-right { margin-right: 8rem; }
    .md-margin-8-half-rem-right { margin-right: 8.5rem; }
    .md-margin-9-rem-right { margin-right: 9rem; }
    .md-margin-9-half-rem-right { margin-right: 9.5rem; }
    .md-margin-10-rem-right { margin-right: 10rem; }
    .md-margin-10-half-rem-right { margin-right: 10.5rem; }

    /* margin left */
    .md-margin-one-left { margin-left:1%; }
    .md-margin-two-left { margin-left:2%; }
    .md-margin-three-left { margin-left:3%; }
    .md-margin-four-left { margin-left:4%; }
    .md-margin-five-left { margin-left:5%; }
    .md-margin-six-left { margin-left:6%; }
    .md-margin-seven-left { margin-left:7%; }
    .md-margin-eight-left { margin-left:8%; }
    .md-margin-nine-left { margin-left:9%; }
    .md-margin-ten-left { margin-left:10%; }
    .md-margin-eleven-left { margin-left:11%; }
    .md-margin-twelve-left { margin-left:12%; }
    .md-margin-thirteen-left { margin-left:13%; }
    .md-margin-fourteen-left { margin-left:14%; }
    .md-margin-fifteen-left { margin-left:15%; }
    .md-margin-sixteen-left { margin-left:16%; }
    .md-margin-seventeen-left { margin-left:17%; }
    .md-margin-eighteen-left { margin-left:18%; }
    .md-margin-nineteen-left { margin-left:19%; }
    .md-margin-twenty-left { margin-left:20%; }
    .md-margin-5px-left { margin-left:5px; }
    .md-margin-10px-left { margin-left:10px; }
    .md-margin-15px-left { margin-left:15px; }
    .md-margin-20px-left { margin-left:20px; }
    .md-margin-25px-left { margin-left:25px; }
    .md-margin-30px-left { margin-left:30px; }
    .md-margin-35px-left { margin-left:35px; }
    .md-margin-40px-left { margin-left:40px; }
    .md-margin-45px-left { margin-left:45px; }
    .md-margin-50px-left { margin-left:50px; }
    .md-margin-55px-left { margin-left:55px; }
    .md-margin-60px-left { margin-left:60px; }
    .md-margin-65px-left { margin-left:65px; }
    .md-margin-70px-left { margin-left:70px; }
    .md-margin-75px-left { margin-left:75px; }
    .md-margin-80px-left { margin-left:80px; }
    .md-margin-85px-left { margin-left:85px; }
    .md-margin-90px-left { margin-left:90px; }
    .md-margin-95px-left { margin-left:95px; }
    .md-margin-100px-left { margin-left:100px; }
    .md-margin-1-rem-left { margin-left: 1rem; }
    .md-margin-1-half-rem-left { margin-left: 1.5rem; }
    .md-margin-2-rem-left { margin-left: 2rem; }
    .md-margin-2-half-rem-left { margin-left: 2.5rem; }
    .md-margin-3-rem-left { margin-left: 3rem; }
    .md-margin-3-half-rem-left { margin-left: 3.5rem; }
    .md-margin-4-rem-left { margin-left: 4rem; }
    .md-margin-4-half-rem-left { margin-left: 4.5rem; }
    .md-margin-5-rem-left { margin-left: 5rem; }
    .md-margin-5-half-rem-left { margin-left: 5.5rem; }
    .md-margin-6-rem-left { margin-left: 6rem; }
    .md-margin-6-half-rem-left { margin-left: 6.5rem; }
    .md-margin-7-rem-left { margin-left: 7rem; }
    .md-margin-7-half-rem-left { margin-left: 7.5rem; }
    .md-margin-8-rem-left { margin-left: 8rem; }
    .md-margin-8-half-rem-left { margin-left: 8.5rem; }
    .md-margin-9-rem-left { margin-left: 9rem; }
    .md-margin-9-half-rem-left { margin-left: 9.5rem; }
    .md-margin-10-rem-left { margin-left: 10rem; }
    .md-margin-10-half-rem-left { margin-left: 10.5rem; }

    /* margin left right */
    .md-margin-one-lr { margin-left:1%; margin-right:1%; }
    .md-margin-two-lr { margin-left:2%; margin-right:2%; }
    .md-margin-three-lr { margin-left:3%; margin-right:3%; }
    .md-margin-four-lr { margin-left:4%; margin-right:4%; }
    .md-margin-five-lr { margin-left:5%; margin-right:5%; }
    .md-margin-six-lr { margin-left:6%; margin-right:6%; }
    .md-margin-seven-lr { margin-left:7%; margin-right:7%; }
    .md-margin-eight-lr { margin-left:8%; margin-right:8%; }
    .md-margin-nine-lr { margin-left:9%; margin-right:9%; }
    .md-margin-ten-lr { margin-left:10%; margin-right:10%; }
    .md-margin-eleven-lr { margin-left:11%; margin-right:11%; }
    .md-margin-twelve-lr { margin-left:12%; margin-right:12%; }
    .md-margin-thirteen-lr { margin-left:13%; margin-right:13%; }
    .md-margin-fourteen-lr { margin-left:14%; margin-right:14%; }
    .md-margin-fifteen-lr { margin-left:15%; margin-right:15%; }
    .md-margin-sixteen-lr { margin-left:16%; margin-right:16%; }
    .md-margin-seventeen-lr { margin-left:17%; margin-right:17%; }
    .md-margin-eighteen-lr { margin-left:18%; margin-right:18%; }
    .md-margin-nineteen-lr { margin-left:19%; margin-right:19%; }
    .md-margin-twenty-lr { margin-left:20%; margin-right:20%; }
    .md-margin-5px-lr { margin-left:5px; margin-right:5px; }
    .md-margin-10px-lr { margin-left:10px; margin-right:10px; }
    .md-margin-15px-lr { margin-left:15px; margin-right:15px; }
    .md-margin-20px-lr { margin-left:20px; margin-right:20px; }
    .md-margin-25px-lr { margin-left:25px; margin-right:25px; }
    .md-margin-30px-lr { margin-left:30px; margin-right:30px; }
    .md-margin-35px-lr { margin-left:35px; margin-right:35px; }
    .md-margin-40px-lr { margin-left:40px; margin-right:40px; }
    .md-margin-45px-lr { margin-left:45px; margin-right:45px; }
    .md-margin-50px-lr { margin-left:50px; margin-right:50px; }
    .md-margin-55px-lr { margin-left:55px; margin-right:55px; }
    .md-margin-60px-lr { margin-left:60px; margin-right:60px; }
    .md-margin-65px-lr { margin-left:65px; margin-right:65px; }
    .md-margin-70px-lr { margin-left:70px; margin-right:70px; }
    .md-margin-75px-lr { margin-left:75px; margin-right:75px; }
    .md-margin-80px-lr { margin-left:80px; margin-right:80px; }
    .md-margin-85px-lr { margin-left:85px; margin-right:85px; }
    .md-margin-90px-lr { margin-left:90px; margin-right:90px; }
    .md-margin-95px-lr { margin-left:95px; margin-right:95px; }
    .md-margin-100px-lr { margin-left:100px; margin-right:100px; }
    .md-margin-1-rem-lr { margin-left: 1rem; margin-right: 1rem; }
    .md-margin-1-half-rem-lr { margin-left: 1.5rem; margin-right: 1.5rem; }
    .md-margin-2-rem-lr { margin-left: 2rem; margin-right: 2rem; }
    .md-margin-2-half-rem-lr { margin-left: 2.5rem; margin-right: 2.5rem; }
    .md-margin-3-rem-lr { margin-left: 3rem; margin-right: 3rem; }
    .md-margin-3-half-rem-lr { margin-left: 3.5rem; margin-right: 3.5rem; }
    .md-margin-4-rem-lr { margin-left: 4rem; margin-right: 4rem; }
    .md-margin-4-half-rem-lr { margin-left: 4.5rem; margin-right: 4.5rem; }
    .md-margin-5-rem-lr { margin-left: 5rem; margin-right: 5rem; }
    .md-margin-5-half-rem-lr { margin-left: 5.5rem; margin-right: 5.5rem; }
    .md-margin-6-rem-lr { margin-left: 6rem; margin-right: 6rem; }
    .md-margin-6-half-rem-lr { margin-left: 6.5rem; margin-right: 6.5rem; }
    .md-margin-7-rem-lr { margin-left: 7rem; margin-right: 7rem; }
    .md-margin-7-half-rem-lr { margin-left: 7.5rem; margin-right: 7.5rem; }
    .md-margin-8-rem-lr { margin-left: 8rem; margin-right: 8rem; }
    .md-margin-8-half-rem-lr { margin-left: 8.5rem; margin-right: 8.5rem; }
    .md-margin-9-rem-lr { margin-left: 9rem; margin-right: 9rem; }
    .md-margin-9-half-rem-lr { margin-left: 9.5rem; margin-right: 9.5rem; }
    .md-margin-10-rem-lr { margin-left: 10rem; margin-right: 10rem; }
    .md-margin-10-half-rem-lr { margin-left: 10.5rem; margin-right: 10.5rem; }

    /* margin top bottom */
    .md-margin-one-tb { margin-top:1%; margin-bottom:1%; }
    .md-margin-two-tb { margin-top:2%; margin-bottom:2%; }
    .md-margin-three-tb { margin-top:3%; margin-bottom:3%; }
    .md-margin-four-tb { margin-top:4%; margin-bottom:4%; }
    .md-margin-five-tb { margin-top:5%; margin-bottom:5%; }
    .md-margin-six-tb { margin-top:6%; margin-bottom:6%; }
    .md-margin-seven-tb { margin-top:7%; margin-bottom:7%; }
    .md-margin-eight-tb { margin-top:8%; margin-bottom:8%; }
    .md-margin-nine-tb { margin-top:9%; margin-bottom:9%; }
    .md-margin-ten-tb { margin-top:10%; margin-bottom:10%; }
    .md-margin-eleven-tb { margin-top:11%; margin-bottom:11%; }
    .md-margin-twelve-tb { margin-top:12%; margin-bottom:12%; }
    .md-margin-thirteen-tb { margin-top:13%; margin-bottom:13%; }
    .md-margin-fourteen-tb { margin-top:14%; margin-bottom:14%; }
    .md-margin-fifteen-tb { margin-top:15%; margin-bottom:15%; }
    .md-margin-sixteen-tb { margin-top:16%; margin-bottom:16%; }
    .md-margin-seventeen-tb { margin-top:17%; margin-bottom:17%; }
    .md-margin-eighteen-tb { margin-top:18%; margin-bottom:18%; }
    .md-margin-nineteen-tb { margin-top:19%; margin-bottom:19%; }
    .md-margin-twenty-tb { margin-top:20%; margin-bottom:20%; }
    .md-margin-5px-tb { margin-top:5px; margin-bottom:5px; }
    .md-margin-10px-tb { margin-top:10px; margin-bottom:10px; }
    .md-margin-15px-tb { margin-top:15px; margin-bottom:15px; }
    .md-margin-20px-tb { margin-top:20px; margin-bottom:20px; }
    .md-margin-25px-tb { margin-top:25px; margin-bottom:25px; }
    .md-margin-30px-tb { margin-top:30px; margin-bottom:30px; }
    .md-margin-35px-tb { margin-top:35px; margin-bottom:35px; }
    .md-margin-40px-tb { margin-top:40px; margin-bottom:40px; }
    .md-margin-45px-tb { margin-top:45px; margin-bottom:45px; }
    .md-margin-50px-tb { margin-top:50px; margin-bottom:50px; }
    .md-margin-55px-tb { margin-top:55px; margin-bottom:55px; }
    .md-margin-60px-tb { margin-top:60px; margin-bottom:60px; }
    .md-margin-65px-tb { margin-top:65px; margin-bottom:65px; }
    .md-margin-70px-tb { margin-top:70px; margin-bottom:70px; }
    .md-margin-75px-tb { margin-top:75px; margin-bottom:75px; }
    .md-margin-80px-tb { margin-top:80px; margin-bottom:80px; }
    .md-margin-85px-tb { margin-top:85px; margin-bottom:85px; }
    .md-margin-90px-tb { margin-top:90px; margin-bottom:90px; }
    .md-margin-95px-tb { margin-top:95px; margin-bottom:95px; }
    .md-margin-100px-tb { margin-top:100px; margin-bottom:100px; }
    .md-margin-1-rem-tb { margin-top: 1rem; margin-bottom: 1rem; }
    .md-margin-1-half-rem-tb { margin-top: 1.5rem; margin-bottom: 1.5rem; }
    .md-margin-2-rem-tb { margin-top: 2rem; margin-bottom: 2rem; }
    .md-margin-2-half-rem-tb { margin-top: 2.5rem; margin-bottom: 2.5rem; }
    .md-margin-3-rem-tb { margin-top: 3rem; margin-bottom: 3rem; }
    .md-margin-3-half-rem-tb { margin-top: 3.5rem; margin-bottom: 3.5rem; }
    .md-margin-4-rem-tb { margin-top: 4rem; margin-bottom: 4rem; }
    .md-margin-4-half-rem-tb { margin-top: 4.5rem; margin-bottom: 4.5rem; }
    .md-margin-5-rem-tb { margin-top: 5rem; margin-bottom: 5rem; }
    .md-margin-5-half-rem-tb { margin-top: 5.5rem; margin-bottom: 5.5rem; }
    .md-margin-6-rem-tb { margin-top: 6rem; margin-bottom: 6rem; }
    .md-margin-6-half-rem-tb { margin-top: 6.5rem; margin-bottom: 6.5rem; }
    .md-margin-7-rem-tb { margin-top: 7rem; margin-bottom: 7rem; }
    .md-margin-7-half-rem-tb { margin-top: 7.5rem; margin-bottom: 7.5rem; }
    .md-margin-8-rem-tb { margin-top: 8rem; margin-bottom: 8rem; }
    .md-margin-8-half-rem-tb { margin-top: 8.5rem; margin-bottom: 8.5rem; }
    .md-margin-9-rem-tb { margin-top: 9rem; margin-bottom: 9rem; }
    .md-margin-9-half-rem-tb { margin-top: 9.5rem; margin-bottom: 9.5rem; }
    .md-margin-10-rem-tb { margin-top: 10rem; margin-bottom: 10rem; }
    .md-margin-10-half-rem-tb { margin-top: 10.5rem; margin-bottom: 10.5rem; }

    .md-margin-auto-lr { margin-left: auto !important; margin-right: auto !important; }
    .md-margin-auto { margin: auto; }
    .md-no-margin { margin: 0 !important; }
    .md-no-margin-top { margin-top: 0 !important; }
    .md-no-margin-bottom { margin-bottom: 0 !important; }
    .md-no-margin-left { margin-left: 0 !important; }
    .md-no-margin-right { margin-right: 0 !important; }
    .md-no-margin-tb { margin-top: 0 !important; margin-bottom: 0 !important; }
    .md-no-margin-lr { margin-right: 0 !important; margin-left: 0 !important; }

    /* padding */
    .md-padding-one-all { padding:1%; }
    .md-padding-two-all { padding:2%; }
    .md-padding-three-all { padding:3%; }
    .md-padding-four-all { padding:4%; }
    .md-padding-five-all { padding:5%; }
    .md-padding-six-all { padding:6%; }
    .md-padding-seven-all { padding:7%; }
    .md-padding-eight-all { padding:8%; }
    .md-padding-nine-all { padding:9%; }
    .md-padding-ten-all { padding:10%; }
    .md-padding-eleven-all { padding:11%; }
    .md-padding-twelve-all { padding:12%; }
    .md-padding-thirteen-all { padding:13%; }
    .md-padding-fourteen-all { padding:14%; }
    .md-padding-fifteen-all { padding:15%; }
    .md-padding-sixteen-all { padding:16%; }
    .md-padding-seventeen-all { padding:17%; }
    .md-padding-eighteen-all { padding:18%; }
    .md-padding-nineteen-all { padding:19%; }
    .md-padding-twenty-all { padding:20%; }
    .md-padding-5px-all { padding:5px; }
    .md-padding-10px-all { padding:10px; }
    .md-padding-15px-all { padding:15px; }
    .md-padding-20px-all { padding:20px; }
    .md-padding-25px-all { padding:25px; }
    .md-padding-30px-all { padding:30px; }
    .md-padding-35px-all { padding:35px; }
    .md-padding-40px-all { padding:40px; }
    .md-padding-45px-all { padding:45px; }
    .md-padding-50px-all { padding:50px; }
    .md-padding-55px-all { padding:55px; }
    .md-padding-60px-all { padding:60px; }
    .md-padding-65px-all { padding:65px; }
    .md-padding-70px-all { padding:70px; }
    .md-padding-75px-all { padding:75px; }
    .md-padding-80px-all { padding:80px; }
    .md-padding-85px-all { padding:85px; }
    .md-padding-90px-all { padding:90px; }
    .md-padding-95px-all { padding:95px; }
    .md-padding-100px-all { padding:100px; }
    .md-padding-1-rem-all { padding: 1rem; }
    .md-padding-1-half-rem-all { padding: 1.5rem; }
    .md-padding-2-rem-all { padding: 2rem; }
    .md-padding-2-half-rem-all { padding: 2.5rem; }
    .md-padding-3-rem-all { padding: 3rem; }
    .md-padding-3-half-rem-all { padding: 3.5rem; }
    .md-padding-4-rem-all { padding: 4rem; }
    .md-padding-4-half-rem-all { padding: 4.5rem; }
    .md-padding-5-rem-all { padding: 5rem; }
    .md-padding-5-half-rem-all { padding: 5.5rem; }
    .md-padding-6-rem-all { padding: 6rem; }
    .md-padding-6-half-rem-all { padding: 6.5rem; }
    .md-padding-7-rem-all { padding: 7rem; }
    .md-padding-7-half-rem-all { padding: 7.5rem; }
    .md-padding-8-rem-all { padding: 8rem; }
    .md-padding-8-half-rem-all { padding: 8.5rem; }
    .md-padding-9-rem-all { padding: 9rem; }
    .md-padding-9-half-rem-all { padding: 9.5rem; }
    .md-padding-10-rem-all { padding: 10rem; }
    .md-padding-10-half-rem-all { padding: 10.5rem; }

    /* padding top */
    .md-padding-one-top { padding-top:1%; }
    .md-padding-two-top { padding-top:2%; }
    .md-padding-three-top { padding-top:3%; }
    .md-padding-four-top { padding-top:4%; }
    .md-padding-five-top { padding-top:5%; }
    .md-padding-six-top { padding-top:6%; }
    .md-padding-seven-top { padding-top:7%; }
    .md-padding-eight-top { padding-top:8%; }
    .md-padding-nine-top { padding-top:9%; }
    .md-padding-ten-top { padding-top:10%; }
    .md-padding-eleven-top { padding-top:11%; }
    .md-padding-twelve-top { padding-top:12%; }
    .md-padding-thirteen-top { padding-top:13%; }
    .md-padding-fourteen-top { padding-top:14%; }
    .md-padding-fifteen-top { padding-top:15%; }
    .md-padding-sixteen-top { padding-top:16%; }
    .md-padding-seventeen-top { padding-top:17%; }
    .md-padding-eighteen-top { padding-top:18%; }
    .md-padding-nineteen-top { padding-top:19%; }
    .md-padding-twenty-top { padding-top:20%; }
    .md-padding-5px-top { padding-top:5px; }
    .md-padding-10px-top { padding-top:10px; }
    .md-padding-15px-top { padding-top:15px; }
    .md-padding-20px-top { padding-top:20px; }
    .md-padding-25px-top { padding-top:25px; }
    .md-padding-30px-top { padding-top:30px; }
    .md-padding-35px-top { padding-top:35px; }
    .md-padding-40px-top { padding-top:40px; }
    .md-padding-45px-top { padding-top:45px; }
    .md-padding-50px-top { padding-top:50px; }
    .md-padding-55px-top { padding-top:55px; }
    .md-padding-60px-top { padding-top:60px; }
    .md-padding-65px-top { padding-top:65px; }
    .md-padding-70px-top { padding-top:70px; }
    .md-padding-75px-top { padding-top:75px; }
    .md-padding-80px-top { padding-top:80px; }
    .md-padding-85px-top { padding-top:85px; }
    .md-padding-90px-top { padding-top:90px; }
    .md-padding-95px-top { padding-top:95px; }
    .md-padding-100px-top { padding-top:100px; }
    .md-padding-1-rem-top { padding-top: 1rem; }
    .md-padding-1-half-rem-top { padding-top: 1.5rem; }
    .md-padding-2-rem-top { padding-top: 2rem; }
    .md-padding-2-half-rem-top { padding-top: 2.5rem; }
    .md-padding-3-rem-top { padding-top: 3rem; }
    .md-padding-3-half-rem-top { padding-top: 3.5rem; }
    .md-padding-4-rem-top { padding-top: 4rem; }
    .md-padding-4-half-rem-top { padding-top: 4.5rem; }
    .md-padding-5-rem-top { padding-top: 5rem; }
    .md-padding-5-half-rem-top { padding-top: 5.5rem; }
    .md-padding-6-rem-top { padding-top: 6rem; }
    .md-padding-6-half-rem-top { padding-top: 6.5rem; }
    .md-padding-7-rem-top { padding-top: 7rem; }
    .md-padding-7-half-rem-top { padding-top: 7.5rem; }
    .md-padding-8-rem-top { padding-top: 8rem; }
    .md-padding-8-half-rem-top { padding-top: 8.5rem; }
    .md-padding-9-rem-top { padding-top: 9rem; }
    .md-padding-9-half-rem-top { padding-top: 9.5rem; }
    .md-padding-10-rem-top { padding-top: 10rem; }
    .md-padding-10-half-rem-top { padding-top: 10.5rem; }

    /* padding bottom */
    .md-padding-one-bottom { padding-bottom:1%; }
    .md-padding-two-bottom { padding-bottom:2%; }
    .md-padding-three-bottom { padding-bottom:3%; }
    .md-padding-four-bottom { padding-bottom:4%; }
    .md-padding-five-bottom { padding-bottom:5%; }
    .md-padding-six-bottom { padding-bottom:6%; }
    .md-padding-seven-bottom { padding-bottom:7%; }
    .md-padding-eight-bottom { padding-bottom:8%; }
    .md-padding-nine-bottom { padding-bottom:9%; }
    .md-padding-ten-bottom { padding-bottom:10%; }
    .md-padding-eleven-bottom { padding-bottom:11%; }
    .md-padding-twelve-bottom { padding-bottom:12%; }
    .md-padding-thirteen-bottom { padding-bottom:13%; }
    .md-padding-fourteen-bottom { padding-bottom:14%; }
    .md-padding-fifteen-bottom { padding-bottom:15%; }
    .md-padding-sixteen-bottom { padding-bottom:16%; }
    .md-padding-seventeen-bottom { padding-bottom:17%; }
    .md-padding-eighteen-bottom { padding-bottom:18%; }
    .md-padding-nineteen-bottom { padding-bottom:19%; }
    .md-padding-twenty-bottom { padding-bottom:20%; }
    .md-padding-5px-bottom { padding-bottom:5px; }
    .md-padding-10px-bottom { padding-bottom:10px; }
    .md-padding-15px-bottom { padding-bottom:15px; }
    .md-padding-20px-bottom { padding-bottom:20px; }
    .md-padding-25px-bottom { padding-bottom:25px; }
    .md-padding-30px-bottom { padding-bottom:30px; }
    .md-padding-35px-bottom { padding-bottom:35px; }
    .md-padding-40px-bottom { padding-bottom:40px; }
    .md-padding-45px-bottom { padding-bottom:45px; }
    .md-padding-50px-bottom { padding-bottom:50px; }
    .md-padding-55px-bottom { padding-bottom:55px; }
    .md-padding-60px-bottom { padding-bottom:60px; }
    .md-padding-65px-bottom { padding-bottom:65px; }
    .md-padding-70px-bottom { padding-bottom:70px; }
    .md-padding-75px-bottom { padding-bottom:75px; }
    .md-padding-80px-bottom { padding-bottom:80px; }
    .md-padding-85px-bottom { padding-bottom:85px; }
    .md-padding-90px-bottom { padding-bottom:90px; }
    .md-padding-95px-bottom { padding-bottom:95px; }
    .md-padding-100px-bottom { padding-bottom:100px; }
    .md-padding-1-rem-bottom { padding-bottom: 1rem; }
    .md-padding-1-half-rem-bottom { padding-bottom: 1.5rem; }
    .md-padding-2-rem-bottom { padding-bottom: 2rem; }
    .md-padding-2-half-rem-bottom { padding-bottom: 2.5rem; }
    .md-padding-3-rem-bottom { padding-bottom: 3rem; }
    .md-padding-3-half-rem-bottom { padding-bottom: 3.5rem; }
    .md-padding-4-rem-bottom { padding-bottom: 4rem; }
    .md-padding-4-half-rem-bottom { padding-bottom: 4.5rem; }
    .md-padding-5-rem-bottom { padding-bottom: 5rem; }
    .md-padding-5-half-rem-bottom { padding-bottom: 5.5rem; }
    .md-padding-6-rem-bottom { padding-bottom: 6rem; }
    .md-padding-6-half-rem-bottom { padding-bottom: 6.5rem; }
    .md-padding-7-rem-bottom { padding-bottom: 7rem; }
    .md-padding-7-half-rem-bottom { padding-bottom: 7.5rem; }
    .md-padding-8-rem-bottom { padding-bottom: 8rem; }
    .md-padding-8-half-rem-bottom { padding-bottom: 8.5rem; }
    .md-padding-9-rem-bottom { padding-bottom: 9rem; }
    .md-padding-9-half-rem-bottom { padding-bottom: 9.5rem; }
    .md-padding-10-rem-bottom { padding-bottom: 10rem; }
    .md-padding-10-half-rem-bottom { padding-bottom: 10.5rem; }

    /* padding right */
    .md-padding-one-right { padding-right:1%; }
    .md-padding-two-right { padding-right:2%; }
    .md-padding-three-right { padding-right:3%; }
    .md-padding-four-right { padding-right:4% }
    .md-padding-five-right { padding-right:5%; }
    .md-padding-six-right { padding-right:6%; }
    .md-padding-seven-right { padding-right:7%; }
    .md-padding-eight-right { padding-right:8%; }
    .md-padding-nine-right { padding-right:9%; }
    .md-padding-ten-right { padding-right:10%; }
    .md-padding-eleven-right { padding-right:11%; }
    .md-padding-twelve-right { padding-right:12%; }
    .md-padding-thirteen-right { padding-right:13%; }
    .md-padding-fourteen-right { padding-right:14%; }
    .md-padding-fifteen-right { padding-right:15%; }
    .md-padding-sixteen-right { padding-right:16%; }
    .md-padding-seventeen-right { padding-right:17%; }
    .md-padding-eighteen-right { padding-right:18%; }
    .md-padding-nineteen-right { padding-right:19%; }
    .md-padding-twenty-right { padding-right:20%; }
    .md-padding-5px-right { padding-right:5px; }
    .md-padding-10px-right { padding-right:10px; }
    .md-padding-15px-right { padding-right:15px; }
    .md-padding-20px-right { padding-right:20px; }
    .md-padding-25px-right { padding-right:25px; }
    .md-padding-30px-right { padding-right:30px; }
    .md-padding-35px-right { padding-right:35px; }
    .md-padding-40px-right { padding-right:40px; }
    .md-padding-45px-right { padding-right:45px; }
    .md-padding-50px-right { padding-right:50px; }
    .md-padding-55px-right { padding-right:55px; }
    .md-padding-60px-right { padding-right:60px; }
    .md-padding-65px-right { padding-right:65px; }
    .md-padding-70px-right { padding-right:70px; }
    .md-padding-75px-right { padding-right:75px; }
    .md-padding-80px-right { padding-right:80px; }
    .md-padding-85px-right { padding-right:85px; }
    .md-padding-90px-right { padding-right:90px; }
    .md-padding-95px-right { padding-right:95px; }
    .md-padding-100px-right { padding-right:100px; }
    .md-padding-1-rem-right { padding-right: 1rem; }
    .md-padding-1-half-rem-right { padding-right: 1.5rem; }
    .md-padding-2-rem-right { padding-right: 2rem; }
    .md-padding-2-half-rem-right { padding-right: 2.5rem; }
    .md-padding-3-rem-right { padding-right: 3rem; }
    .md-padding-3-half-rem-right { padding-right: 3.5rem; }
    .md-padding-4-rem-right { padding-right: 4rem; }
    .md-padding-4-half-rem-right { padding-right: 4.5rem; }
    .md-padding-5-rem-right { padding-right: 5rem; }
    .md-padding-5-half-rem-right { padding-right: 5.5rem; }
    .md-padding-6-rem-right { padding-right: 6rem; }
    .md-padding-6-half-rem-right { padding-right: 6.5rem; }
    .md-padding-7-rem-right { padding-right: 7rem; }
    .md-padding-7-half-rem-right { padding-right: 7.5rem; }
    .md-padding-8-rem-right { padding-right: 8rem; }
    .md-padding-8-half-rem-right { padding-right: 8.5rem; }
    .md-padding-9-rem-right { padding-right: 9rem; }
    .md-padding-9-half-rem-right { padding-right: 9.5rem; }
    .md-padding-10-rem-right { padding-right: 10rem; }
    .md-padding-10-half-rem-right { padding-right: 10.5rem; }

    /* padding left */
    .md-padding-one-left { padding-left:1%; }
    .md-padding-two-left { padding-left:2%; }
    .md-padding-three-left { padding-left:3%; }
    .md-padding-four-left { padding-left:4%; }
    .md-padding-five-left { padding-left:5%; }
    .md-padding-six-left { padding-left:6%; }
    .md-padding-seven-left { padding-left:7%; }
    .md-padding-eight-left { padding-left:8%; }
    .md-padding-nine-left { padding-left:9%; }
    .md-padding-ten-left { padding-left:10%; }
    .md-padding-eleven-left { padding-left:11%; }
    .md-padding-twelve-left { padding-left:12%; }
    .md-padding-thirteen-left { padding-left:13%; }
    .md-padding-fourteen-left { padding-left:14%; }
    .md-padding-fifteen-left { padding-left:15%; }
    .md-padding-sixteen-left { padding-left:16%; }
    .md-padding-seventeen-left { padding-left:17%; }
    .md-padding-eighteen-left { padding-left:18%; }
    .md-padding-nineteen-left { padding-left:19%; }
    .md-padding-twenty-left { padding-left:20%; }
    .md-padding-5px-left { padding-left:5px; }
    .md-padding-10px-left { padding-left:10px; }
    .md-padding-15px-left { padding-left:15px; }
    .md-padding-20px-left { padding-left:20px; }
    .md-padding-25px-left { padding-left:25px; }
    .md-padding-30px-left { padding-left:30px; }
    .md-padding-35px-left { padding-left:35px; }
    .md-padding-40px-left { padding-left:40px; }
    .md-padding-45px-left { padding-left:45px; }
    .md-padding-50px-left { padding-left:50px; }
    .md-padding-55px-left { padding-left:55px; }
    .md-padding-60px-left { padding-left:60px; }
    .md-padding-65px-left { padding-left:65px; }
    .md-padding-70px-left { padding-left:70px; }
    .md-padding-75px-left { padding-left:75px; }
    .md-padding-80px-left { padding-left:80px; }
    .md-padding-85px-left { padding-left:85px; }
    .md-padding-90px-left { padding-left:90px; }
    .md-padding-95px-left { padding-left:95px; }
    .md-padding-100px-left { padding-left:100px; }
    .md-padding-1-rem-left { padding-left: 1rem; }
    .md-padding-1-half-rem-left { padding-left: 1.5rem; }
    .md-padding-2-rem-left { padding-left: 2rem; }
    .md-padding-2-half-rem-left { padding-left: 2.5rem; }
    .md-padding-3-rem-left { padding-left: 3rem; }
    .md-padding-3-half-rem-left { padding-left: 3.5rem; }
    .md-padding-4-rem-left { padding-left: 4rem; }
    .md-padding-4-half-rem-left { padding-left: 4.5rem; }
    .md-padding-5-rem-left { padding-left: 5rem; }
    .md-padding-5-half-rem-left { padding-left: 5.5rem; }
    .md-padding-6-rem-left { padding-left: 6rem; }
    .md-padding-6-half-rem-left { padding-left: 6.5rem; }
    .md-padding-7-rem-left { padding-left: 7rem; }
    .md-padding-7-half-rem-left { padding-left: 7.5rem; }
    .md-padding-8-rem-left { padding-left: 8rem; }
    .md-padding-8-half-rem-left { padding-left: 8.5rem; }
    .md-padding-9-rem-left { padding-left: 9rem; }
    .md-padding-9-half-rem-left { padding-left: 9.5rem; }
    .md-padding-10-rem-left { padding-left: 10rem; }
    .md-padding-10-half-rem-left { padding-left: 10.5rem; }

    /* padding top bottom */
    .md-padding-one-tb { padding-top:1%; padding-bottom:1%; }
    .md-padding-two-tb { padding-top:2%; padding-bottom:2%; }
    .md-padding-three-tb { padding-top:3%; padding-bottom:3%; }
    .md-padding-four-tb { padding-top:4%; padding-bottom:4%; }
    .md-padding-five-tb { padding-top:5%; padding-bottom:5%; }
    .md-padding-six-tb { padding-top:6%; padding-bottom:6%; }
    .md-padding-seven-tb { padding-top:7%; padding-bottom:7%; }
    .md-padding-eight-tb { padding-top:8%; padding-bottom:8%; }
    .md-padding-nine-tb { padding-top:9%; padding-bottom:9%; }
    .md-padding-ten-tb { padding-top:10%; padding-bottom:10%; }
    .md-padding-eleven-tb { padding-top:11%; padding-bottom:11%; }
    .md-padding-twelve-tb { padding-top:12%; padding-bottom:12%; }
    .md-padding-thirteen-tb { padding-top:13%; padding-bottom:13%; }
    .md-padding-fourteen-tb { padding-top:14%; padding-bottom:14%; }
    .md-padding-fifteen-tb { padding-top:15%; padding-bottom:15%; }
    .md-padding-sixteen-tb { padding-top:16%; padding-bottom:16%; }
    .md-padding-seventeen-tb { padding-top:17%; padding-bottom:17%; }
    .md-padding-eighteen-tb { padding-top:18%; padding-bottom:18%; }
    .md-padding-nineteen-tb { padding-top:19%; padding-bottom:19%; }
    .md-padding-twenty-tb { padding-top:20%; padding-bottom:20%; }
    .md-padding-5px-tb { padding-top:5px; padding-bottom:5px; }
    .md-padding-10px-tb { padding-top:10px; padding-bottom:10px; }
    .md-padding-15px-tb { padding-top:15px; padding-bottom:15px; }
    .md-padding-20px-tb { padding-top:20px; padding-bottom:20px; }
    .md-padding-25px-tb { padding-top:25px; padding-bottom:25px; }
    .md-padding-30px-tb { padding-top:30px; padding-bottom:30px; }
    .md-padding-35px-tb { padding-top:35px; padding-bottom:35px; }
    .md-padding-40px-tb { padding-top:40px; padding-bottom:40px; }
    .md-padding-45px-tb { padding-top:45px; padding-bottom:45px; }
    .md-padding-50px-tb { padding-top:50px; padding-bottom:50px; }
    .md-padding-55px-tb { padding-top:55px; padding-bottom:55px; }
    .md-padding-60px-tb { padding-top:60px; padding-bottom:60px; }
    .md-padding-65px-tb { padding-top:65px; padding-bottom:65px; }
    .md-padding-70px-tb { padding-top:70px; padding-bottom:70px; }
    .md-padding-75px-tb { padding-top:75px; padding-bottom:75px; }
    .md-padding-80px-tb { padding-top:80px; padding-bottom:80px; }
    .md-padding-85px-tb { padding-top:85px; padding-bottom:85px; }
    .md-padding-90px-tb { padding-top:90px; padding-bottom:90px; }
    .md-padding-95px-tb { padding-top:95px; padding-bottom:95px; }
    .md-padding-100px-tb { padding-top:100px; padding-bottom:100px; }
    .md-padding-1-rem-tb { padding-top: 1rem; padding-bottom: 1rem; }
    .md-padding-1-half-rem-tb { padding-top: 1.5rem; padding-bottom: 1.5rem; }
    .md-padding-2-rem-tb { padding-top: 2rem; padding-bottom: 2rem; }
    .md-padding-2-half-rem-tb { padding-top: 2.5rem; padding-bottom: 2.5rem; }
    .md-padding-3-rem-tb { padding-top: 3rem; padding-bottom: 3rem; }
    .md-padding-3-half-rem-tb { padding-top: 3.5rem; padding-bottom: 3.5rem; }
    .md-padding-4-rem-tb { padding-top: 4rem; padding-bottom: 4rem; }
    .md-padding-4-half-rem-tb { padding-top: 4.5rem; padding-bottom: 4.5rem; }
    .md-padding-5-rem-tb { padding-top: 5rem; padding-bottom: 5rem; }
    .md-padding-5-half-rem-tb { padding-top: 5.5rem; padding-bottom: 5.5rem; }
    .md-padding-6-rem-tb { padding-top: 6rem; padding-bottom: 6rem; }
    .md-padding-6-half-rem-tb { padding-top: 6.5rem; padding-bottom: 6.5rem; }
    .md-padding-7-rem-tb { padding-top: 7rem; padding-bottom: 7rem; }
    .md-padding-7-half-rem-tb { padding-top: 7.5rem; padding-bottom: 7.5rem; }
    .md-padding-8-rem-tb { padding-top: 8rem; padding-bottom: 8rem; }
    .md-padding-8-half-rem-tb { padding-top: 8.5rem; padding-bottom: 8.5rem; }
    .md-padding-9-rem-tb { padding-top: 9rem; padding-bottom: 9rem; }
    .md-padding-9-half-rem-tb { padding-top: 9.5rem; padding-bottom: 9.5rem; }
    .md-padding-10-rem-tb { padding-top: 10rem; padding-bottom: 10rem; }
    .md-padding-10-half-rem-tb { padding-top: 10.5rem; padding-bottom: 10.5rem; }

    /* padding left right */
    .md-padding-one-lr { padding-left:1%; padding-right:1%; }
    .md-padding-two-lr { padding-left:2%; padding-right:2%; }
    .md-padding-three-lr { padding-left:3%; padding-right:3%; }
    .md-padding-four-lr { padding-left:4%; padding-right:4%; }
    .md-padding-five-lr { padding-left:5%; padding-right:5%; }
    .md-padding-six-lr { padding-left:6%; padding-right:6%; }
    .md-padding-seven-lr { padding-left:7%; padding-right:7%; }
    .md-padding-eight-lr { padding-left:8%; padding-right:8%; }
    .md-padding-nine-lr { padding-left:9%; padding-right:9%; }
    .md-padding-ten-lr { padding-left:10%; padding-right:10%; }
    .md-padding-eleven-lr { padding-left:11%; padding-right:11%; }
    .md-padding-twelve-lr { padding-left:12%; padding-right:12%; }
    .md-padding-thirteen-lr { padding-left:13%; padding-right:13%; }
    .md-padding-fourteen-lr { padding-left:14%; padding-right:14%; }
    .md-padding-fifteen-lr { padding-left:15%; padding-right:15%; }
    .md-padding-sixteen-lr { padding-left:16%; padding-right:16%; }
    .md-padding-seventeen-lr { padding-left:17%; padding-right:17%; }
    .md-padding-eighteen-lr { padding-left:18%; padding-right:18%; }
    .md-padding-nineteen-lr { padding-left:19%; padding-right:19%; }
    .md-padding-twenty-lr { padding-left:20%; padding-right:20%; }
    .md-padding-5px-lr { padding-left:5px; padding-right:5px; }
    .md-padding-10px-lr { padding-left:10px; padding-right:10px; }
    .md-padding-15px-lr { padding-left:15px; padding-right:15px; }
    .md-padding-20px-lr { padding-left:20px; padding-right:20px; }
    .md-padding-25px-lr { padding-left:25px; padding-right:25px; }
    .md-padding-30px-lr { padding-left:30px; padding-right:30px; }
    .md-padding-35px-lr { padding-left:35px; padding-right:35px; }
    .md-padding-40px-lr { padding-left:40px; padding-right:40px; }
    .md-padding-45px-lr { padding-left:45px; padding-right:45px; }
    .md-padding-50px-lr { padding-left:50px; padding-right:50px; }
    .md-padding-55px-lr { padding-left:55px; padding-right:55px; }
    .md-padding-60px-lr { padding-left:60px; padding-right:60px; }
    .md-padding-65px-lr { padding-left:65px; padding-right:65px; }
    .md-padding-70px-lr { padding-left:70px; padding-right:70px; }
    .md-padding-75px-lr { padding-left:75px; padding-right:75px; }
    .md-padding-80px-lr { padding-left:80px; padding-right:80px; }
    .md-padding-85px-lr { padding-left:85px; padding-right:85px; }
    .md-padding-90px-lr { padding-left:90px; padding-right:90px; }
    .md-padding-95px-lr { padding-left:95px; padding-right:95px; }
    .md-padding-100px-lr { padding-left:100px; padding-right:100px; }
    .md-padding-1-rem-lr { padding-left: 1rem; padding-right: 1rem; }
    .md-padding-1-half-rem-lr { padding-left: 1.5rem; padding-right: 1.5rem; }
    .md-padding-2-rem-lr { padding-left: 2rem; padding-right: 2rem; }
    .md-padding-2-half-rem-lr { padding-left: 2.5rem; padding-right: 2.5rem; }
    .md-padding-3-rem-lr { padding-left: 3rem; padding-right: 3rem; }
    .md-padding-3-half-rem-lr { padding-left: 3.5rem; padding-right: 3.5rem; }
    .md-padding-4-rem-lr { padding-left: 4rem; padding-right: 4rem; }
    .md-padding-4-half-rem-lr { padding-left: 4.5rem; padding-right: 4.5rem; }
    .md-padding-5-rem-lr { padding-left: 5rem; padding-right: 5rem; }
    .md-padding-5-half-rem-lr { padding-left: 5.5rem; padding-right: 5.5rem; }
    .md-padding-6-rem-lr { padding-left: 6rem; padding-right: 6rem; }
    .md-padding-6-half-rem-lr { padding-left: 6.5rem; padding-right: 6.5rem; }
    .md-padding-7-rem-lr { padding-left: 7rem; padding-right: 7rem; }
    .md-padding-7-half-rem-lr { padding-left: 7.5rem; padding-right: 7.5rem; }
    .md-padding-8-rem-lr { padding-left: 8rem; padding-right: 8rem; }
    .md-padding-8-half-rem-lr { padding-left: 8.5rem; padding-right: 8.5rem; }
    .md-padding-9-rem-lr { padding-left: 9rem; padding-right: 9rem; }
    .md-padding-9-half-rem-lr { padding-left: 9.5rem; padding-right: 9.5rem; }
    .md-padding-10-rem-lr { padding-left: 10rem; padding-right: 10rem; }
    .md-padding-10-half-rem-lr { padding-left: 10.5rem; padding-right: 10.5rem; }

    .md-no-padding { padding:0 !important; }
    .md-no-padding-lr { padding-left: 0 !important; padding-right: 0 !important; }
    .md-no-padding-tb { padding-top: 0 !important; padding-bottom: 0 !important; }
    .md-no-padding-top { padding-top:0 !important; }
    .md-no-padding-bottom { padding-bottom:0 !important; }
    .md-no-padding-left { padding-left:0 !important; }
    .md-no-padding-right { padding-right:0 !important; }

    /* display and overflow */
    .md-d-initial { display: initial !important; }
    .md-overflow-hidden { overflow:hidden !important; }
    .md-overflow-visible { overflow:visible !important; }
    .md-overflow-auto { overflow:auto !important; }

    /* position */
    .md-position-relative { position: relative !important; }
    .md-position-absolute { position: absolute !important; }
    .md-position-fixed { position: fixed !important; }
    .md-position-inherit { position: inherit !important; }
    .md-position-initial { position: initial !important; }

    /* top */
    .md-top-0px { top: 0; }
    .md-top-1px { top: 1px; }
    .md-top-2px { top: 2px; }
    .md-top-3px { top: 3px; }
    .md-top-4px { top: 4px; }
    .md-top-5px { top: 5px; }
    .md-top-6px { top: 6px; }
    .md-top-7px { top: 7px; }
    .md-top-8px { top: 8px; }
    .md-top-9px { top: 9px; }
    .md-top-10px { top: 10px; }
    .md-top-15px { top: 15px; }
    .md-top-20px { top: 20px; }
    .md-top-25px { top: 25px; }
    .md-top-30px { top: 30px; }
    .md-top-35px { top: 35px; }
    .md-top-40px { top: 40px; }
    .md-top-45px { top: 45px; }
    .md-top-50px { top: 50px; }
    .md-top-auto { top:auto; }
    .md-top-inherit { top:inherit; }

    /* top minus */
    .md-top-minus-1px { top: -1px; }
    .md-top-minus-2px { top: -2px; }
    .md-top-minus-3px { top: -3px; }
    .md-top-minus-4px { top: -4px; }
    .md-top-minus-5px { top: -5px; }
    .md-top-minus-6px { top: -6px; }
    .md-top-minus-7px { top: -7px; }
    .md-top-minus-8px { top: -8px; }
    .md-top-minus-9px { top: -9px; }
    .md-top-minus-10px { top: -10px; }
    .md-top-minus-15px { top: -15px; }
    .md-top-minus-20px { top: -20px; }
    .md-top-minus-25px { top: -25px; }
    .md-top-minus-30px { top: -30px; }
    .md-top-minus-35px { top: -35px; }
    .md-top-minus-40px { top: -40px; }
    .md-top-minus-45px { top: -45px; }
    .md-top-minus-50px { top: -50px; }

    /* bottom */
    .md-bottom-0px { bottom:0; }
    .md-bottom-1px { bottom:1px; }
    .md-bottom-2px { bottom:2px; }
    .md-bottom-3px { bottom:3px; }
    .md-bottom-4px { bottom:4px; }
    .md-bottom-5px { bottom:5px; }
    .md-bottom-6px { bottom:6px; }
    .md-bottom-7px { bottom:7px; }
    .md-bottom-8px { bottom:8px; }
    .md-bottom-9px { bottom:9px; }
    .md-bottom-10px { bottom:10px; }
    .md-bottom-15px { bottom:15px; }
    .md-bottom-20px { bottom:20px; }
    .md-bottom-25px { bottom:25px; }
    .md-bottom-30px { bottom:30px; }
    .md-bottom-35px { bottom:35px; }
    .md-bottom-40px { bottom:40px; }
    .md-bottom-45px { bottom:45px; }
    .md-bottom-50px { bottom:50px; }
    .md-bottom-55px { bottom:55px; }
    .md-bottom-60px { bottom:60px; }
    .md-bottom-auto { bottom: auto; }
    .md-bottom-inherit { bottom: inherit; }

    /* bottom minus */
    .md-bottom-minus-1px { bottom: -1px; }
    .md-bottom-minus-2px { bottom: -2px; }
    .md-bottom-minus-3px { bottom: -3px; }
    .md-bottom-minus-4px { bottom: -4px; }
    .md-bottom-minus-5px { bottom: -5px; }
    .md-bottom-minus-6px { bottom: -6px; }
    .md-bottom-minus-7px { bottom: -7px; }
    .md-bottom-minus-8px { bottom: -8px; }
    .md-bottom-minus-9px { bottom: -9px; }
    .md-bottom-minus-10px { bottom: -10px; }
    .md-bottom-minus-15px { bottom: -15px; }
    .md-bottom-minus-20px { bottom: -20px; }
    .md-bottom-minus-25px { bottom: -25px; }
    .md-bottom-minus-30px { bottom: -30px; }
    .md-bottom-minus-35px { bottom: -35px; }
    .md-bottom-minus-40px { bottom: -40px; }
    .md-bottom-minus-45px { bottom: -45px; }
    .md-bottom-minus-50px { bottom: -50px; }

    /* right */
    .md-right-0px { right: 0; }
    .md-right-1px { right: 1px; }
    .md-right-2px { right: 2px; }
    .md-right-3px { right: 3px; }
    .md-right-4px { right: 4px; }
    .md-right-5px { right: 5px; }
    .md-right-6px { right: 6px; }
    .md-right-7px { right: 7px; }
    .md-right-8px { right: 8px; }
    .md-right-9px { right: 9px; }
    .md-right-10px { right: 10px; }
    .md-right-15px { right: 15px; }
    .md-right-20px { right: 20px; }
    .md-right-25px { right: 25px; }
    .md-right-30px { right: 30px; }
    .md-right-35px { right: 35px; }
    .md-right-40px { right: 40px; }
    .md-right-45px { right: 45px; }
    .md-right-50px { right: 50px; }
    .md-right-auto { right: auto; }
    .md-right-inherit { right: inherit; }

    /* right minus */
    .md-right-minus-1px { right: -1px; }
    .md-right-minus-2px { right: -2px; }
    .md-right-minus-3px { right: -3px; }
    .md-right-minus-4px { right: -4px; }
    .md-right-minus-5px { right: -5px; }
    .md-right-minus-6px { right: -6px; }
    .md-right-minus-7px { right: -7px; }
    .md-right-minus-8px { right: -8px; }
    .md-right-minus-9px { right: -9px; }
    .md-right-minus-10px { right: -10px; }
    .md-right-minus-15px { right: -15px; }
    .md-right-minus-20px { right: -20px; }
    .md-right-minus-25px { right: -25px; }
    .md-right-minus-30px { right: -30px; }
    .md-right-minus-35px { right: -35px; }
    .md-right-minus-40px { right: -40px; }
    .md-right-minus-45px { right: -45px; }
    .md-right-minus-50px { right: -50px; }

    /* left */
    .md-left-0px { left: 0; }
    .md-left-1px { left: 1px; }
    .md-left-2px { left: 2px; }
    .md-left-3px { left: 3px; }
    .md-left-4px { left: 4px; }
    .md-left-5px { left: 5px; }
    .md-left-6px { left: 6px; }
    .md-left-7px { left: 7px; }
    .md-left-8px { left: 8px; }
    .md-left-9px { left: 9px; }
    .md-left-10px { left: 10px; }
    .md-left-15px { left: 15px; }
    .md-left-20px { left: 20px; }
    .md-left-25px { left: 25px; }
    .md-left-30px { left: 30px; }
    .md-left-35px { left: 35px; }
    .md-left-40px { left: 40px; }
    .md-left-45px { left: 45px; }
    .md-left-50px { left: 50px; }
    .md-left-55px { left: 55px; }
    .md-left-60px { left: 60px; }
    .md-left-auto { left: auto; }
    .md-left-inherit { left: inherit; }

    /* left minus */
    .md-left-minus-1px { left: -1px; }
    .md-left-minus-2px { left: -2px; }
    .md-left-minus-3px { left: -3px; }
    .md-left-minus-4px { left: -4px; }
    .md-left-minus-5px { left: -5px; }
    .md-left-minus-6px { left: -6px; }
    .md-left-minus-7px { left: -7px; }
    .md-left-minus-8px { left: -8px; }
    .md-left-minus-9px { left: -9px; }
    .md-left-minus-10px { left: -10px; }
    .md-left-minus-15px { left: -15px; }
    .md-left-minus-20px { left: -20px; }
    .md-left-minus-25px { left: -25px; }
    .md-left-minus-30px { left: -30px; }
    .md-left-minus-35px { left: -35px; }
    .md-left-minus-40px { left: -40px; }
    .md-left-minus-45px { left: -45px; }
    .md-left-minus-50px { left: -50px; }

    /* md width */
    .md-w-1px { width:1px !important; }
    .md-w-2px { width:2px !important; }
    .md-w-3px { width:3px !important; }
    .md-w-4px { width:4px !important; }
    .md-w-5px { width:5px !important; }
    .md-w-6px { width:6px !important; }
    .md-w-7px { width:7px !important; }
    .md-w-8px { width:8px !important; }
    .md-w-9px { width:9px !important; }
    .md-w-10px { width:10px !important; }
    .md-w-15px { width:15px !important; }
    .md-w-20px { width:20px !important; }
    .md-w-25px { width:25px !important; }
    .md-w-30px { width:30px !important; }
    .md-w-35px { width:35px !important; }
    .md-w-40px { width:40px !important; }
    .md-w-50px { width:50px !important; }
    .md-w-55px { width:55px !important; }
    .md-w-60px { width:60px !important; }
    .md-w-65px { width:65px !important; }
    .md-w-70px { width:70px !important; }
    .md-w-75px { width:75px !important; }
    .md-w-80px { width:80px !important; }
    .md-w-85px { width:85px !important; }
    .md-w-90px { width:90px !important; }
    .md-w-95px { width:95px !important; }
    .md-w-100px { width:100px !important; }
    .md-w-110px { width:110px !important; }
    .md-w-120px { width:120px !important; }
    .md-w-130px { width:130px !important; }
    .md-w-140px { width:140px !important; }
    .md-w-150px { width:150px !important; }
    .md-w-160px { width:160px !important; }
    .md-w-170px { width:170px !important; }
    .md-w-180px { width:180px !important; }
    .md-w-190px { width:190px !important; }
    .md-w-200px { width:200px !important; }
    .md-w-250px { width:250px !important; }
    .md-w-300px { width:300px !important; }
    .md-w-350px { width:350px !important; }
    .md-w-400px { width:400px !important; }
    .md-w-450px { width:450px !important; }
    .md-w-500px { width:500px !important; }
    .md-w-550px { width:550px !important; }
    .md-w-600px { width:600px !important; }
    .md-w-650px { width:650px !important; }
    .md-w-700px { width:700px !important; }
    .md-w-750px { width:750px !important; }
    .md-w-800px { width:800px !important; }
    .md-w-850px { width:850px !important; }
    .md-w-900px { width:900px !important; }
    .md-w-950px { width:950px !important; }
    .md-w-1000px { width:1000px !important; }
    .md-w-10 { width: 10% !important; }
    .md-w-15 { width: 15% !important; }
    .md-w-20 { width: 20% !important; }
    .md-w-25 { width: 25% !important; }
    .md-w-30 { width: 30% !important; }
    .md-w-35 { width: 35% !important; }
    .md-w-40 { width: 40% !important; }
    .md-w-45 { width: 45% !important; }
    .md-w-50 { width: 50% !important; }
    .md-w-55 { width: 55% !important; }
    .md-w-60 { width: 60% !important; }
    .md-w-65 { width: 65% !important; }
    .md-w-70 { width: 70% !important; }
    .md-w-75 { width: 75% !important; }
    .md-w-80 { width: 80% !important; }
    .md-w-85 { width: 85% !important; }
    .md-w-90 { width: 90% !important; }
    .md-w-95 { width: 95% !important; }
    .md-w-100 { width: 100% !important; }
    .md-w-auto { width:auto !important; }

    /* height */
    .md-h-1px { height: 1px !important; }
    .md-h-2px { height: 2px !important; }
    .md-h-3px { height: 3px !important; }
    .md-h-4px { height: 4px !important; }
    .md-h-5px { height: 5px !important; }
    .md-h-6px { height: 6px !important; }
    .md-h-7px { height: 7px !important; }
    .md-h-8px { height: 8px !important; }
    .md-h-9px { height: 9px !important; }
    .md-h-10px { height: 10px !important; }
    .md-h-20px { height: 20px !important; }
    .md-h-30px { height: 30px !important; }
    .md-h-40px { height: 40px !important; }
    .md-h-42px { height: 42px !important; }
    .md-h-50px { height: 50px !important; }
    .md-h-60px { height: 60px !important; }
    .md-h-70px { height: 70px !important; }
    .md-h-80px { height: 80px !important; }
    .md-h-90px { height: 90px !important; }
    .md-h-100px { height: 100px !important; }
    .md-h-110px { height: 110px !important; }
    .md-h-120px { height: 120px !important; }
    .md-h-130px { height: 130px !important; }
    .md-h-140px { height: 140px !important; }
    .md-h-150px { height: 150px !important; }
    .md-h-160px { height: 160px !important; }
    .md-h-170px { height: 170px !important; }
    .md-h-180px { height: 180px !important; }
    .md-h-190px { height: 190px !important; }
    .md-h-200px { height: 200px !important; }
    .md-h-250px { height: 250px !important; }
    .md-h-300px { height: 300px !important; }
    .md-h-350px { height: 350px !important; }
    .md-h-400px { height: 400px !important; }
    .md-h-450px { height: 450px !important; }
    .md-h-500px { height: 500px !important; }
    .md-h-520px { height: 520px !important; }
    .md-h-550px { height: 550px !important; }
    .md-h-580px { height: 580px !important; }
    .md-h-600px { height: 600px !important; }
    .md-h-650px { height: 650px !important; }
    .md-h-700px { height: 700px !important; }
    .md-h-720px { height: 720px !important; }
    .md-h-750px { height: 750px !important; }
    .md-h-800px { height: 800px !important; }
    .md-h-820px { height: 820px !important; }
    .md-h-830px { height: 830px !important; }
    .md-h-850px { height: 850px !important; }

    .md-h-50 { height: 50%; }
    .md-h-100 { height: 100% !important; }
    .md-h-auto { height:auto !important; }

    /* min-height */
    .md-min-h-100px { min-height: 100px; }
    .md-min-h-200px { min-height: 200px; }
    .md-min-h-300px { min-height: 300px; }
    .md-min-h-400px { min-height: 400px; }
    .md-min-h-500px { min-height: 500px; }
    .md-min-h-600px { min-height: 600px; }
    .md-min-h-700px { min-height: 700px; }

    /* screen height */
    .one-third-screen { height:550px; }
    .one-fourth-screen { height:650px; }
    .one-fifth-screen { height:750px; }

    /* text size */
    .text-extra-big { font-size: 130px; line-height: 130px; }

    /* letter spacing */
    .md-letter-spacing-normal { letter-spacing: normal; }
    .md-letter-spacing-1-half { letter-spacing: 0.50px; }
    .md-letter-spacing-1px { letter-spacing: 1px; }
    .md-letter-spacing-2px { letter-spacing: 2px; }
    .md-letter-spacing-3px { letter-spacing: 3px; }
    .md-letter-spacing-4px { letter-spacing: 4px; }
    .md-letter-spacing-5px { letter-spacing: 5px; }

    /* list style 08 */
    .list-style-08 li:last-child { padding-bottom: 22px; }

    /* accordion style 04 */
    .accordion-style-04 .panel .panel-body { width: 50%; }

    /* time table */
    .time-table .panel { padding: 20px; }
    .time-table .panel .panel-time { min-width: 150px; }
    .time-table .panel .panel-body { width: calc(100% - 330px); }

    /* tab */
    .nav-tabs > li { padding: 0 15px; }
    .nav-tabs > li.nav-item { margin: 0; padding: 0 8px; }

    /* tab style 05 */
    .tab-style-05 .nav-tabs li { padding: 0; }
    .tab-style-05 .nav-tabs li a { padding: 8px 22px; }

    /* tab style 06 */
    .tab-style-06 .nav-tabs > li.nav-item > a.nav-link { padding: 0 20px 22px; }

    /* tab style 07 */
    .tab-style-07 .nav-tabs { -ms-flex-wrap: nowrap; flex-wrap: nowrap; }
    .tab-style-07 .nav-tabs > li.nav-item > a.nav-link { padding: 20px 30px 18px 30px; line-height: 26px; }

    /* testimonials carousel style 01 */
    .testimonials-carousel-style-01 { min-width: 100%; }

    /* testimonials carousel style 02 */
    .testimonials-carousel-style-02 { min-width: 100%; }

    /* process step style 4 */
    .process-step-style-04 .process-step-item { padding-left: 0; padding-right: 0; }

    /* no border */
    .md-no-border-top { border-top:0 !important }
    .md-no-border-bottom { border-bottom:0 !important }
    .md-no-border-right { border-right:0 !important }
    .md-no-border-left { border-left:0 !important }
    .md-no-border-all { border: 0 !important }

    /* border width */
    .md-border-width-1px { border-width:1px !important; }
    .md-border-width-2px { border-width:2px !important; }
    .md-border-width-3px { border-width:3px !important; }
    .md-border-width-4px { border-width:4px !important; }
    .md-border-width-5px { border-width:5px !important; }
    .md-border-width-6px { border-width:6px !important; }
    .md-border-width-7px { border-width:7px !important; }
    .md-border-width-8px { border-width:8px !important; }
    .md-border-width-9px { border-width:9px !important; }
    .md-border-width-10px { border-width:10px !important; }
    .md-border-width-11px { border-width:11px !important; }
    .md-border-width-12px { border-width:12px !important; }
    .md-border-width-13px { border-width:13px !important; }
    .md-border-width-14px { border-width:14px !important; }
    .md-border-width-15px { border-width:15px !important; }
    .md-border-width-16px { border-width:16px !important; }
    .md-border-width-17px { border-width:17px !important; }
    .md-border-width-18px { border-width:18px !important; }
    .md-border-width-19px { border-width:19px !important; }
    .md-border-width-20px { border-width:20px !important; }

    /* border */
    .md-border-all { border: 1px solid; }
    .md-border-top { border-top: 1px solid; }
    .md-border-bottom { border-bottom: 1px solid; }
    .md-border-left { border-left: 1px solid; }
    .md-border-right { border-right: 1px solid; }
    .md-border-lr { border-left: 1px solid; border-right: 1px solid; }
    .md-border-tb { border-top: 1px solid; border-bottom: 1px solid; }

    /* border color */
    .md-border-color-white { border-color: #fff; }
    .md-border-color-black { border-color: #000; }
    .md-border-color-sky-blue { border-color: #2e94eb; }
    .md-border-color-extra-dark-gray { border-color: #232323; }
    .md-border-color-medium-dark-gray { border-color: #363636; }
    .md-border-color-dark-gray { border-color: #939393; }
    .md-border-color-extra-medium-gray { border-color: #dbdbdb; }
    .md-border-color-medium-gray { border-color: #e4e4e4; }
    .md-border-color-extra-light-gray { border-color: #ededed; }
    .md-border-color-light-gray { border-color: #f5f5f5; }
    .md-border-color-light-pink { border-color: #862237; }
    .md-border-color-deep-pink { border-color: #ff214f; }
    .md-border-color-pink { border-color: #ff357c; }
    .md-border-color-fast-blue { border-color: #0038e3; }
    .md-border-color-orange { border-color: #ff6437; }
    .md-border-color-green { border-color: #45d690; }
    .md-border-color-golden { border-color: #d0ba6d; }
    .md-border-color-persian-blue { border-color: #0039CC; }
    .md-border-color-purple { border-color: #7342ac; }
    .md-border-color-parrot-green { border-color: #cee002; }
    .md-border-color-dark-red { border-color: #e12837; }

    /* transparent border */
    .md-border-color-transparent { border-color: transparent; }
    .md-border-color-black-transparent { border-color: rgba(0,0,0,.1); }
    .md-border-color-white-transparent { border-color: rgba(255,255,255,.1); }
    .md-border-color-golden-transparent { border-color: rgba(208, 186, 109, 0.2); }
    .md-border-color-pink-transparent { border-color: rgba(255, 33, 79, 0.45); }
    .md-border-color-dark-white-transparent { border-color: rgba(255,255,255,0.2); }
    .md-border-color-medium-white-transparent { border-color: rgba(255,255,255,0.4); }
    .md-border-color-full-dark-white-transparent { border-color: rgba(255,255,255,0.05); }
    .md-border-color-light-white-transparent { border-color: rgba(255,255,255,0.1); }
    .md-border-color-nero-transparent { border-color: rgba(25,25,25,0.1); }
    .md-border-color-extra-medium-gray-transparent { border-color: rgba(219,219,219,.04); }

    /* border style */
    .md-border-dotted { border-style: dotted !important; }
    .md-border-dashed { border-style: dashed !important; }
    .md-border-solid { border-style: solid !important; }
    .md-border-double { border-style: double !important; }
    .md-border-groove { border-style: groove !important; }
    .md-border-ridge { border-style: ridge !important; }
    .md-border-inset { border-style: inset !important; }
    .md-border-outset { border-style: outset !important; }
    .md-border-none { border-style: none !important; }
    .md-border-hidden { border-style: hidden !important; }
    .md-border-transperent { border-color: transparent !important; }

    /* swiper thumb */
    .single-product-thumb .slider-vertical { position: relative; }
    .single-product-thumb .slider-vertical .swiper-wrapper { flex-direction: row; }
    .single-product-thumb .slider-vertical .swiper-slide { width: 25%; margin-right: 10px; }
    .swiper-thumb-next-prev { top: 50%; bottom: inherit; z-index: 1; transform: translateY(-50%); -webkit-transform: translateY(-50%); -moz-transform: translateY(-50%); -ms-transform: translateY(-50%); }
    .swiper-thumb-next-prev .swiper-thumb-prev, .swiper-thumb-next-prev .swiper-thumb-next { width: 30px; height: 30px; line-height: 30px; position: absolute; transform: translateY(-50%) rotate(-90deg); -webkit-transform: translateY(-50%) rotate(-90deg); -moz-transform: translateY(-50%) rotate(-90deg); -ms-transform: translateY(-50%) rotate(-90deg); }
    .swiper-thumb-next-prev .swiper-thumb-prev { left: 15px; }
    .swiper-thumb-next-prev .swiper-thumb-next { right: 15px;; left: auto; }

    /* swiper vertical */  
    .slider-vertical .vh-100 { height: calc(100vh - 70px) !important }
    .slider-vertical .swiper-number-pagination { left: 68px; bottom: 50px; font-size: 16px; line-height: 22px; }
    .slider-vertical >.swiper-pagination-bullets { top: 50%; transform: translateY(-50%); -webkit-transform: translateY(-50%); -moz-transform: translateY(-50%); -ms-transform: translateY(-50%); }
    .slider-vertical >.swiper-pagination-bullets>.swiper-pagination-bullet { margin: 8px 0; }

    /* split slider */
    .home-split-portfolio { height: auto !important; }
    .home-split-portfolio .swiper-slide { -ms-flex-wrap: wrap;flex-wrap: wrap; height: auto; }
    .home-split-portfolio .swiper-slide .swiper-slide-l,.home-split-portfolio .swiper-slide .swiper-slide-r { opacity: 1; visibility: visible; -webkit-transform: translateY(0);-ms-transform: translateY(0);transform: translateY(0); width: 100% !important; -webkit-box-flex: 0;-ms-flex: 0 0 100%;flex: 0 0 100%; height: calc(100vh - 70px) !important;}
    .home-split-portfolio .swiper-wrapper { -ms-flex-wrap: wrap;flex-wrap: wrap; overflow: auto; }

    /* horizontal portfolio slider */
    .horizontal-portfolio-slider-main { -webkit-overflow-scrolling: touch; }
    .horizontal-portfolio-slider-main .horizontal-portfolio-slider .swiper-wrapper{ -ms-flex-wrap: wrap; flex-wrap: wrap; overflow: auto; transform: translate3d(0, 0px, 0px) !important; }

    /* grid */
    .grid.md-grid-6col li { width: 16.67%; }
    .grid.md-grid-6col li.grid-item-double { width: 33.33%; }
    .grid.md-grid-5col li { width: 20%; }
    .grid.md-grid-5col li.grid-item-double { width: 40%; }
    .grid.md-grid-4col li { width: 25%; }
    .grid.md-grid-4col li.grid-item-double { width: 50%; }
    .grid.md-grid-3col li { width: 33.33%; }
    .grid.md-grid-3col li.grid-item-double { width: 66.67%; }
    .grid.md-grid-2col li { width: 50%; }
    .grid.md-grid-2col li.grid-item-double { width: 100%; }
    .grid.md-grid-1col li { width: 100%; }

    /* team style 02 */
    .team-style-02 .social-icon a { margin: 0 8px; }

    /* blog side image */
    .blog-side-image .separator { display: none; }

    /* blog modern */
    .blog-modern .blog-post-image { margin-bottom: 120px; }
    .blog-modern .post-details { left: 30px; width: calc(100% - 60px); }

    /* outside box */
    .outside-box-left { margin-left: 0; }
    .outside-box-right { margin-right: 0; }
    .outside-box-bottom { margin-bottom: 0; }

    /* footer */
    footer .footer-horizontal-link li { margin-right: 20px; }

    /* magnific popup */
    .mfp-container { padding-left: 15px; padding-right: 15px; }
    button.mfp-close,.mfp-image-holder button.mfp-close, .mfp-iframe-holder button.mfp-close, .mfp-close, .mfp-close:active { top: 15px; right: 15px; }
  
    /* cart and checkout page */
    .cart-products thead { display: none; }
    .cart-products tr { border-bottom: 1px solid #e8e8e8; position: relative; display: block; padding-left: 95px; padding-bottom: 20px; background: none; margin-bottom: 20px; overflow: hidden; }
    .cart-products td { display: block; border: none; text-align: right; margin-bottom: 10px; padding: 0; }
    .cart-products td:last-child { margin: 0; }
    .cart-products td:before { content: attr(data-title); font-weight: 500; float: left; display: block; color: #232323; font-family: 'Poppins',sans-serif; font-size: 12px; }
    .cart-products .product-remove { width: auto; position: absolute; right: 0; margin: 0; padding: 0; text-align: right; top: 0; z-index: 11; border: none; }
    .cart-products .product-thumbnail { position: absolute; left: 0; width: 80px; display: inline-block; top: 0; overflow: hidden; text-align: left; }
    .cart-products .product-name { text-align: left; padding-right: 25px; }
    .cart-products .product-name .variation { float: none; display: block; }
    .total-price-table td{ text-align: right; }    
    .checkout-total-price-table td{ text-align: left; }

    /* application */
    .banner-bottom-right-images > img { bottom: 0; }

    /* yoga meditation */
    .home-yoga-meditation .outside-box-right .absolute-middle-center { left: 50%; }

    /* consulting */
    .home-consulting .popup-youtube.absolute-middle-center { left: 50%; }

    /* digital agency */
    .home-digital-agency .outside-box-text-right .text-extra-big-2 { font-size: 150px; }

    /* design agency */
    .home-design-agency .outside-box-left { margin-left: 0; }

    /* interactive portfolio */
    .home-interactive-portfolio .fullscreen-hover-box .interactive-title { font-size: 55px; line-height: 60px; }

    /* vertical portfolio */
    .home-vertical-portfolio .navbar { padding: 0 3.5rem;}

    /* freelancer */
    .home-freelancer .title-extra-large-heavy { line-height: 90px; }

    /* fashion shop */
    .home-fashion-shop .tp-tabs { left: 50% !important; -webkit-transform: translateX(-50%) !important;-ms-transform: translateX(-50%) !important;transform: translateX(-50%) !important; top: auto !important; bottom: 5% !important; }

    /* slider navigation style 04 */
    .slider-navigation-style-04.swiper-button-prev { left: -60px; }
    .slider-navigation-style-04.swiper-button-next { right: -60px; }

    /* content box image */
    .content-box-image { height: 300px; }

    /* interactive list style */
    .fullscreen-hover-list .hover-list-item .interactive-icon { top: 4px; }

    /* split portfolio */
    .home-split-portfolio.vh-100 { height: calc(100vh - 70px) !important; }
    .home-split-portfolio .title-large { font-size: 90px; line-height: 90px; }

    /* coming soon v2 */
    .show-notify-popup .mfp-container { padding-top: 30px; }
    
    /* landing page */
    .litho-landing-header .navbar-brand:after { display: none; }
    .litho-parallax-bg { width: 60%; right: -90px;}
    .customer-bg-section { display: none; }
    .landing-page-footer .title-large-2 { font-size: 54px; line-height: 64px; }
    .landing-page-auto-slider .swiper-container.swiper-auto-slide .swiper-slide { width: 60% !important; }
    
    /* text shadow */
    .md-no-text-shadow { text-shadow: none;}
    
    /* cookie message */
    .gdpr-container .gdpr-content { display: block; margin-right: 0; margin-bottom: 15px;}
        
    /* consulting */
    .home-dentist .popup-youtube.absolute-middle-center { left: 50%; }
}

@media only screen and (max-width: 991px) and (orientation: landscape) {
    /* home decor */
    .home-decor .zeus { transform: translateX(-50%) !important; top: 90% !important; }

    /*home spa salon*/
    .home-spa-salon .img img { width: 220px !important; }
    .home-spa-salon .rev_slider_wrapper { height: 450px !important; }

    /*coming soon*/
    .coming-soon .tp-fullwidth-forcer ,.coming-soon .rev_slider_wrapper { height: 450px !important; }
    .coming-soon .logo img { width: 100px !important; }
    .coming-soon .coming-soon-text { font-size: 12px !important; line-height: 14px !important; min-width: 300px !important; }
    .coming-soon .coming-soon-icon i { font-size: 11px !important; }

    /*home architecture*/
    .home-architecture .rev_slider_wrapper { min-height: 400px !important; }
    .architecture-overlap { position: relative !important; }
    .home-architecture .tparrows.tp-rightarrow { transform: matrix(1, 0, 0, 1, -67, -67) !important; }
    .home-architecture .tparrows.tp-leftarrow { transform: matrix(1, 0, 0, 1, -67, -134) !important; }
    .home-architecture .architecture-title { font-size: 34px !important; line-height: 34px !important; }

    /*home startup*/
    .home-startup .rev_slider_wrapper { min-height: 450px; }

    /*home yoga meditation*/
    .home-yoga-meditation .rev_slider_wrapper { height: 350px !important; }
    .home-yoga-meditation .yoga-woman, .home-yoga-meditation .yoga-leaves { bottom: -140px; }
    .home-yoga-meditation .yoga-woman img { height: 275px !important; width: auto !important; }
    .home-yoga-meditation .yoga-main-text { font-size: 60px !important; top: 25px !important; }
    .home-yoga-meditation .yoga-small-text { font-size: 15px !important; line-height: 20px !important; }
    .home-yoga-meditation .yoga-top-line { transform: matrix(1, 0, 0, 1, 0, 19) !important; min-width: 420px !important; }
    .home-yoga-meditation .yoga-bottom-line { transform: matrix(1, 0, 0, 1, 0, 36) !important; min-width: 400px !important; }

    /*home furniture shop*/
    .home-furniture-shop, .home-furniture-shop .rev_slider_wrapper { height: 350px !important; }
    
    /*home vertical portfolio*/
    .home-vertical-portfolio .slider-vertical { height: 100% !important; }
    
    /* maintenance */
    .maintenance .maintenance-title { font-size: 28px !important; line-height: 30px !important; padding-top: 20px !important; }

    /* height */
    .md-landscape-h-300px { height: 300px !important; }
    .md-landscape-h-350px { height: 350px !important; }
    .md-landscape-h-400px { height: 400px !important; }
    .md-landscape-h-450px { height: 450px !important; }
    .md-landscape-h-500px { height: 500px !important; }
    .md-landscape-h-520px { height: 520px !important; }
    .md-landscape-h-550px { height: 550px !important; }
    .md-landscape-h-580px { height: 580px !important; }
    .md-landscape-h-600px { height: 600px !important; }
    .md-landscape-h-650px { height: 650px !important; }
    .md-landscape-h-700px { height: 700px !important; }
}

@media only screen and (max-width: 991px) and (min-width: 767px) and (orientation: landscape) {
    .home-hotel-resort .rev_slider_wrapper { min-height: 450px !important; }
    .home-startup .rev_slider_wrapper { min-height: 450px; }
}

@media (max-width: 778px) {
    /* home decor */
    .home-decor .rev_slider_wrapper { height: 540px; }
    .home-decor .zeus { transform:matrix(1, 0, 0, 1, -350, -59) !important }

    /* architecture */
    .home-architecture .tparrows.tp-leftarrow { transform: matrix(1, 0, 0, 1, -67, -234) !important; }
    .home-architecture .tparrows.tp-rightarrow { transform: matrix(1, 0, 0, 1, -67, -301) !important; }
}

@media (max-width: 767px) {
    /* reset */
    html { font-size: 11px }
    .xs-center-col { float:none; margin-left:auto; margin-right:auto; }
    section, section.extra-big-section { padding: 50px 0; }
    section.big-section { padding:80px 0; }
    .center-col-style .custom-col { flex: 0 0 100%; max-width: 100%; margin: 8px 0; }
    .sm-last-order { order: 10; }

    /* typography */
    p { margin: 0 0 15px }

    /* text size */
    .title-extra-small { font-size: 20px; line-height: 30px; }
    .title-large { font-size: 45px; line-height: 43px; }
    .title-extra-large { font-size: 50px; line-height:50px }
    .text-extra-big { font-size: 100px; line-height: 100px; }
    .text-big { font-size: 90px; line-height: 90px; }

    /* lineheight */
    .sm-line-height-0px { line-height: 0px; }
    .sm-line-height-8px { line-height: 8px; }
    .sm-line-height-10px { line-height: 10px; }
    .sm-line-height-14px { line-height: 14px; }
    .sm-line-height-15px { line-height: 15px; }
    .sm-line-height-16px { line-height: 16px; }
    .sm-line-height-18px { line-height: 18px; }
    .sm-line-height-20px { line-height: 20px; }
    .sm-line-height-22px { line-height: 22px; }
    .sm-line-height-24px { line-height: 24px; }
    .sm-line-height-26px { line-height: 26px; }
    .sm-line-height-28px { line-height: 28px; }
    .sm-line-height-30px { line-height: 30px; }
    .sm-line-height-32px { line-height: 32px; }
    .sm-line-height-34px { line-height: 34px; }
    .sm-line-height-36px { line-height: 36px; }
    .sm-line-height-38px { line-height: 38px; }
    .sm-line-height-40px { line-height: 40px; }
    .sm-line-height-50px { line-height: 50px; }
    .sm-line-height-140px { line-height: 140px; }
    .sm-line-height-normal { line-height: normal; }

    /* letter spacing minus */
    .sm-letter-spacing-minus-1-half { letter-spacing: -0.50px; }
    .sm-letter-spacing-minus-1px { letter-spacing: -1px; }
    .sm-letter-spacing-minus-2px { letter-spacing: -2px; }
    .sm-letter-spacing-minus-3px { letter-spacing: -3px; }
    .sm-letter-spacing-minus-4px { letter-spacing: -4px; }
    .sm-letter-spacing-minus-5px { letter-spacing: -5px; }

    /* absolute middle center */
    .sm-absolute-middle-center { left: 50%; top: 50%; position: absolute; -ms-transform: translateX(-50%) translateY(-50%); -moz-transform: translateX(-50%) translateY(-50%); -webkit-transform: translateX(-50%) translateY(-50%); transform: translateX(-50%) translateY(-50%); }

    /* background image */
    .sm-background-image-none { background: inherit !important; }
    .sm-background-position-left { background-position: left center; }
    .sm-background-position-right { background-position: right center; }
    .sm-background-position-top { background-position: right top; }
    .sm-background-position-center { background-position: center; }
    .sm-background-position-left-top { background-position: left top; }

    /* box shadow */
    .sm-box-shadow-none { box-shadow: none; }

    /* video */
    .html-video-play .play-icon { padding-left: 5px; }
    .html-video-play .pause-icon { font-size: 22px; padding-left: 0; }

    /* margin */
    .sm-margin-one-all { margin:1%; }
    .sm-margin-two-all { margin:2%; }
    .sm-margin-three-all { margin:3%; }
    .sm-margin-four-all { margin:4%; }
    .sm-margin-five-all { margin:5%; }
    .sm-margin-six-all { margin:6%; }
    .sm-margin-seven-all { margin:7%; }
    .sm-margin-eight-all { margin:8%; }
    .sm-margin-nine-all { margin:9%; }
    .sm-margin-ten-all { margin:10%; }
    .sm-margin-eleven-all { margin:11%; }
    .sm-margin-twelve-all { margin:12%; }
    .sm-margin-thirteen-all { margin:13%; }
    .sm-margin-fourteen-all { margin:14%; }
    .sm-margin-fifteen-all { margin:15%; }
    .sm-margin-sixteen-all { margin:16%; }
    .sm-margin-seventeen-all { margin:17%; }
    .sm-margin-eighteen-all { margin:18%; }
    .sm-margin-nineteen-all { margin:19%; }
    .sm-margin-twenty-all { margin:20%; }
    .sm-margin-5px-all { margin:5px; }
    .sm-margin-10px-all { margin:10px; }
    .sm-margin-15px-all { margin:15px; }
    .sm-margin-20px-all { margin:20px; }
    .sm-margin-25px-all { margin:25px; }
    .sm-margin-30px-all { margin:30px; }
    .sm-margin-35px-all { margin:35px; }
    .sm-margin-40px-all { margin:40px; }
    .sm-margin-45px-all { margin:45px; }
    .sm-margin-50px-all { margin:50px; }
    .sm-margin-55px-all { margin:55px; }
    .sm-margin-60px-all { margin:60px; }
    .sm-margin-65px-all { margin:65px; }
    .sm-margin-70px-all { margin:70px; }
    .sm-margin-75px-all { margin:75px; }
    .sm-margin-80px-all { margin:80px; }
    .sm-margin-85px-all { margin:85px; }
    .sm-margin-90px-all { margin:90px; }
    .sm-margin-95px-all { margin:95px; }
    .sm-margin-100px-all { margin:100px; }
    .sm-margin-1-rem-all { margin: 1rem; }
    .sm-margin-1-half-rem-all { margin: 1.5rem; }
    .sm-margin-2-rem-all { margin: 2rem; }
    .sm-margin-2-half-rem-all { margin: 2.5rem; }
    .sm-margin-3-rem-all { margin: 3rem; }
    .sm-margin-3-half-rem-all { margin: 3.5rem; }
    .sm-margin-4-rem-all { margin: 4rem; }
    .sm-margin-4-half-rem-all { margin: 4.5rem; }
    .sm-margin-5-rem-all { margin: 5rem; }
    .sm-margin-5-half-rem-all { margin: 5.5rem; }
    .sm-margin-6-rem-all { margin: 6rem; }
    .sm-margin-6-half-rem-all { margin: 6.5rem; }
    .sm-margin-7-rem-all { margin: 7rem; }
    .sm-margin-7-half-rem-all { margin: 7.5rem; }
    .sm-margin-8-rem-all { margin: 8rem; }
    .sm-margin-8-half-rem-all { margin: 8.5rem; }
    .sm-margin-9-rem-all { margin: 9rem; }
    .sm-margin-9-half-rem-all { margin: 9.5rem; }
    .sm-margin-10-rem-all { margin: 10rem; }
    .sm-margin-10-half-rem-all { margin: 10.5rem; }

    /* margin top */
    .sm-margin-one-top { margin-top:1%; }
    .sm-margin-two-top { margin-top:2%; }
    .sm-margin-three-top { margin-top:3%; }
    .sm-margin-four-top { margin-top:4%; }
    .sm-margin-five-top { margin-top:5%; }
    .sm-margin-six-top { margin-top:6%; }
    .sm-margin-seven-top { margin-top:7%; }
    .sm-margin-eight-top { margin-top:8%; }
    .sm-margin-nine-top { margin-top:9%; }
    .sm-margin-ten-top { margin-top:10%; }
    .sm-margin-eleven-top { margin-top:11%; }
    .sm-margin-twelve-top { margin-top:12%; }
    .sm-margin-thirteen-top { margin-top:13%; }
    .sm-margin-fourteen-top { margin-top:14%; }
    .sm-margin-fifteen-top { margin-top:15%; }
    .sm-margin-sixteen-top { margin-top:16%; }
    .sm-margin-seventeen-top { margin-top:17%; }
    .sm-margin-eighteen-top { margin-top:18%; }
    .sm-margin-nineteen-top { margin-top:19%; }
    .sm-margin-twenty-top { margin-top:20%; }
    .sm-margin-5px-top { margin-top:5px; }
    .sm-margin-10px-top { margin-top:10px; }
    .sm-margin-15px-top { margin-top:15px; }
    .sm-margin-20px-top { margin-top:20px; }
    .sm-margin-25px-top { margin-top:25px; }
    .sm-margin-30px-top { margin-top:30px; }
    .sm-margin-35px-top { margin-top:35px; }
    .sm-margin-40px-top { margin-top:40px; }
    .sm-margin-45px-top { margin-top:45px; }
    .sm-margin-50px-top { margin-top:50px; }
    .sm-margin-55px-top { margin-top:55px; }
    .sm-margin-60px-top { margin-top:60px; }
    .sm-margin-65px-top { margin-top:65px; }
    .sm-margin-70px-top { margin-top:70px; }
    .sm-margin-75px-top { margin-top:75px; }
    .sm-margin-80px-top { margin-top:80px; }
    .sm-margin-85px-top { margin-top:85px; }
    .sm-margin-90px-top { margin-top:90px; }
    .sm-margin-95px-top { margin-top:95px; }
    .sm-margin-100px-top { margin-top:100px; }
    .sm-margin-1-rem-top { margin-top: 1rem; }
    .sm-margin-1-half-rem-top { margin-top: 1.5rem; }
    .sm-margin-2-rem-top { margin-top: 2rem; }
    .sm-margin-2-half-rem-top { margin-top: 2.5rem; }
    .sm-margin-3-rem-top { margin-top: 3rem; }
    .sm-margin-3-half-rem-top { margin-top: 3.5rem; }
    .sm-margin-4-rem-top { margin-top: 4rem; }
    .sm-margin-4-half-rem-top { margin-top: 4.5rem; }
    .sm-margin-5-rem-top { margin-top: 5rem; }
    .sm-margin-5-half-rem-top { margin-top: 5.5rem; }
    .sm-margin-6-rem-top { margin-top: 6rem; }
    .sm-margin-6-half-rem-top { margin-top: 6.5rem; }
    .sm-margin-7-rem-top { margin-top: 7rem; }
    .sm-margin-7-half-rem-top { margin-top: 7.5rem; }
    .sm-margin-8-rem-top { margin-top: 8rem; }
    .sm-margin-8-half-rem-top { margin-top: 8.5rem; }
    .sm-margin-9-rem-top { margin-top: 9rem; }
    .sm-margin-9-half-rem-top { margin-top: 9.5rem; }
    .sm-margin-10-rem-top { margin-top: 10rem; }
    .sm-margin-10-half-rem-top { margin-top: 10.5rem; }

    /* margin bottom */
    .sm-margin-one-bottom { margin-bottom:1%; }
    .sm-margin-two-bottom { margin-bottom:2%; }
    .sm-margin-three-bottom { margin-bottom:3%; }
    .sm-margin-four-bottom { margin-bottom:4%; }
    .sm-margin-five-bottom { margin-bottom:5%; }
    .sm-margin-six-bottom { margin-bottom:6%; }
    .sm-margin-seven-bottom { margin-bottom:7%; }
    .sm-margin-eight-bottom { margin-bottom:8%; }
    .sm-margin-nine-bottom { margin-bottom:9%; }
    .sm-margin-ten-bottom { margin-bottom:10%; }
    .sm-margin-eleven-bottom { margin-bottom:11%; }
    .sm-margin-twelve-bottom { margin-bottom:12%; }
    .sm-margin-thirteen-bottom { margin-bottom:13%; }
    .sm-margin-fourteen-bottom { margin-bottom:14%; }
    .sm-margin-fifteen-bottom { margin-bottom:15%; }
    .sm-margin-sixteen-bottom { margin-bottom:16%; }
    .sm-margin-seventeen-bottom { margin-bottom:17%; }
    .sm-margin-eighteen-bottom { margin-bottom:18%; }
    .sm-margin-nineteen-bottom { margin-bottom:19%; }
    .sm-margin-twenty-bottom { margin-bottom:20%; }
    .sm-margin-5px-bottom { margin-bottom:5px; }
    .sm-margin-10px-bottom { margin-bottom:10px; }
    .sm-margin-15px-bottom { margin-bottom:15px; }
    .sm-margin-20px-bottom { margin-bottom:20px; }
    .sm-margin-25px-bottom { margin-bottom:25px; }
    .sm-margin-30px-bottom { margin-bottom:30px; }
    .sm-margin-35px-bottom { margin-bottom:35px; }
    .sm-margin-40px-bottom { margin-bottom:40px; }
    .sm-margin-45px-bottom { margin-bottom:45px; }
    .sm-margin-50px-bottom { margin-bottom:50px; }
    .sm-margin-55px-bottom { margin-bottom:55px; }
    .sm-margin-60px-bottom { margin-bottom:60px; }
    .sm-margin-65px-bottom { margin-bottom:65px; }
    .sm-margin-70px-bottom { margin-bottom:70px; }
    .sm-margin-75px-bottom { margin-bottom:75px; }
    .sm-margin-80px-bottom { margin-bottom:80px; }
    .sm-margin-85px-bottom { margin-bottom:85px; }
    .sm-margin-90px-bottom { margin-bottom:90px; }
    .sm-margin-95px-bottom { margin-bottom:95px; }
    .sm-margin-100px-bottom { margin-bottom:100px; }
    .sm-margin-1-rem-bottom { margin-bottom: 1rem; }
    .sm-margin-1-half-rem-bottom { margin-bottom: 1.5rem; }
    .sm-margin-2-rem-bottom { margin-bottom: 2rem; }
    .sm-margin-2-half-rem-bottom { margin-bottom: 2.5rem; }
    .sm-margin-3-rem-bottom { margin-bottom: 3rem; }
    .sm-margin-3-half-rem-bottom { margin-bottom: 3.5rem; }
    .sm-margin-4-rem-bottom { margin-bottom: 4rem; }
    .sm-margin-4-half-rem-bottom { margin-bottom: 4.5rem; }
    .sm-margin-5-rem-bottom { margin-bottom: 5rem; }
    .sm-margin-5-half-rem-bottom { margin-bottom: 5.5rem; }
    .sm-margin-6-rem-bottom { margin-bottom: 6rem; }
    .sm-margin-6-half-rem-bottom { margin-bottom: 6.5rem; }
    .sm-margin-7-rem-bottom { margin-bottom: 7rem; }
    .sm-margin-7-half-rem-bottom { margin-bottom: 7.5rem; }
    .sm-margin-8-rem-bottom { margin-bottom: 8rem; }
    .sm-margin-8-half-rem-bottom { margin-bottom: 8.5rem; }
    .sm-margin-9-rem-bottom { margin-bottom: 9rem; }
    .sm-margin-9-half-rem-bottom { margin-bottom: 9.5rem; }
    .sm-margin-10-rem-bottom { margin-bottom: 10rem; }
    .sm-margin-10-half-rem-bottom { margin-bottom: 10.5rem; }

    /* margin right */
    .sm-margin-one-right { margin-right:1%; }
    .sm-margin-two-right { margin-right:2%; }
    .sm-margin-three-right { margin-right:3%; }
    .sm-margin-four-right { margin-right:4%; }
    .sm-margin-five-right { margin-right:5%; }
    .sm-margin-six-right { margin-right:6%; }
    .sm-margin-seven-right { margin-right:7%; }
    .sm-margin-eight-right { margin-right:8%; }
    .sm-margin-nine-right { margin-right:9%; }
    .sm-margin-ten-right { margin-right:10%; }
    .sm-margin-eleven-right { margin-right:11%; }
    .sm-margin-twelve-right { margin-right:12%; }
    .sm-margin-thirteen-right { margin-right:13%; }
    .sm-margin-fourteen-right { margin-right:14%; }
    .sm-margin-fifteen-right { margin-right:15%; }
    .sm-margin-sixteen-right { margin-right:16%; }
    .sm-margin-seventeen-right { margin-right:17%; }
    .sm-margin-eighteen-right { margin-right:18%; }
    .sm-margin-nineteen-right { margin-right:19%; }
    .sm-margin-twenty-right { margin-right:20%; }
    .sm-margin-5px-right { margin-right:5px; }
    .sm-margin-10px-right { margin-right:10px; }
    .sm-margin-15px-right { margin-right:15px; }
    .sm-margin-20px-right { margin-right:20px; }
    .sm-margin-25px-right { margin-right:25px; }
    .sm-margin-30px-right { margin-right:30px; }
    .sm-margin-35px-right { margin-right:35px; }
    .sm-margin-40px-right { margin-right:40px; }
    .sm-margin-45px-right { margin-right:45px; }
    .sm-margin-50px-right { margin-right:50px; }
    .sm-margin-55px-right { margin-right:55px; }
    .sm-margin-60px-right { margin-right:60px; }
    .sm-margin-65px-right { margin-right:65px; }
    .sm-margin-70px-right { margin-right:70px; }
    .sm-margin-75px-right { margin-right:75px; }
    .sm-margin-80px-right { margin-right:80px; }
    .sm-margin-85px-right { margin-right:85px; }
    .sm-margin-90px-right { margin-right:90px; }
    .sm-margin-95px-right { margin-right:95px; }
    .sm-margin-100px-right { margin-right:100px; }
    .sm-margin-1-rem-right { margin-right: 1rem; }
    .sm-margin-1-half-rem-right { margin-right: 1.5rem; }
    .sm-margin-2-rem-right { margin-right: 2rem; }
    .sm-margin-2-half-rem-right { margin-right: 2.5rem; }
    .sm-margin-3-rem-right { margin-right: 3rem; }
    .sm-margin-3-half-rem-right { margin-right: 3.5rem; }
    .sm-margin-4-rem-right { margin-right: 4rem; }
    .sm-margin-4-half-rem-right { margin-right: 4.5rem; }
    .sm-margin-5-rem-right { margin-right: 5rem; }
    .sm-margin-5-half-rem-right { margin-right: 5.5rem; }
    .sm-margin-6-rem-right { margin-right: 6rem; }
    .sm-margin-6-half-rem-right { margin-right: 6.5rem; }
    .sm-margin-7-rem-right { margin-right: 7rem; }
    .sm-margin-7-half-rem-right { margin-right: 7.5rem; }
    .sm-margin-8-rem-right { margin-right: 8rem; }
    .sm-margin-8-half-rem-right { margin-right: 8.5rem; }
    .sm-margin-9-rem-right { margin-right: 9rem; }
    .sm-margin-9-half-rem-right { margin-right: 9.5rem; }
    .sm-margin-10-rem-right { margin-right: 10rem; }
    .sm-margin-10-half-rem-right { margin-right: 10.5rem; }

    /* margin left */
    .sm-margin-one-left { margin-left:1%; }
    .sm-margin-two-left { margin-left:2%; }
    .sm-margin-three-left { margin-left:3%; }
    .sm-margin-four-left { margin-left:4%; }
    .sm-margin-five-left { margin-left:5%; }
    .sm-margin-six-left { margin-left:6%; }
    .sm-margin-seven-left { margin-left:7%; }
    .sm-margin-eight-left { margin-left:8%; }
    .sm-margin-nine-left { margin-left:9%; }
    .sm-margin-ten-left { margin-left:10%; }
    .sm-margin-eleven-left { margin-left:11%; }
    .sm-margin-twelve-left { margin-left:12%; }
    .sm-margin-thirteen-left { margin-left:13%; }
    .sm-margin-fourteen-left { margin-left:14%; }
    .sm-margin-fifteen-left { margin-left:15%; }
    .sm-margin-sixteen-left { margin-left:16%; }
    .sm-margin-seventeen-left { margin-left:17%; }
    .sm-margin-eighteen-left { margin-left:18%; }
    .sm-margin-nineteen-left { margin-left:19%; }
    .sm-margin-twenty-left { margin-left:20%; }
    .sm-margin-5px-left { margin-left:5px; }
    .sm-margin-10px-left { margin-left:10px; }
    .sm-margin-15px-left { margin-left:15px; }
    .sm-margin-20px-left { margin-left:20px; }
    .sm-margin-25px-left { margin-left:25px; }
    .sm-margin-30px-left { margin-left:30px; }
    .sm-margin-35px-left { margin-left:35px; }
    .sm-margin-40px-left { margin-left:40px; }
    .sm-margin-45px-left { margin-left:45px; }
    .sm-margin-50px-left { margin-left:50px; }
    .sm-margin-55px-left { margin-left:55px; }
    .sm-margin-60px-left { margin-left:60px; }
    .sm-margin-65px-left { margin-left:65px; }
    .sm-margin-70px-left { margin-left:70px; }
    .sm-margin-75px-left { margin-left:75px; }
    .sm-margin-80px-left { margin-left:80px; }
    .sm-margin-85px-left { margin-left:85px; }
    .sm-margin-90px-left { margin-left:90px; }
    .sm-margin-95px-left { margin-left:95px; }
    .sm-margin-100px-left { margin-left:100px; }
    .sm-margin-1-rem-left { margin-left: 1rem; }
    .sm-margin-1-half-rem-left { margin-left: 1.5rem; }
    .sm-margin-2-rem-left { margin-left: 2rem; }
    .sm-margin-2-half-rem-left { margin-left: 2.5rem; }
    .sm-margin-3-rem-left { margin-left: 3rem; }
    .sm-margin-3-half-rem-left { margin-left: 3.5rem; }
    .sm-margin-4-rem-left { margin-left: 4rem; }
    .sm-margin-4-half-rem-left { margin-left: 4.5rem; }
    .sm-margin-5-rem-left { margin-left: 5rem; }
    .sm-margin-5-half-rem-left { margin-left: 5.5rem; }
    .sm-margin-6-rem-left { margin-left: 6rem; }
    .sm-margin-6-half-rem-left { margin-left: 6.5rem; }
    .sm-margin-7-rem-left { margin-left: 7rem; }
    .sm-margin-7-half-rem-left { margin-left: 7.5rem; }
    .sm-margin-8-rem-left { margin-left: 8rem; }
    .sm-margin-8-half-rem-left { margin-left: 8.5rem; }
    .sm-margin-9-rem-left { margin-left: 9rem; }
    .sm-margin-9-half-rem-left { margin-left: 9.5rem; }
    .sm-margin-10-rem-left { margin-left: 10rem; }
    .sm-margin-10-half-rem-left { margin-left: 10.5rem; }

    /* margin left right */
    .sm-margin-one-lr { margin-left:1%; margin-right:1%; }
    .sm-margin-two-lr { margin-left:2%; margin-right:2%; }
    .sm-margin-three-lr { margin-left:3%; margin-right:3%; }
    .sm-margin-four-lr { margin-left:4%; margin-right:4%; }
    .sm-margin-five-lr { margin-left:5%; margin-right:5%; }
    .sm-margin-six-lr { margin-left:6%; margin-right:6%; }
    .sm-margin-seven-lr { margin-left:7%; margin-right:7%; }
    .sm-margin-eight-lr { margin-left:8%; margin-right:8%; }
    .sm-margin-nine-lr { margin-left:9%; margin-right:9%; }
    .sm-margin-ten-lr { margin-left:10%; margin-right:10%; }
    .sm-margin-eleven-lr { margin-left:11%; margin-right:11%; }
    .sm-margin-twelve-lr { margin-left:12%; margin-right:12%; }
    .sm-margin-thirteen-lr { margin-left:13%; margin-right:13%; }
    .sm-margin-fourteen-lr { margin-left:14%; margin-right:14%; }
    .sm-margin-fifteen-lr { margin-left:15%; margin-right:15%; }
    .sm-margin-sixteen-lr { margin-left:16%; margin-right:16%; }
    .sm-margin-seventeen-lr { margin-left:17%; margin-right:17%; }
    .sm-margin-eighteen-lr { margin-left:18%; margin-right:18%; }
    .sm-margin-nineteen-lr { margin-left:19%; margin-right:19%; }
    .sm-margin-twenty-lr { margin-left:20%; margin-right:20%; }
    .sm-margin-5px-lr { margin-left:5px; margin-right:5px; }
    .sm-margin-10px-lr { margin-left:10px; margin-right:10px; }
    .sm-margin-15px-lr { margin-left:15px; margin-right:15px; }
    .sm-margin-20px-lr { margin-left:20px; margin-right:20px; }
    .sm-margin-25px-lr { margin-left:25px; margin-right:25px; }
    .sm-margin-30px-lr { margin-left:30px; margin-right:30px; }
    .sm-margin-35px-lr { margin-left:35px; margin-right:35px; }
    .sm-margin-40px-lr { margin-left:40px; margin-right:40px; }
    .sm-margin-45px-lr { margin-left:45px; margin-right:45px; }
    .sm-margin-50px-lr { margin-left:50px; margin-right:50px; }
    .sm-margin-55px-lr { margin-left:55px; margin-right:55px; }
    .sm-margin-60px-lr { margin-left:60px; margin-right:60px; }
    .sm-margin-65px-lr { margin-left:65px; margin-right:65px; }
    .sm-margin-70px-lr { margin-left:70px; margin-right:70px; }
    .sm-margin-75px-lr { margin-left:75px; margin-right:75px; }
    .sm-margin-80px-lr { margin-left:80px; margin-right:80px; }
    .sm-margin-85px-lr { margin-left:85px; margin-right:85px; }
    .sm-margin-90px-lr { margin-left:90px; margin-right:90px; }
    .sm-margin-95px-lr { margin-left:95px; margin-right:95px; }
    .sm-margin-100px-lr { margin-left:100px; margin-right:100px; }
    .sm-margin-1-rem-lr { margin-left: 1rem; margin-right: 1rem; }
    .sm-margin-1-half-rem-lr { margin-left: 1.5rem; margin-right: 1.5rem; }
    .sm-margin-2-rem-lr { margin-left: 2rem; margin-right: 2rem; }
    .sm-margin-2-half-rem-lr { margin-left: 2.5rem; margin-right: 2.5rem; }
    .sm-margin-3-rem-lr { margin-left: 3rem; margin-right: 3rem; }
    .sm-margin-3-half-rem-lr { margin-left: 3.5rem; margin-right: 3.5rem; }
    .sm-margin-4-rem-lr { margin-left: 4rem; margin-right: 4rem; }
    .sm-margin-4-half-rem-lr { margin-left: 4.5rem; margin-right: 4.5rem; }
    .sm-margin-5-rem-lr { margin-left: 5rem; margin-right: 5rem; }
    .sm-margin-5-half-rem-lr { margin-left: 5.5rem; margin-right: 5.5rem; }
    .sm-margin-6-rem-lr { margin-left: 6rem; margin-right: 6rem; }
    .sm-margin-6-half-rem-lr { margin-left: 6.5rem; margin-right: 6.5rem; }
    .sm-margin-7-rem-lr { margin-left: 7rem; margin-right: 7rem; }
    .sm-margin-7-half-rem-lr { margin-left: 7.5rem; margin-right: 7.5rem; }
    .sm-margin-8-rem-lr { margin-left: 8rem; margin-right: 8rem; }
    .sm-margin-8-half-rem-lr { margin-left: 8.5rem; margin-right: 8.5rem; }
    .sm-margin-9-rem-lr { margin-left: 9rem; margin-right: 9rem; }
    .sm-margin-9-half-rem-lr { margin-left: 9.5rem; margin-right: 9.5rem; }
    .sm-margin-10-rem-lr { margin-left: 10rem; margin-right: 10rem; }
    .sm-margin-10-half-rem-lr { margin-left: 10.5rem; margin-right: 10.5rem; }

    /* margin top bottom */
    .sm-margin-one-tb { margin-top:1%; margin-bottom:1%; }
    .sm-margin-two-tb { margin-top:2%; margin-bottom:2%; }
    .sm-margin-three-tb { margin-top:3%; margin-bottom:3%; }
    .sm-margin-four-tb { margin-top:4%; margin-bottom:4%; }
    .sm-margin-five-tb { margin-top:5%; margin-bottom:5%; }
    .sm-margin-six-tb { margin-top:6%; margin-bottom:6%; }
    .sm-margin-seven-tb { margin-top:7%; margin-bottom:7%; }
    .sm-margin-eight-tb { margin-top:8%; margin-bottom:8%; }
    .sm-margin-nine-tb { margin-top:9%; margin-bottom:9%; }
    .sm-margin-ten-tb { margin-top:10%; margin-bottom:10%; }
    .sm-margin-eleven-tb { margin-top:11%; margin-bottom:11%; }
    .sm-margin-twelve-tb { margin-top:12%; margin-bottom:12%; }
    .sm-margin-thirteen-tb { margin-top:13%; margin-bottom:13%; }
    .sm-margin-fourteen-tb { margin-top:14%; margin-bottom:14%; }
    .sm-margin-fifteen-tb { margin-top:15%; margin-bottom:15%; }
    .sm-margin-sixteen-tb { margin-top:16%; margin-bottom:16%; }
    .sm-margin-seventeen-tb { margin-top:17%; margin-bottom:17%; }
    .sm-margin-eighteen-tb { margin-top:18%; margin-bottom:18%; }
    .sm-margin-nineteen-tb { margin-top:19%; margin-bottom:19%; }
    .sm-margin-twenty-tb { margin-top:20%; margin-bottom:20%; }
    .sm-margin-5px-tb { margin-top:5px; margin-bottom:5px; }
    .sm-margin-10px-tb { margin-top:10px; margin-bottom:10px; }
    .sm-margin-15px-tb { margin-top:15px; margin-bottom:15px; }
    .sm-margin-20px-tb { margin-top:20px; margin-bottom:20px; }
    .sm-margin-25px-tb { margin-top:25px; margin-bottom:25px; }
    .sm-margin-30px-tb { margin-top:30px; margin-bottom:30px; }
    .sm-margin-35px-tb { margin-top:35px; margin-bottom:35px; }
    .sm-margin-40px-tb { margin-top:40px; margin-bottom:40px; }
    .sm-margin-45px-tb { margin-top:45px; margin-bottom:45px; }
    .sm-margin-50px-tb { margin-top:50px; margin-bottom:50px; }
    .sm-margin-55px-tb { margin-top:55px; margin-bottom:55px; }
    .sm-margin-60px-tb { margin-top:60px; margin-bottom:60px; }
    .sm-margin-65px-tb { margin-top:65px; margin-bottom:65px; }
    .sm-margin-70px-tb { margin-top:70px; margin-bottom:70px; }
    .sm-margin-75px-tb { margin-top:75px; margin-bottom:75px; }
    .sm-margin-80px-tb { margin-top:80px; margin-bottom:80px; }
    .sm-margin-85px-tb { margin-top:85px; margin-bottom:85px; }
    .sm-margin-90px-tb { margin-top:90px; margin-bottom:90px; }
    .sm-margin-95px-tb { margin-top:95px; margin-bottom:95px; }
    .sm-margin-100px-tb { margin-top:100px; margin-bottom:100px; }
    .sm-margin-1-rem-tb { margin-top: 1rem; margin-bottom: 1rem; }
    .sm-margin-1-half-rem-tb { margin-top: 1.5rem; margin-bottom: 1.5rem; }
    .sm-margin-2-rem-tb { margin-top: 2rem; margin-bottom: 2rem; }
    .sm-margin-2-half-rem-tb { margin-top: 2.5rem; margin-bottom: 2.5rem; }
    .sm-margin-3-rem-tb { margin-top: 3rem; margin-bottom: 3rem; }
    .sm-margin-3-half-rem-tb { margin-top: 3.5rem; margin-bottom: 3.5rem; }
    .sm-margin-4-rem-tb { margin-top: 4rem; margin-bottom: 4rem; }
    .sm-margin-4-half-rem-tb { margin-top: 4.5rem; margin-bottom: 4.5rem; }
    .sm-margin-5-rem-tb { margin-top: 5rem; margin-bottom: 5rem; }
    .sm-margin-5-half-rem-tb { margin-top: 5.5rem; margin-bottom: 5.5rem; }
    .sm-margin-6-rem-tb { margin-top: 6rem; margin-bottom: 6rem; }
    .sm-margin-6-half-rem-tb { margin-top: 6.5rem; margin-bottom: 6.5rem; }
    .sm-margin-7-rem-tb { margin-top: 7rem; margin-bottom: 7rem; }
    .sm-margin-7-half-rem-tb { margin-top: 7.5rem; margin-bottom: 7.5rem; }
    .sm-margin-8-rem-tb { margin-top: 8rem; margin-bottom: 8rem; }
    .sm-margin-8-half-rem-tb { margin-top: 8.5rem; margin-bottom: 8.5rem; }
    .sm-margin-9-rem-tb { margin-top: 9rem; margin-bottom: 9rem; }
    .sm-margin-9-half-rem-tb { margin-top: 9.5rem; margin-bottom: 9.5rem; }
    .sm-margin-10-rem-tb { margin-top: 10rem; margin-bottom: 10rem; }
    .sm-margin-10-half-rem-tb { margin-top: 10.5rem; margin-bottom: 10.5rem; }

    .sm-margin-auto-lr { margin-left: auto !important; margin-right: auto !important; }
    .sm-margin-auto { margin: auto; }
    .sm-no-margin { margin: 0 !important; }
    .sm-no-margin-top { margin-top: 0 !important; }
    .sm-no-margin-bottom { margin-bottom: 0 !important; }
    .sm-no-margin-left { margin-left: 0 !important; }
    .sm-no-margin-right { margin-right: 0 !important; }
    .sm-no-margin-tb { margin-top: 0 !important; margin-bottom: 0 !important; }
    .sm-no-margin-lr { margin-right: 0 !important; margin-left: 0 !important; }

    /* padding */
    .sm-padding-one-all { padding:1%; }
    .sm-padding-two-all { padding:2%; }
    .sm-padding-three-all { padding:3%; }
    .sm-padding-four-all { padding:4%; }
    .sm-padding-five-all { padding:5%; }
    .sm-padding-six-all { padding:6%; }
    .sm-padding-seven-all { padding:7%; }
    .sm-padding-eight-all { padding:8%; }
    .sm-padding-nine-all { padding:9%; }
    .sm-padding-ten-all { padding:10%; }
    .sm-padding-eleven-all { padding:11%; }
    .sm-padding-twelve-all { padding:12%; }
    .sm-padding-thirteen-all { padding:13%; }
    .sm-padding-fourteen-all { padding:14%; }
    .sm-padding-fifteen-all { padding:15%; }
    .sm-padding-sixteen-all { padding:16%; }
    .sm-padding-seventeen-all { padding:17%; }
    .sm-padding-eighteen-all { padding:18%; }
    .sm-padding-nineteen-all { padding:19%; }
    .sm-padding-twenty-all { padding:20%; }
    .sm-padding-5px-all { padding:5px; }
    .sm-padding-10px-all { padding:10px; }
    .sm-padding-15px-all { padding:15px; }
    .sm-padding-20px-all { padding:20px; }
    .sm-padding-25px-all { padding:25px; }
    .sm-padding-30px-all { padding:30px; }
    .sm-padding-35px-all { padding:35px; }
    .sm-padding-40px-all { padding:40px; }
    .sm-padding-45px-all { padding:45px; }
    .sm-padding-50px-all { padding:50px; }
    .sm-padding-55px-all { padding:55px; }
    .sm-padding-60px-all { padding:60px; }
    .sm-padding-65px-all { padding:65px; }
    .sm-padding-70px-all { padding:70px; }
    .sm-padding-75px-all { padding:75px; }
    .sm-padding-80px-all { padding:80px; }
    .sm-padding-85px-all { padding:85px; }
    .sm-padding-90px-all { padding:90px; }
    .sm-padding-95px-all { padding:95px; }
    .sm-padding-100px-all { padding:100px; }
    .sm-padding-1-rem-all { padding: 1rem; }
    .sm-padding-1-half-rem-all { padding: 1.5rem; }
    .sm-padding-2-rem-all { padding: 2rem; }
    .sm-padding-2-half-rem-all { padding: 2.5rem; }
    .sm-padding-3-rem-all { padding: 3rem; }
    .sm-padding-3-half-rem-all { padding: 3.5rem; }
    .sm-padding-4-rem-all { padding: 4rem; }
    .sm-padding-4-half-rem-all { padding: 4.5rem; }
    .sm-padding-5-rem-all { padding: 5rem; }
    .sm-padding-5-half-rem-all { padding: 5.5rem; }
    .sm-padding-6-rem-all { padding: 6rem; }
    .sm-padding-6-half-rem-all { padding: 6.5rem; }
    .sm-padding-7-rem-all { padding: 7rem; }
    .sm-padding-7-half-rem-all { padding: 7.5rem; }
    .sm-padding-8-rem-all { padding: 8rem; }
    .sm-padding-8-half-rem-all { padding: 8.5rem; }
    .sm-padding-9-rem-all { padding: 9rem; }
    .sm-padding-9-half-rem-all { padding: 9.5rem; }
    .sm-padding-10-rem-all { padding: 10rem; }
    .sm-padding-10-half-rem-all { padding: 10.5rem; }

    /* padding top */
    .sm-padding-one-top { padding-top:1%; }
    .sm-padding-two-top { padding-top:2%; }
    .sm-padding-three-top { padding-top:3%; }
    .sm-padding-four-top { padding-top:4%; }
    .sm-padding-five-top { padding-top:5%; }
    .sm-padding-six-top { padding-top:6%; }
    .sm-padding-seven-top { padding-top:7%; }
    .sm-padding-eight-top { padding-top:8%; }
    .sm-padding-nine-top { padding-top:9%; }
    .sm-padding-ten-top { padding-top:10%; }
    .sm-padding-eleven-top { padding-top:11%; }
    .sm-padding-twelve-top { padding-top:12%; }
    .sm-padding-thirteen-top { padding-top:13%; }
    .sm-padding-fourteen-top { padding-top:14%; }
    .sm-padding-fifteen-top { padding-top:15%; }
    .sm-padding-sixteen-top { padding-top:16%; }
    .sm-padding-seventeen-top { padding-top:17%; }
    .sm-padding-eighteen-top { padding-top:18%; }
    .sm-padding-nineteen-top { padding-top:19%; }
    .sm-padding-twenty-top { padding-top:20%; }
    .sm-padding-5px-top { padding-top:5px; }
    .sm-padding-10px-top { padding-top:10px; }
    .sm-padding-15px-top { padding-top:15px; }
    .sm-padding-20px-top { padding-top:20px; }
    .sm-padding-25px-top { padding-top:25px; }
    .sm-padding-30px-top { padding-top:30px; }
    .sm-padding-35px-top { padding-top:35px; }
    .sm-padding-40px-top { padding-top:40px; }
    .sm-padding-45px-top { padding-top:45px; }
    .sm-padding-50px-top { padding-top:50px; }
    .sm-padding-55px-top { padding-top:55px; }
    .sm-padding-60px-top { padding-top:60px; }
    .sm-padding-65px-top { padding-top:65px; }
    .sm-padding-70px-top { padding-top:70px; }
    .sm-padding-75px-top { padding-top:75px; }
    .sm-padding-80px-top { padding-top:80px; }
    .sm-padding-85px-top { padding-top:85px; }
    .sm-padding-90px-top { padding-top:90px; }
    .sm-padding-95px-top { padding-top:95px; }
    .sm-padding-100px-top { padding-top:100px; }
    .sm-padding-1-rem-top { padding-top: 1rem; }
    .sm-padding-1-half-rem-top { padding-top: 1.5rem; }
    .sm-padding-2-rem-top { padding-top: 2rem; }
    .sm-padding-2-half-rem-top { padding-top: 2.5rem; }
    .sm-padding-3-rem-top { padding-top: 3rem; }
    .sm-padding-3-half-rem-top { padding-top: 3.5rem; }
    .sm-padding-4-rem-top { padding-top: 4rem; }
    .sm-padding-4-half-rem-top { padding-top: 4.5rem; }
    .sm-padding-5-rem-top { padding-top: 5rem; }
    .sm-padding-5-half-rem-top { padding-top: 5.5rem; }
    .sm-padding-6-rem-top { padding-top: 6rem; }
    .sm-padding-6-half-rem-top { padding-top: 6.5rem; }
    .sm-padding-7-rem-top { padding-top: 7rem; }
    .sm-padding-7-half-rem-top { padding-top: 7.5rem; }
    .sm-padding-8-rem-top { padding-top: 8rem; }
    .sm-padding-8-half-rem-top { padding-top: 8.5rem; }
    .sm-padding-9-rem-top { padding-top: 9rem; }
    .sm-padding-9-half-rem-top { padding-top: 9.5rem; }
    .sm-padding-10-rem-top { padding-top: 10rem; }
    .sm-padding-10-half-rem-top { padding-top: 10.5rem; }

    /* padding bottom */
    .sm-padding-one-bottom { padding-bottom:1%; }
    .sm-padding-two-bottom { padding-bottom:2%; }
    .sm-padding-three-bottom { padding-bottom:3%; }
    .sm-padding-four-bottom { padding-bottom:4%; }
    .sm-padding-five-bottom { padding-bottom:5%; }
    .sm-padding-six-bottom { padding-bottom:6%; }
    .sm-padding-seven-bottom { padding-bottom:7%; }
    .sm-padding-eight-bottom { padding-bottom:8%; }
    .sm-padding-nine-bottom { padding-bottom:9%; }
    .sm-padding-ten-bottom { padding-bottom:10%; }
    .sm-padding-eleven-bottom { padding-bottom:11%; }
    .sm-padding-twelve-bottom { padding-bottom:12%; }
    .sm-padding-thirteen-bottom { padding-bottom:13%; }
    .sm-padding-fourteen-bottom { padding-bottom:14%; }
    .sm-padding-fifteen-bottom { padding-bottom:15%; }
    .sm-padding-sixteen-bottom { padding-bottom:16%; }
    .sm-padding-seventeen-bottom { padding-bottom:17%; }
    .sm-padding-eighteen-bottom { padding-bottom:18%; }
    .sm-padding-nineteen-bottom { padding-bottom:19%; }
    .sm-padding-twenty-bottom { padding-bottom:20%; }
    .sm-padding-5px-bottom { padding-bottom:5px; }
    .sm-padding-10px-bottom { padding-bottom:10px; }
    .sm-padding-15px-bottom { padding-bottom:15px; }
    .sm-padding-20px-bottom { padding-bottom:20px; }
    .sm-padding-25px-bottom { padding-bottom:25px; }
    .sm-padding-30px-bottom { padding-bottom:30px; }
    .sm-padding-35px-bottom { padding-bottom:35px; }
    .sm-padding-40px-bottom { padding-bottom:40px; }
    .sm-padding-45px-bottom { padding-bottom:45px; }
    .sm-padding-50px-bottom { padding-bottom:50px; }
    .sm-padding-55px-bottom { padding-bottom:55px; }
    .sm-padding-60px-bottom { padding-bottom:60px; }
    .sm-padding-65px-bottom { padding-bottom:65px; }
    .sm-padding-70px-bottom { padding-bottom:70px; }
    .sm-padding-75px-bottom { padding-bottom:75px; }
    .sm-padding-80px-bottom { padding-bottom:80px; }
    .sm-padding-85px-bottom { padding-bottom:85px; }
    .sm-padding-90px-bottom { padding-bottom:90px; }
    .sm-padding-95px-bottom { padding-bottom:95px; }
    .sm-padding-100px-bottom { padding-bottom:100px; }
    .sm-padding-1-rem-bottom { padding-bottom: 1rem; }
    .sm-padding-1-half-rem-bottom { padding-bottom: 1.5rem; }
    .sm-padding-2-rem-bottom { padding-bottom: 2rem; }
    .sm-padding-2-half-rem-bottom { padding-bottom: 2.5rem; }
    .sm-padding-3-rem-bottom { padding-bottom: 3rem; }
    .sm-padding-3-half-rem-bottom { padding-bottom: 3.5rem; }
    .sm-padding-4-rem-bottom { padding-bottom: 4rem; }
    .sm-padding-4-half-rem-bottom { padding-bottom: 4.5rem; }
    .sm-padding-5-rem-bottom { padding-bottom: 5rem; }
    .sm-padding-5-half-rem-bottom { padding-bottom: 5.5rem; }
    .sm-padding-6-rem-bottom { padding-bottom: 6rem; }
    .sm-padding-6-half-rem-bottom { padding-bottom: 6.5rem; }
    .sm-padding-7-rem-bottom { padding-bottom: 7rem; }
    .sm-padding-7-half-rem-bottom { padding-bottom: 7.5rem; }
    .sm-padding-8-rem-bottom { padding-bottom: 8rem; }
    .sm-padding-8-half-rem-bottom { padding-bottom: 8.5rem; }
    .sm-padding-9-rem-bottom { padding-bottom: 9rem; }
    .sm-padding-9-half-rem-bottom { padding-bottom: 9.5rem; }
    .sm-padding-10-rem-bottom { padding-bottom: 10rem; }
    .sm-padding-10-half-rem-bottom { padding-bottom: 10.5rem; }

    /* padding right */
    .sm-padding-one-right { padding-right:1%; }
    .sm-padding-two-right { padding-right:2%; }
    .sm-padding-three-right { padding-right:3%; }
    .sm-padding-four-right { padding-right:4% }
    .sm-padding-five-right { padding-right:5%; }
    .sm-padding-six-right { padding-right:6%; }
    .sm-padding-seven-right { padding-right:7%; }
    .sm-padding-eight-right { padding-right:8%; }
    .sm-padding-nine-right { padding-right:9%; }
    .sm-padding-ten-right { padding-right:10%; }
    .sm-padding-eleven-right { padding-right:11%; }
    .sm-padding-twelve-right { padding-right:12%; }
    .sm-padding-thirteen-right { padding-right:13%; }
    .sm-padding-fourteen-right { padding-right:14%; }
    .sm-padding-fifteen-right { padding-right:15%; }
    .sm-padding-sixteen-right { padding-right:16%; }
    .sm-padding-seventeen-right { padding-right:17%; }
    .sm-padding-eighteen-right { padding-right:18%; }
    .sm-padding-nineteen-right { padding-right:19%; }
    .sm-padding-twenty-right { padding-right:20%; }
    .sm-padding-5px-right { padding-right:5px; }
    .sm-padding-10px-right { padding-right:10px; }
    .sm-padding-15px-right { padding-right:15px; }
    .sm-padding-20px-right { padding-right:20px; }
    .sm-padding-25px-right { padding-right:25px; }
    .sm-padding-30px-right { padding-right:30px; }
    .sm-padding-35px-right { padding-right:35px; }
    .sm-padding-40px-right { padding-right:40px; }
    .sm-padding-45px-right { padding-right:45px; }
    .sm-padding-50px-right { padding-right:50px; }
    .sm-padding-55px-right { padding-right:55px; }
    .sm-padding-60px-right { padding-right:60px; }
    .sm-padding-65px-right { padding-right:65px; }
    .sm-padding-70px-right { padding-right:70px; }
    .sm-padding-75px-right { padding-right:75px; }
    .sm-padding-80px-right { padding-right:80px; }
    .sm-padding-85px-right { padding-right:85px; }
    .sm-padding-90px-right { padding-right:90px; }
    .sm-padding-95px-right { padding-right:95px; }
    .sm-padding-100px-right { padding-right:100px; }
    .sm-padding-1-rem-right { padding-right: 1rem; }
    .sm-padding-1-half-rem-right { padding-right: 1.5rem; }
    .sm-padding-2-rem-right { padding-right: 2rem; }
    .sm-padding-2-half-rem-right { padding-right: 2.5rem; }
    .sm-padding-3-rem-right { padding-right: 3rem; }
    .sm-padding-3-half-rem-right { padding-right: 3.5rem; }
    .sm-padding-4-rem-right { padding-right: 4rem; }
    .sm-padding-4-half-rem-right { padding-right: 4.5rem; }
    .sm-padding-5-rem-right { padding-right: 5rem; }
    .sm-padding-5-half-rem-right { padding-right: 5.5rem; }
    .sm-padding-6-rem-right { padding-right: 6rem; }
    .sm-padding-6-half-rem-right { padding-right: 6.5rem; }
    .sm-padding-7-rem-right { padding-right: 7rem; }
    .sm-padding-7-half-rem-right { padding-right: 7.5rem; }
    .sm-padding-8-rem-right { padding-right: 8rem; }
    .sm-padding-8-half-rem-right { padding-right: 8.5rem; }
    .sm-padding-9-rem-right { padding-right: 9rem; }
    .sm-padding-9-half-rem-right { padding-right: 9.5rem; }
    .sm-padding-10-rem-right { padding-right: 10rem; }
    .sm-padding-10-half-rem-right { padding-right: 10.5rem; }

    /* padding left */
    .sm-padding-one-left { padding-left:1%; }
    .sm-padding-two-left { padding-left:2%; }
    .sm-padding-three-left { padding-left:3%; }
    .sm-padding-four-left { padding-left:4%; }
    .sm-padding-five-left { padding-left:5%; }
    .sm-padding-six-left { padding-left:6%; }
    .sm-padding-seven-left { padding-left:7%; }
    .sm-padding-eight-left { padding-left:8%; }
    .sm-padding-nine-left { padding-left:9%; }
    .sm-padding-ten-left { padding-left:10%; }
    .sm-padding-eleven-left { padding-left:11%; }
    .sm-padding-twelve-left { padding-left:12%; }
    .sm-padding-thirteen-left { padding-left:13%; }
    .sm-padding-fourteen-left { padding-left:14%; }
    .sm-padding-fifteen-left { padding-left:15%; }
    .sm-padding-sixteen-left { padding-left:16%; }
    .sm-padding-seventeen-left { padding-left:17%; }
    .sm-padding-eighteen-left { padding-left:18%; }
    .sm-padding-nineteen-left { padding-left:19%; }
    .sm-padding-twenty-left { padding-left:20%; }
    .sm-padding-5px-left { padding-left:5px; }
    .sm-padding-10px-left { padding-left:10px; }
    .sm-padding-15px-left { padding-left:15px; }
    .sm-padding-20px-left { padding-left:20px; }
    .sm-padding-25px-left { padding-left:25px; }
    .sm-padding-30px-left { padding-left:30px; }
    .sm-padding-35px-left { padding-left:35px; }
    .sm-padding-40px-left { padding-left:40px; }
    .sm-padding-45px-left { padding-left:45px; }
    .sm-padding-50px-left { padding-left:50px; }
    .sm-padding-55px-left { padding-left:55px; }
    .sm-padding-60px-left { padding-left:60px; }
    .sm-padding-65px-left { padding-left:65px; }
    .sm-padding-70px-left { padding-left:70px; }
    .sm-padding-75px-left { padding-left:75px; }
    .sm-padding-80px-left { padding-left:80px; }
    .sm-padding-85px-left { padding-left:85px; }
    .sm-padding-90px-left { padding-left:90px; }
    .sm-padding-95px-left { padding-left:95px; }
    .sm-padding-100px-left { padding-left:100px; }
    .sm-padding-1-rem-left { padding-left: 1rem; }
    .sm-padding-1-half-rem-left { padding-left: 1.5rem; }
    .sm-padding-2-rem-left { padding-left: 2rem; }
    .sm-padding-2-half-rem-left { padding-left: 2.5rem; }
    .sm-padding-3-rem-left { padding-left: 3rem; }
    .sm-padding-3-half-rem-left { padding-left: 3.5rem; }
    .sm-padding-4-rem-left { padding-left: 4rem; }
    .sm-padding-4-half-rem-left { padding-left: 4.5rem; }
    .sm-padding-5-rem-left { padding-left: 5rem; }
    .sm-padding-5-half-rem-left { padding-left: 5.5rem; }
    .sm-padding-6-rem-left { padding-left: 6rem; }
    .sm-padding-6-half-rem-left { padding-left: 6.5rem; }
    .sm-padding-7-rem-left { padding-left: 7rem; }
    .sm-padding-7-half-rem-left { padding-left: 7.5rem; }
    .sm-padding-8-rem-left { padding-left: 8rem; }
    .sm-padding-8-half-rem-left { padding-left: 8.5rem; }
    .sm-padding-9-rem-left { padding-left: 9rem; }
    .sm-padding-9-half-rem-left { padding-left: 9.5rem; }
    .sm-padding-10-rem-left { padding-left: 10rem; }
    .sm-padding-10-half-rem-left { padding-left: 10.5rem; }

    /* padding top bottom */
    .sm-padding-one-tb { padding-top:1%; padding-bottom:1%; }
    .sm-padding-two-tb { padding-top:2%; padding-bottom:2%; }
    .sm-padding-three-tb { padding-top:3%; padding-bottom:3%; }
    .sm-padding-four-tb { padding-top:4%; padding-bottom:4%; }
    .sm-padding-five-tb { padding-top:5%; padding-bottom:5%; }
    .sm-padding-six-tb { padding-top:6%; padding-bottom:6%; }
    .sm-padding-seven-tb { padding-top:7%; padding-bottom:7%; }
    .sm-padding-eight-tb { padding-top:8%; padding-bottom:8%; }
    .sm-padding-nine-tb { padding-top:9%; padding-bottom:9%; }
    .sm-padding-ten-tb { padding-top:10%; padding-bottom:10%; }
    .sm-padding-eleven-tb { padding-top:11%; padding-bottom:11%; }
    .sm-padding-twelve-tb { padding-top:12%; padding-bottom:12%; }
    .sm-padding-thirteen-tb { padding-top:13%; padding-bottom:13%; }
    .sm-padding-fourteen-tb { padding-top:14%; padding-bottom:14%; }
    .sm-padding-fifteen-tb { padding-top:15%; padding-bottom:15%; }
    .sm-padding-sixteen-tb { padding-top:16%; padding-bottom:16%; }
    .sm-padding-seventeen-tb { padding-top:17%; padding-bottom:17%; }
    .sm-padding-eighteen-tb { padding-top:18%; padding-bottom:18%; }
    .sm-padding-nineteen-tb { padding-top:19%; padding-bottom:19%; }
    .sm-padding-twenty-tb { padding-top:20%; padding-bottom:20%; }
    .sm-padding-5px-tb { padding-top:5px; padding-bottom:5px; }
    .sm-padding-10px-tb { padding-top:10px; padding-bottom:10px; }
    .sm-padding-15px-tb { padding-top:15px; padding-bottom:15px; }
    .sm-padding-20px-tb { padding-top:20px; padding-bottom:20px; }
    .sm-padding-25px-tb { padding-top:25px; padding-bottom:25px; }
    .sm-padding-30px-tb { padding-top:30px; padding-bottom:30px; }
    .sm-padding-35px-tb { padding-top:35px; padding-bottom:35px; }
    .sm-padding-40px-tb { padding-top:40px; padding-bottom:40px; }
    .sm-padding-45px-tb { padding-top:45px; padding-bottom:45px; }
    .sm-padding-50px-tb { padding-top:50px; padding-bottom:50px; }
    .sm-padding-55px-tb { padding-top:55px; padding-bottom:55px; }
    .sm-padding-60px-tb { padding-top:60px; padding-bottom:60px; }
    .sm-padding-65px-tb { padding-top:65px; padding-bottom:65px; }
    .sm-padding-70px-tb { padding-top:70px; padding-bottom:70px; }
    .sm-padding-75px-tb { padding-top:75px; padding-bottom:75px; }
    .sm-padding-80px-tb { padding-top:80px; padding-bottom:80px; }
    .sm-padding-85px-tb { padding-top:85px; padding-bottom:85px; }
    .sm-padding-90px-tb { padding-top:90px; padding-bottom:90px; }
    .sm-padding-95px-tb { padding-top:95px; padding-bottom:95px; }
    .sm-padding-100px-tb { padding-top:100px; padding-bottom:100px; }
    .sm-padding-1-rem-tb { padding-top: 1rem; padding-bottom: 1rem; }
    .sm-padding-1-half-rem-tb { padding-top: 1.5rem; padding-bottom: 1.5rem; }
    .sm-padding-2-rem-tb { padding-top: 2rem; padding-bottom: 2rem; }
    .sm-padding-2-half-rem-tb { padding-top: 2.5rem; padding-bottom: 2.5rem; }
    .sm-padding-3-rem-tb { padding-top: 3rem; padding-bottom: 3rem; }
    .sm-padding-3-half-rem-tb { padding-top: 3.5rem; padding-bottom: 3.5rem; }
    .sm-padding-4-rem-tb { padding-top: 4rem; padding-bottom: 4rem; }
    .sm-padding-4-half-rem-tb { padding-top: 4.5rem; padding-bottom: 4.5rem; }
    .sm-padding-5-rem-tb { padding-top: 5rem; padding-bottom: 5rem; }
    .sm-padding-5-half-rem-tb { padding-top: 5.5rem; padding-bottom: 5.5rem; }
    .sm-padding-6-rem-tb { padding-top: 6rem; padding-bottom: 6rem; }
    .sm-padding-6-half-rem-tb { padding-top: 6.5rem; padding-bottom: 6.5rem; }
    .sm-padding-7-rem-tb { padding-top: 7rem; padding-bottom: 7rem; }
    .sm-padding-7-half-rem-tb { padding-top: 7.5rem; padding-bottom: 7.5rem; }
    .sm-padding-8-rem-tb { padding-top: 8rem; padding-bottom: 8rem; }
    .sm-padding-8-half-rem-tb { padding-top: 8.5rem; padding-bottom: 8.5rem; }
    .sm-padding-9-rem-tb { padding-top: 9rem; padding-bottom: 9rem; }
    .sm-padding-9-half-rem-tb { padding-top: 9.5rem; padding-bottom: 9.5rem; }
    .sm-padding-10-rem-tb { padding-top: 10rem; padding-bottom: 10rem; }
    .sm-padding-10-half-rem-tb { padding-top: 10.5rem; padding-bottom: 10.5rem; }

    /* padding left right */
    .sm-padding-one-lr { padding-left:1%; padding-right:1%; }
    .sm-padding-two-lr { padding-left:2%; padding-right:2%; }
    .sm-padding-three-lr { padding-left:3%; padding-right:3%; }
    .sm-padding-four-lr { padding-left:4%; padding-right:4%; }
    .sm-padding-five-lr { padding-left:5%; padding-right:5%; }
    .sm-padding-six-lr { padding-left:6%; padding-right:6%; }
    .sm-padding-seven-lr { padding-left:7%; padding-right:7%; }
    .sm-padding-eight-lr { padding-left:8%; padding-right:8%; }
    .sm-padding-nine-lr { padding-left:9%; padding-right:9%; }
    .sm-padding-ten-lr { padding-left:10%; padding-right:10%; }
    .sm-padding-eleven-lr { padding-left:11%; padding-right:11%; }
    .sm-padding-twelve-lr { padding-left:12%; padding-right:12%; }
    .sm-padding-thirteen-lr { padding-left:13%; padding-right:13%; }
    .sm-padding-fourteen-lr { padding-left:14%; padding-right:14%; }
    .sm-padding-fifteen-lr { padding-left:15%; padding-right:15%; }
    .sm-padding-sixteen-lr { padding-left:16%; padding-right:16%; }
    .sm-padding-seventeen-lr { padding-left:17%; padding-right:17%; }
    .sm-padding-eighteen-lr { padding-left:18%; padding-right:18%; }
    .sm-padding-nineteen-lr { padding-left:19%; padding-right:19%; }
    .sm-padding-twenty-lr { padding-left:20%; padding-right:20%; }
    .sm-padding-5px-lr { padding-left:5px; padding-right:5px; }
    .sm-padding-10px-lr { padding-left:10px; padding-right:10px; }
    .sm-padding-15px-lr { padding-left:15px; padding-right:15px; }
    .sm-padding-20px-lr { padding-left:20px; padding-right:20px; }
    .sm-padding-25px-lr { padding-left:25px; padding-right:25px; }
    .sm-padding-30px-lr { padding-left:30px; padding-right:30px; }
    .sm-padding-35px-lr { padding-left:35px; padding-right:35px; }
    .sm-padding-40px-lr { padding-left:40px; padding-right:40px; }
    .sm-padding-45px-lr { padding-left:45px; padding-right:45px; }
    .sm-padding-50px-lr { padding-left:50px; padding-right:50px; }
    .sm-padding-55px-lr { padding-left:55px; padding-right:55px; }
    .sm-padding-60px-lr { padding-left:60px; padding-right:60px; }
    .sm-padding-65px-lr { padding-left:65px; padding-right:65px; }
    .sm-padding-70px-lr { padding-left:70px; padding-right:70px; }
    .sm-padding-75px-lr { padding-left:75px; padding-right:75px; }
    .sm-padding-80px-lr { padding-left:80px; padding-right:80px; }
    .sm-padding-85px-lr { padding-left:85px; padding-right:85px; }
    .sm-padding-90px-lr { padding-left:90px; padding-right:90px; }
    .sm-padding-95px-lr { padding-left:95px; padding-right:95px; }
    .sm-padding-100px-lr { padding-left:100px; padding-right:100px; }
    .sm-padding-1-rem-lr { padding-left: 1rem; padding-right: 1rem; }
    .sm-padding-1-half-rem-lr { padding-left: 1.5rem; padding-right: 1.5rem; }
    .sm-padding-2-rem-lr { padding-left: 2rem; padding-right: 2rem; }
    .sm-padding-2-half-rem-lr { padding-left: 2.5rem; padding-right: 2.5rem; }
    .sm-padding-3-rem-lr { padding-left: 3rem; padding-right: 3rem; }
    .sm-padding-3-half-rem-lr { padding-left: 3.5rem; padding-right: 3.5rem; }
    .sm-padding-4-rem-lr { padding-left: 4rem; padding-right: 4rem; }
    .sm-padding-4-half-rem-lr { padding-left: 4.5rem; padding-right: 4.5rem; }
    .sm-padding-5-rem-lr { padding-left: 5rem; padding-right: 5rem; }
    .sm-padding-5-half-rem-lr { padding-left: 5.5rem; padding-right: 5.5rem; }
    .sm-padding-6-rem-lr { padding-left: 6rem; padding-right: 6rem; }
    .sm-padding-6-half-rem-lr { padding-left: 6.5rem; padding-right: 6.5rem; }
    .sm-padding-7-rem-lr { padding-left: 7rem; padding-right: 7rem; }
    .sm-padding-7-half-rem-lr { padding-left: 7.5rem; padding-right: 7.5rem; }
    .sm-padding-8-rem-lr { padding-left: 8rem; padding-right: 8rem; }
    .sm-padding-8-half-rem-lr { padding-left: 8.5rem; padding-right: 8.5rem; }
    .sm-padding-9-rem-lr { padding-left: 9rem; padding-right: 9rem; }
    .sm-padding-9-half-rem-lr { padding-left: 9.5rem; padding-right: 9.5rem; }
    .sm-padding-10-rem-lr { padding-left: 10rem; padding-right: 10rem; }
    .sm-padding-10-half-rem-lr { padding-left: 10.5rem; padding-right: 10.5rem; }

    .sm-no-padding { padding:0 !important; }
    .sm-no-padding-lr { padding-left: 0 !important; padding-right: 0 !important; }
    .sm-no-padding-tb { padding-top: 0 !important; padding-bottom: 0 !important; }
    .sm-no-padding-top { padding-top:0 !important; }
    .sm-no-padding-bottom { padding-bottom:0 !important; }
    .sm-no-padding-left { padding-left:0 !important; }
    .sm-no-padding-right { padding-right:0 !important; }

    /* display and overflow */
    .sm-d-initial { display: initial !important; }
    .sm-overflow-hidden { overflow:hidden !important; }
    .sm-overflow-visible { overflow:visible !important; }
    .sm-overflow-auto { overflow:auto !important; }

    /* position */
    .sm-position-relative { position: relative !important; }
    .sm-position-absolute { position: absolute !important; }
    .sm-position-fixed { position: fixed !important; }
    .sm-position-inherit { position: inherit !important; }
    .sm-position-initial { position: initial !important; }

    /* top */
    .sm-top-0px { top: 0; }
    .sm-top-1px { top: 1px; }
    .sm-top-2px { top: 2px; }
    .sm-top-3px { top: 3px; }
    .sm-top-4px { top: 4px; }
    .sm-top-5px { top: 5px; }
    .sm-top-6px { top: 6px; }
    .sm-top-7px { top: 7px; }
    .sm-top-8px { top: 8px; }
    .sm-top-9px { top: 9px; }
    .sm-top-10px { top: 10px; }
    .sm-top-15px { top: 15px; }
    .sm-top-20px { top: 20px; }
    .sm-top-25px { top: 25px; }
    .sm-top-30px { top: 30px; }
    .sm-top-35px { top: 35px; }
    .sm-top-40px { top: 40px; }
    .sm-top-45px { top: 45px; }
    .sm-top-50px { top: 50px; }
    .sm-top-auto { top:auto; }
    .sm-top-inherit { top:inherit; }

    /* top minus */
    .sm-top-minus-1px { top: -1px; }
    .sm-top-minus-2px { top: -2px; }
    .sm-top-minus-3px { top: -3px; }
    .sm-top-minus-4px { top: -4px; }
    .sm-top-minus-5px { top: -5px; }
    .sm-top-minus-6px { top: -6px; }
    .sm-top-minus-7px { top: -7px; }
    .sm-top-minus-8px { top: -8px; }
    .sm-top-minus-9px { top: -9px; }
    .sm-top-minus-10px { top: -10px; }
    .sm-top-minus-15px { top: -15px; }
    .sm-top-minus-20px { top: -20px; }
    .sm-top-minus-25px { top: -25px; }
    .sm-top-minus-30px { top: -30px; }
    .sm-top-minus-35px { top: -35px; }
    .sm-top-minus-40px { top: -40px; }
    .sm-top-minus-45px { top: -45px; }
    .sm-top-minus-50px { top: -50px; }

    /* bottom */
    .sm-bottom-0px { bottom:0; }
    .sm-bottom-1px { bottom:1px; }
    .sm-bottom-2px { bottom:2px; }
    .sm-bottom-3px { bottom:3px; }
    .sm-bottom-4px { bottom:4px; }
    .sm-bottom-5px { bottom:5px; }
    .sm-bottom-6px { bottom:6px; }
    .sm-bottom-7px { bottom:7px; }
    .sm-bottom-8px { bottom:8px; }
    .sm-bottom-9px { bottom:9px; }
    .sm-bottom-10px { bottom:10px; }
    .sm-bottom-15px { bottom:15px; }
    .sm-bottom-20px { bottom:20px; }
    .sm-bottom-25px { bottom:25px; }
    .sm-bottom-30px { bottom:30px; }
    .sm-bottom-35px { bottom:35px; }
    .sm-bottom-40px { bottom:40px; }
    .sm-bottom-45px { bottom:45px; }
    .sm-bottom-50px { bottom:50px; }
    .sm-bottom-55px { bottom:55px; }
    .sm-bottom-60px { bottom:60px; }
    .sm-bottom-auto { bottom: auto; }
    .sm-bottom-inherit { bottom: inherit; }

    /* bottom minus */
    .sm-bottom-minus-1px { bottom: -1px; }
    .sm-bottom-minus-2px { bottom: -2px; }
    .sm-bottom-minus-3px { bottom: -3px; }
    .sm-bottom-minus-4px { bottom: -4px; }
    .sm-bottom-minus-5px { bottom: -5px; }
    .sm-bottom-minus-6px { bottom: -6px; }
    .sm-bottom-minus-7px { bottom: -7px; }
    .sm-bottom-minus-8px { bottom: -8px; }
    .sm-bottom-minus-9px { bottom: -9px; }
    .sm-bottom-minus-10px { bottom: -10px; }
    .sm-bottom-minus-15px { bottom: -15px; }
    .sm-bottom-minus-20px { bottom: -20px; }
    .sm-bottom-minus-25px { bottom: -25px; }
    .sm-bottom-minus-30px { bottom: -30px; }
    .sm-bottom-minus-35px { bottom: -35px; }
    .sm-bottom-minus-40px { bottom: -40px; }
    .sm-bottom-minus-45px { bottom: -45px; }
    .sm-bottom-minus-50px { bottom: -50px; }

    /* right */
    .sm-right-0px { right: 0; }
    .sm-right-1px { right: 1px; }
    .sm-right-2px { right: 2px; }
    .sm-right-3px { right: 3px; }
    .sm-right-4px { right: 4px; }
    .sm-right-5px { right: 5px; }
    .sm-right-6px { right: 6px; }
    .sm-right-7px { right: 7px; }
    .sm-right-8px { right: 8px; }
    .sm-right-9px { right: 9px; }
    .sm-right-10px { right: 10px; }
    .sm-right-15px { right: 15px; }
    .sm-right-20px { right: 20px; }
    .sm-right-25px { right: 25px; }
    .sm-right-30px { right: 30px; }
    .sm-right-35px { right: 35px; }
    .sm-right-40px { right: 40px; }
    .sm-right-45px { right: 45px; }
    .sm-right-50px { right: 50px; }
    .sm-right-auto { right: auto; }
    .sm-right-inherit { right: inherit; }

    /* right minus */
    .sm-right-minus-1px { right: -1px; }
    .sm-right-minus-2px { right: -2px; }
    .sm-right-minus-3px { right: -3px; }
    .sm-right-minus-4px { right: -4px; }
    .sm-right-minus-5px { right: -5px; }
    .sm-right-minus-6px { right: -6px; }
    .sm-right-minus-7px { right: -7px; }
    .sm-right-minus-8px { right: -8px; }
    .sm-right-minus-9px { right: -9px; }
    .sm-right-minus-10px { right: -10px; }
    .sm-right-minus-15px { right: -15px; }
    .sm-right-minus-20px { right: -20px; }
    .sm-right-minus-25px { right: -25px; }
    .sm-right-minus-30px { right: -30px; }
    .sm-right-minus-35px { right: -35px; }
    .sm-right-minus-40px { right: -40px; }
    .sm-right-minus-45px { right: -45px; }
    .sm-right-minus-50px { right: -50px; }

    /* left */
    .sm-left-0px { left: 0; }
    .sm-left-1px { left: 1px; }
    .sm-left-2px { left: 2px; }
    .sm-left-3px { left: 3px; }
    .sm-left-4px { left: 4px; }
    .sm-left-5px { left: 5px; }
    .sm-left-6px { left: 6px; }
    .sm-left-7px { left: 7px; }
    .sm-left-8px { left: 8px; }
    .sm-left-9px { left: 9px; }
    .sm-left-10px { left: 10px; }
    .sm-left-15px { left: 15px; }
    .sm-left-20px { left: 20px; }
    .sm-left-25px { left: 25px; }
    .sm-left-30px { left: 30px; }
    .sm-left-35px { left: 35px; }
    .sm-left-40px { left: 40px; }
    .sm-left-45px { left: 45px; }
    .sm-left-50px { left: 50px; }
    .sm-left-55px { left: 55px; }
    .sm-left-60px { left: 60px; }
    .sm-left-auto { left: auto; }
    .sm-left-inherit { left: inherit; }

    /* left minus */
    .sm-left-minus-1px { left: -1px; }
    .sm-left-minus-2px { left: -2px; }
    .sm-left-minus-3px { left: -3px; }
    .sm-left-minus-4px { left: -4px; }
    .sm-left-minus-5px { left: -5px; }
    .sm-left-minus-6px { left: -6px; }
    .sm-left-minus-7px { left: -7px; }
    .sm-left-minus-8px { left: -8px; }
    .sm-left-minus-9px { left: -9px; }
    .sm-left-minus-10px { left: -10px; }
    .sm-left-minus-15px { left: -15px; }
    .sm-left-minus-20px { left: -20px; }
    .sm-left-minus-25px { left: -25px; }
    .sm-left-minus-30px { left: -30px; }
    .sm-left-minus-35px { left: -35px; }
    .sm-left-minus-40px { left: -40px; }
    .sm-left-minus-45px { left: -45px; }
    .sm-left-minus-50px { left: -50px; }

    /* width */
    .sm-w-1px { width:1px !important; }
    .sm-w-2px { width:2px !important; }
    .sm-w-3px { width:3px !important; }
    .sm-w-4px { width:4px !important; }
    .sm-w-5px { width:5px !important; }
    .sm-w-6px { width:6px !important; }
    .sm-w-7px { width:7px !important; }
    .sm-w-8px { width:8px !important; }
    .sm-w-9px { width:9px !important; }
    .sm-w-10px { width:10px !important; }
    .sm-w-15px { width:15px !important; }
    .sm-w-20px { width:20px !important; }
    .sm-w-25px { width:25px !important; }
    .sm-w-30px { width:30px !important; }
    .sm-w-35px { width:35px !important; }
    .sm-w-40px { width:40px !important; }
    .sm-w-50px { width:50px !important; }
    .sm-w-55px { width:55px !important; }
    .sm-w-60px { width:60px !important; }
    .sm-w-65px { width:65px !important; }
    .sm-w-70px { width:70px !important; }
    .sm-w-75px { width:75px !important; }
    .sm-w-80px { width:80px !important; }
    .sm-w-85px { width:85px !important; }
    .sm-w-90px { width:90px !important; }
    .sm-w-95px { width:95px !important; }
    .sm-w-100px { width:100px !important; }
    .sm-w-110px { width:110px !important; }
    .sm-w-120px { width:120px !important; }
    .sm-w-130px { width:130px !important; }
    .sm-w-140px { width:140px !important; }
    .sm-w-150px { width:150px !important; }
    .sm-w-160px { width:160px !important; }
    .sm-w-170px { width:170px !important; }
    .sm-w-180px { width:180px !important; }
    .sm-w-190px { width:190px !important; }
    .sm-w-200px { width:200px !important; }
    .sm-w-250px { width:250px !important; }
    .sm-w-300px { width:300px !important; }
    .sm-w-350px { width:350px !important; }
    .sm-w-400px { width:400px !important; }
    .sm-w-450px { width:450px !important; }
    .sm-w-500px { width:500px !important; }
    .sm-w-550px { width:550px !important; }
    .sm-w-600px { width:600px !important; }
    .sm-w-650px { width:650px !important; }
    .sm-w-700px { width:700px !important; }
    .sm-w-750px { width:750px !important; }
    .sm-w-800px { width:800px !important; }
    .sm-w-850px { width:850px !important; }
    .sm-w-900px { width:900px !important; }
    .sm-w-950px { width:950px !important; }
    .sm-w-1000px { width:1000px !important; }
    .sm-w-10 { width: 10% !important; }
    .sm-w-15 { width: 15% !important; }
    .sm-w-20 { width: 20% !important; }
    .sm-w-25 { width: 25% !important; }
    .sm-w-30 { width: 30% !important; }
    .sm-w-35 { width: 35% !important; }
    .sm-w-40 { width: 40% !important; }
    .sm-w-45 { width: 45% !important; }
    .sm-w-50 { width: 50% !important; }
    .sm-w-55 { width: 55% !important; }
    .sm-w-60 { width: 60% !important; }
    .sm-w-65 { width: 65% !important; }
    .sm-w-70 { width: 70% !important; }
    .sm-w-75 { width: 75% !important; }
    .sm-w-80 { width: 80% !important; }
    .sm-w-85 { width: 85% !important; }
    .sm-w-90 { width: 90% !important; }
    .sm-w-95 { width: 95% !important; }
    .sm-w-100 { width: 100% !important; }
    .sm-w-auto { width:auto !important; }

    /* height */
    .sm-h-1px { height: 1px !important; }
    .sm-h-2px { height: 2px !important; }
    .sm-h-3px { height: 3px !important; }
    .sm-h-4px { height: 4px !important; }
    .sm-h-5px { height: 5px !important; }
    .sm-h-6px { height: 6px !important; }
    .sm-h-7px { height: 7px !important; }
    .sm-h-8px { height: 8px !important; }
    .sm-h-9px { height: 9px !important; }
    .sm-h-10px { height: 10px !important; }
    .sm-h-20px { height: 20px !important; }
    .sm-h-30px { height: 30px !important; }
    .sm-h-40px { height: 40px !important; }
    .sm-h-42px { height: 42px !important; }
    .sm-h-50px { height: 50px !important; }
    .sm-h-60px { height: 60px !important; }
    .sm-h-70px { height: 70px !important; }
    .sm-h-80px { height: 80px !important; }
    .sm-h-90px { height: 90px !important; }
    .sm-h-100px { height: 100px !important; }
    .sm-h-110px { height: 110px !important; }
    .sm-h-120px { height: 120px !important; }
    .sm-h-130px { height: 130px !important; }
    .sm-h-140px { height: 140px !important; }
    .sm-h-150px { height: 150px !important; }
    .sm-h-160px { height: 160px !important; }
    .sm-h-170px { height: 170px !important; }
    .sm-h-180px { height: 180px !important; }
    .sm-h-190px { height: 190px !important; }
    .sm-h-200px { height: 200px !important; }
    .sm-h-250px { height: 250px !important; }
    .sm-h-300px { height: 300px !important; }
    .sm-h-350px { height: 350px !important; }
    .sm-h-400px { height: 400px !important; }
    .sm-h-450px { height: 450px !important; }
    .sm-h-500px { height: 500px !important; }
    .sm-h-520px { height: 520px !important; }
    .sm-h-550px { height: 550px !important; }
    .sm-h-580px { height: 580px !important; }
    .sm-h-600px { height: 600px !important; }
    .sm-h-650px { height: 650px !important; }
    .sm-h-700px { height: 700px !important; }
    .sm-h-720px { height: 720px !important; }
    .sm-h-750px { height: 750px !important; }
    .sm-h-800px { height: 800px !important; }
    .sm-h-820px { height: 820px !important; }
    .sm-h-830px { height: 830px !important; }
    .sm-h-850px { height: 850px !important; }

    .sm-h-50 { height: 50% !important; }
    .sm-h-100 { height: 100% !important; }
    .sm-h-auto { height:auto !important; }

    /* min-height */
    .sm-min-h-100px { min-height: 100px; }
    .sm-min-h-200px { min-height: 200px; }
    .sm-min-h-300px { min-height: 300px; }
    .sm-min-h-400px { min-height: 400px; }
    .sm-min-h-500px { min-height: 500px; }
    .sm-min-h-600px { min-height: 600px; }
    .sm-min-h-700px { min-height: 700px; }

    /* screen height */
    .one-fifth-screen { height:600px; }
    .one-fourth-screen { height:500px; }
    .one-third-screen { height:350px; }
    .one-second-screen { height:300px; }
    .extra-small-screen { height:250px; }

    /* magnific popup */
    button.mfp-arrow, .mfp-arrow:active { height: 50px; width: 50px; margin-top: -25px; opacity: 1; }
    .mfp-arrow:before { margin: 0 auto; width: 100%; height: 100%; display: inline-block; vertical-align: middle; text-align: center; line-height: 50px; border: 0; }

    /* content box image */
    .content-box-image { height: 100%; }

    /* slider navigation */
    .swiper-prev, .swiper-next { font-size: 16px; }
    .swiper-next i, .swiper-prev i { height: 35px; width: 35px; line-height: 35px; }
    .swiper-prev { left: 5px; }
    .swiper-next { right: 5px; }

    /* slider navigation style 08 */
    .slider-navigation-style-08.swiper-button-next.rounded-circle { right: 10px; width: 35px; height: 35px; }
    .slider-navigation-style-08.swiper-button-prev.rounded-circle { left: 10px; width: 35px; height: 35px; }

    /* swiper custom text */
    .slider-custom-text-prev.swiper-button-prev { padding-right: 30px; }
    .slider-custom-text-next.swiper-button-next { padding-left: 30px; }

    /* swiper vertical */
    .slider-vertical .swiper-number-pagination { bottom: 30px; }

    /* swiper pagination */
    .slider-multy-scroll-right > .swiper-pagination-bullets { right: 40px; }

    /* grid */
    .grid.sm-grid-6col li { width: 16.67%; }
    .grid.sm-grid-6col li.grid-item-double { width: 33.33%; }
    .grid.sm-grid-5col li { width: 20%; }
    .grid.sm-grid-5col li.grid-item-double { width: 40%; }
    .grid.sm-grid-4col li { width: 25%; }
    .grid.sm-grid-4col li.grid-item-double { width: 50%; }
    .grid.sm-grid-3col li { width: 33.33%; }
    .grid.sm-grid-3col li.grid-item-double { width: 66.67%; }
    .grid.sm-grid-2col li { width: 50%; }
    .grid.sm-grid-2col li.grid-item-double { width: 100%; }
    .grid.sm-grid-1col li { width: 100%; }

    /* gutter type */
    .grid.gutter-small { margin: 0 -7px; }
    .grid.gutter-small li { padding:7px 7px; }
    .grid.gutter-extra-large li { padding:15px; }
    .grid.gutter-extra-large { margin:0 -15px; }
    .grid.gutter-double-extra-large li { padding: 15px; }
    .grid.gutter-medium { margin: 0 -7px; }
    .grid.gutter-medium li { padding: 7px 7px }
    .grid.gutter-large { margin: 0 -10px; }
    .grid.gutter-large li { padding: 10px 10px }

    /* portfolio scattered */
    .portfolio-scattered.row-cols-xl-4 .col:nth-child(odd) .portfolio-box { padding: 0 0 10%; }
    .portfolio-scattered.row-cols-xl-4 .col:nth-child(even) .portfolio-box { padding: 0 10% 10%; }

    /* interactive banner style 05 */
    .interactive-banners-style-05 .interactive-banners-content, .interactive-banners-style-05 .interactive-banners-overlayer { transform: translateY(calc(100% - 115px)); -webkit-transform: translateY(calc(100% - 115px)); -moz-transform: translateY(calc(100% - 115px)); -ms-transform: translateY(calc(100% - 115px)); }

    /* interactive banner style 09 */
    .interactive-banners-style-09 .interactive-banners-content .interactive-banners-hover-icon { left: 35px; bottom: 35px; }

    /* accordion style 04 */
    .accordion-style-04 .panel { padding-left: 20px; padding-right: 20px; }
    .accordion-style-04 .panel .panel-time { min-width: 115px; }
    .accordion-style-04 .panel .accordion-toggle { width: calc(100% - 330px); }
    .accordion-style-04 .panel .panel-body { margin-left: 115px; width: 70%; }    

    /* time table */
    .time-table .panel .panel-time, .time-table .panel .panel-speaker { width: 100%; text-align: center; }
    .time-table .panel .panel-body { width: 100%; }

    /* tab */
    .nav-tabs > li { padding: 0; display: block; margin-bottom: 10px; width: 100%; }
    .nav-tabs > li.active > a, .nav-tabs > li.active > a:hover, .nav-tabs > li.active > a:focus, .nav-tabs > li > a:focus, .nav-tabs > li > a:hover { border: none; }
    .nav-tabs > li.nav-item { padding: 0; }

    /* tab style 01 */
    .tab-style-01 .nav-tabs { display: table; width: 100%;}
    .tab-style-01 .nav-tabs > li.nav-item { padding: 0; margin: 0 auto 20px; display: inherit; width: auto; }
    .tab-style-01 .nav-tabs > li.nav-item > a.nav-link { display: inline-block; }

    /* tab style 02 */
    .tab-style-02 .nav-tabs > li.nav-item { margin: 0 0 15px 0;}
    .tab-style-02 .nav-tabs > li.nav-item:last-child { margin-bottom: 0;}

    /* tab style 03 */
    .tab-style-03 .nav-tabs > li.nav-item { padding: 0; margin-bottom: 30px; border: 0; }

    /* tab style 04 */
    .tab-style-04 .nav-tabs > li.nav-item { width: auto; }

    /* tab style 05 */
    .tab-style-05 .nav-tabs li { margin-bottom: 5px; }

    /* tab style 06 */
    .tab-style-06 .nav-tabs { border-bottom: none; }
    .tab-style-06 .nav-tabs > li.nav-item { width: 100%; border-bottom: 1px solid rgba(0,0,0,0.10); }
    .tab-style-06 .nav-tabs > li.nav-item > a.nav-link { padding: 15px 20px; }

    /* tab style 07 */
    .tab-style-07 .nav-tabs { -ms-flex-wrap: wrap; flex-wrap: wrap; }
    .tab-style-07 .nav-tabs > li.nav-item { border-bottom: 1px solid #e4e4e4; }
    .tab-style-07 .nav-tabs > li.nav-item > a.nav-link { padding: 10px 30px 8px 30px; }

    /* process step style 04 */
    .process-step-style-04 .process-content { padding-top: 40px; }

    /* grid filter */
    .grid-filter > li.active > a, .grid-filter > li.active > a:focus, .grid-filter > li.active > a:hover, .grid-filter > li > a:hover { border-bottom: 1px solid; }

    /* newsletter style 01 */
    .newsletter-style-01 input { padding-right: 20px; }

    /* table style 01 */
    .table-style-01 table { width: 600px; }

    /* pricing table style 02 */
    .pricing-table-style-02 .pricing-popular { left: 0; width: 100%; }

    /* countdown style 02 */
    .countdown.countdown-style-02 .countdown-box { padding: 0 15px; }

    /* no border */
    .sm-no-border-top { border-top:0 !important }
    .sm-no-border-bottom { border-bottom:0 !important }
    .sm-no-border-right { border-right:0 !important }
    .sm-no-border-left { border-left:0 !important }
    .sm-no-border-all { border: 0 !important }

    /* border width */
    .sm-border-width-1px { border-width:1px !important; }
    .sm-border-width-2px { border-width:2px !important; }
    .sm-border-width-3px { border-width:3px !important; }
    .sm-border-width-4px { border-width:4px !important; }
    .sm-border-width-5px { border-width:5px !important; }
    .sm-border-width-6px { border-width:6px !important; }
    .sm-border-width-7px { border-width:7px !important; }
    .sm-border-width-8px { border-width:8px !important; }
    .sm-border-width-9px { border-width:9px !important; }
    .sm-border-width-10px { border-width:10px !important; }
    .sm-border-width-11px { border-width:11px !important; }
    .sm-border-width-12px { border-width:12px !important; }
    .sm-border-width-13px { border-width:13px !important; }
    .sm-border-width-14px { border-width:14px !important; }
    .sm-border-width-15px { border-width:15px !important; }
    .sm-border-width-16px { border-width:16px !important; }
    .sm-border-width-17px { border-width:17px !important; }
    .sm-border-width-18px { border-width:18px !important; }
    .sm-border-width-19px { border-width:19px !important; }
    .sm-border-width-20px { border-width:20px !important; }

    /* border */
    .sm-border-all { border: 1px solid; }
    .sm-border-top { border-top: 1px solid; }
    .sm-border-bottom { border-bottom: 1px solid; }
    .sm-border-left { border-left: 1px solid; }
    .sm-border-right { border-right: 1px solid; }
    .sm-border-lr { border-left: 1px solid; border-right: 1px solid; }
    .sm-border-tb { border-top: 1px solid; border-bottom: 1px solid; }

    /* border color */
    .sm-border-color-white { border-color: #fff; }
    .sm-border-color-black { border-color: #000; }
    .sm-border-color-sky-blue { border-color: #2e94eb; }
    .sm-border-color-extra-dark-gray { border-color: #232323; }
    .sm-border-color-medium-dark-gray { border-color: #363636; }
    .sm-border-color-dark-gray { border-color: #939393; }
    .sm-border-color-extra-medium-gray { border-color: #dbdbdb; }
    .sm-border-color-medium-gray { border-color: #e4e4e4; }
    .sm-border-color-extra-light-gray { border-color: #ededed; }
    .sm-border-color-light-gray { border-color: #f5f5f5; }
    .sm-border-color-light-pink { border-color: #862237; }
    .sm-border-color-deep-pink { border-color: #ff214f; }
    .sm-border-color-pink { border-color: #ff357c; }
    .sm-border-color-fast-blue { border-color: #0038e3; }
    .sm-border-color-orange { border-color: #ff6437; }
    .sm-border-color-green { border-color: #45d690; }
    .sm-border-color-golden { border-color: #d0ba6d; }
    .sm-border-color-persian-blue { border-color: #0039CC; }
    .sm-border-color-purple { border-color: #7342ac; }
    .sm-border-color-parrot-green { border-color: #cee002; }
    .sm-border-color-dark-red { border-color: #e12837; }

    /* transparent border */
    .sm-border-color-transparent { border-color: transparent; }
    .sm-border-color-black-transparent { border-color: rgba(0,0,0,.1); }
    .sm-border-color-white-transparent { border-color: rgba(255,255,255,.1); }
    .sm-border-color-golden-transparent { border-color: rgba(208, 186, 109, 0.2); }
    .sm-border-color-pink-transparent { border-color: rgba(255, 33, 79, 0.45); }
    .sm-border-color-dark-white-transparent { border-color: rgba(255,255,255,0.2); }
    .sm-border-color-medium-white-transparent { border-color: rgba(255,255,255,0.4); }
    .sm-border-color-full-dark-white-transparent { border-color: rgba(255,255,255,0.05); }
    .sm-border-color-light-white-transparent { border-color: rgba(255,255,255,0.1); }
    .sm-border-color-nero-transparent { border-color: rgba(25,25,25,0.1); }
    .sm-border-color-extra-medium-gray-transparent { border-color: rgba(219,219,219,.04); }

    /* border style */
    .sm-border-dotted { border-style: dotted !important; }
    .sm-border-dashed { border-style: dashed !important; }
    .sm-border-solid { border-style: solid !important; }
    .sm-border-double { border-style: double !important; }
    .sm-border-groove { border-style: groove !important; }
    .sm-border-ridge { border-style: ridge !important; }
    .sm-border-inset { border-style: inset !important; }
    .sm-border-outset { border-style: outset !important; }
    .sm-border-none { border-style: none !important; }
    .sm-border-hidden { border-style: hidden !important; }
    .sm-border-transperent { border-color: transparent !important; }

    /* navigation */
    .navbar-toggler { margin-left: 0;}
    .navbar-nav { padding-left: 0; padding-right: 0; }
    .navbar.navbar-boxed { padding-left: 0; padding-right: 0; }
    .navbar-expand-lg>.container, .navbar-expand-lg>.container-fluid, .navbar-expand-lg>.container-lg, .navbar-expand-lg>.container-md, .navbar-expand-lg>.container-sm, .navbar-expand-lg>.container-xl { padding-right: 0; padding-left: 0; }
    .top-bar .container-lg { padding-left: 0; padding-right: 0;}
    header .top-bar + .navbar.fixed-top { top: 0; }

    /* hamburger menu */
    .hamburger-menu { width: 100%; min-width: 0; }
    .hamburger-menu .close-button-menu { top: 0; right: 0; }
    .hamburger-menu.menu-full-width .hamburger-menu-wrepper { width: 100%; }
    .hamburger-menu.menu-full-width .menu-list { width: 90%; }
    .hamburger-menu.menu-half-width { width: 100%; }

    /* hamburger menu big text */
    .hamburger-menu-big-font .menu-list li { padding: 10px 0; }
    .hamburger-menu-big-font .menu-list li a { font-size: 24px; line-height: 30px; }
    .hamburger-menu-big-font .menu-list li .menu-toggle { top: 27px; }
    .hamburger-menu-big-font .menu-list li .sub-menu-item { padding: 15px 0 0 15px; }

    /* header sidebar */
    .sidebar-nav-action, .side-menu-header { padding-left: 15px; padding-right: 15px; }

    /* page title */
    .page-title-small span { margin-left: 0; padding-left: 0; }
    .page-title-small span:before { display: none; }
    .page-title-large h1 { font-size: 30px; line-height: 40px; }

    /* blog side image */
    .blog-side-image .blog-post { padding: 20px; }
    .blog-side-image .blog-post:nth-child(odd) { padding-right: 20px; }
    .blog-side-image .blog-post:nth-child(even) { padding-left: 20px; }
    .blog-side-image .blog-post-image, .blog-side-image .post-details { width: 100%; }
    .blog-side-image .blog-post:nth-child(even) .blog-post-image { -ms-flex-order: 1; order: 1; }
    .blog-side-image .blog-post:nth-child(even) .post-details { -ms-flex-order: 2; order: 2; }
    .blog-side-image .avtar-image { width: 30px; margin-right: 15px; }
    .blog-side-image .separator { display: inline-block; }

    /* blog modern */
    .blog-modern .blog-post-image { margin-bottom: 100px; }

    /* sidebar */
    .shopping-left-side-bar .shopping-content, .blog-left-side-bar .blog-content { order: 1; -ms-flex-order: 1; }
    .shopping-left-side-bar .shopping-sidebar, .blog-right-side-bar .blog-content { order: 2; -ms-flex-order: 2; }

    /* video icons */
    .video-icon .video-icon-sonar .video-icon-sonar-bfr { top: -30px; left: -30px; }
    .video-icon .video-icon-sonar .video-icon-sonar-afr { top: -15px; left: -15px; }
    .video-icon-large .video-icon .video-icon-sonar .video-icon-sonar-bfr { top: -25px; left: -25px; }
    .video-icon-large .video-icon .video-icon-sonar .video-icon-sonar-afr { top: -10px; left: -10px; }
    .video-icon-medium .video-icon .video-icon-sonar .video-icon-sonar-bfr { top: -25px; left: -25px; }
    .video-icon-medium .video-icon .video-icon-sonar .video-icon-sonar-afr { top: -10px; left: -10px; }
    .video-icon-extra-large .video-icon .video-icon-sonar .video-icon-sonar-afr { top: -10px; left: -10px; }

    /* video icon size */
    .video-icon-double-large .video-icon { width: 80px; height: 80px; font-size: 28px; }
    .video-icon-double-large.video-icon-box .video-icon i { margin-left: 5px; }
    .video-icon-double-large .video-icon .video-icon-sonar .video-icon-sonar-bfr { width: 140px; height: 140px; }
    .video-icon-double-large .video-icon .video-icon-sonar .video-icon-sonar-afr { width: 110px; height: 110px; }

    /* notify popup */
    .mfp-auto-cursor .mfp-content.notify-popup { padding-top: 35px; padding-bottom: 35px;}
    .show-notify-popup .mfp-container { padding-top: 0; }
    .mfp-notify button.mfp-close { top: 0; right: 0; }

    /* accordion style 03 */
    .accordion-style-03 .panel { padding-left: 15px; padding-right: 15px; margin-bottom: 15px; }
    .accordion-style-03 .panel .panel-heading { padding: 15px 25px 15px 0; }

    /* cart and checkout page */
    .total-price-table td, .total-price-table th { padding-top: 15px; padding-bottom: 15px; }

    /* marketing agency */
    footer.home-marketing-agency .footer-horizontal-link li { margin-right: 0; }

    /* interactive list style */
    .fullscreen-hover-list .hover-list-item.active .interactive-number { transform: translateX(50px); -webkit-transform: translateX(50px); -moz-transform: translateX(50px); -ms-transform: translateX(50px); }
    .fullscreen-hover-list .hover-list-item .interactive-line { width: 20px; margin-left: 25px; }
    .fullscreen-hover-list .hover-list-item .interactive-title:after { left: 25px; bottom: 25px; }
    .fullscreen-hover-list .hover-list-item .interactive-title:hover:after { width: calc(100% - 50px); }

    /* interactive portfolio */
    .home-interactive-portfolio .fullscreen-hover-box .interactive-title { font-size: 35px; line-height: 35px; padding: 25px; }
    .fullscreen-hover-list .hover-list-item .interactive-icon { top: 1px; }

    /* photography */
    .home-photography .interactive-banners-style-13 .interactive-banners-image { width: 80%; }
    .home-photography .interactive-banners-style-13 .interactive-banners-content { width: 30%; }
    .home-photography .hover-option-2 .hover-option-content { width: 35%; }

    /* freelancer */
    .home-freelancer .title-extra-large-heavy { line-height: 70px; }
    .home-freelancer .title-extra-large { font-size: 65px; }
    .home-freelancer-bg-img { background: none !important; }

    /* hotel resort */
    .home-hotel-resort .book-now-btn { font-size: 11px !important; padding: 12px 24px !important; }
    .home-hotel-resort .rev_slider_wrapper { height: 450px !important; }

    /* swiper horizontal 3d */
    .swiper-horizontal-3d.swiper-container { padding-bottom: 0; }
    .swiper-horizontal-3d .swiper-slide.swiper-slide-prev, .swiper-horizontal-3d .swiper-slide.swiper-slide-next { opacity: .0; }
    .swiper-horizontal-3d .swiper-slide.swiper-slide-active { box-shadow: none; }

    /*home architecture*/
    .home-architecture .tparrows.tp-leftarrow { transform: translate(0) !important; bottom: 0; right: 0; top: auto !important; left: auto !important; }
    .home-architecture .tparrows.tp-rightarrow { transform: translate(0) !important; bottom: 67px; right: 0; top: auto !important; left: auto !important; }

    /* split slider */
    .home-split-portfolio .title-large { font-size: 70px; line-height: 65px; }

    /* elements social icon page */
    .elements-social .extra-small-icon li, .elements-social .small-icon li, .elements-social .medium-icon li, .elements-social .large-icon li, .elements-social .extra-large-icon li { margin-bottom: 20px; }

    .home-startup .tp-bullet { opacity: 0.5; height: 10px !important; width: 10px !important; }

    /*home decor*/
    .home-decor .tp-bullets { transform: translateX(-50%) !important; bottom: 30px !important; top: auto !important; }
    
    /* text shadow */
    .sm-no-text-shadow { text-shadow: none;}
}

@media screen and (max-width: 575px) {
    /* reset */
    section.big-section { padding:50px 0; }
    .xs-last-order { order: 10; }

    /* text size */
    .text-extra-big-2 { font-size: 230px; line-height: 190px; }

    /* lineheight */
    .xs-line-height-0px { line-height: 0px; }
    .xs-line-height-8px { line-height: 8px; }
    .xs-line-height-10px { line-height: 10px; }
    .xs-line-height-14px { line-height: 14px; }
    .xs-line-height-15px { line-height: 15px; }
    .xs-line-height-16px { line-height: 16px; }
    .xs-line-height-18px { line-height: 18px; }
    .xs-line-height-20px { line-height: 20px; }
    .xs-line-height-22px { line-height: 22px; }
    .xs-line-height-24px { line-height: 24px; }
    .xs-line-height-26px { line-height: 26px; }
    .xs-line-height-28px { line-height: 28px; }
    .xs-line-height-30px { line-height: 30px; }
    .xs-line-height-32px { line-height: 32px; }
    .xs-line-height-34px { line-height: 34px; }
    .xs-line-height-36px { line-height: 36px; }
    .xs-line-height-38px { line-height: 38px; }
    .xs-line-height-40px { line-height: 40px; }
    .xs-line-height-50px { line-height: 50px; }
    .xs-line-height-140px { line-height: 140px; }
    .xs-line-height-normal { line-height: normal; }

    /* letter spacing minus */
    .xs-letter-spacing-minus-1-half { letter-spacing: -0.50px; }
    .xs-letter-spacing-minus-1px { letter-spacing: -1px; }
    .xs-letter-spacing-minus-2px { letter-spacing: -2px; }
    .xs-letter-spacing-minus-3px { letter-spacing: -3px; }
    .xs-letter-spacing-minus-4px { letter-spacing: -4px; }
    .xs-letter-spacing-minus-5px { letter-spacing: -5px; }

    /* absolute middle center */
    .xs-absolute-middle-center { left: 50%; top: 50%; position: absolute; -ms-transform: translateX(-50%) translateY(-50%); -moz-transform: translateX(-50%) translateY(-50%); -webkit-transform: translateX(-50%) translateY(-50%); transform: translateX(-50%) translateY(-50%); }

    /* background image */
    .xs-background-image-none { background: inherit !important; }
    .xs-background-position-left { background-position: left center; }
    .xs-background-position-right { background-position: right center; }
    .xs-background-position-top { background-position: right top; }
    .xs-background-position-center { background-position: center; }
    .xs-background-position-left-top { background-position: left top; }

    /* overlap */
    .text-overlap-style-04 { font-size: 55px; line-height: 65px; }

    /* box shadow */
    .xs-box-shadow-none { box-shadow: none; }

    /* margin */
    .xs-margin-one-all { margin:1%; }
    .xs-margin-two-all { margin:2%; }
    .xs-margin-three-all { margin:3%; }
    .xs-margin-four-all { margin:4%; }
    .xs-margin-five-all { margin:5%; }
    .xs-margin-six-all { margin:6%; }
    .xs-margin-seven-all { margin:7%; }
    .xs-margin-eight-all { margin:8%; }
    .xs-margin-nine-all { margin:9%; }
    .xs-margin-ten-all { margin:10%; }
    .xs-margin-eleven-all { margin:11%; }
    .xs-margin-twelve-all { margin:12%; }
    .xs-margin-thirteen-all { margin:13%; }
    .xs-margin-fourteen-all { margin:14%; }
    .xs-margin-fifteen-all { margin:15%; }
    .xs-margin-sixteen-all { margin:16%; }
    .xs-margin-seventeen-all { margin:17%; }
    .xs-margin-eighteen-all { margin:18%; }
    .xs-margin-nineteen-all { margin:19%; }
    .xs-margin-twenty-all { margin:20%; }
    .xs-margin-5px-all { margin:5px; }
    .xs-margin-10px-all { margin:10px; }
    .xs-margin-15px-all { margin:15px; }
    .xs-margin-20px-all { margin:20px; }
    .xs-margin-25px-all { margin:25px; }
    .xs-margin-30px-all { margin:30px; }
    .xs-margin-35px-all { margin:35px; }
    .xs-margin-40px-all { margin:40px; }
    .xs-margin-45px-all { margin:45px; }
    .xs-margin-50px-all { margin:50px; }
    .xs-margin-55px-all { margin:55px; }
    .xs-margin-60px-all { margin:60px; }
    .xs-margin-65px-all { margin:65px; }
    .xs-margin-70px-all { margin:70px; }
    .xs-margin-75px-all { margin:75px; }
    .xs-margin-80px-all { margin:80px; }
    .xs-margin-85px-all { margin:85px; }
    .xs-margin-90px-all { margin:90px; }
    .xs-margin-95px-all { margin:95px; }
    .xs-margin-100px-all { margin:100px; }
    .xs-margin-1-rem-all { margin: 1rem; }
    .xs-margin-1-half-rem-all { margin: 1.5rem; }
    .xs-margin-2-rem-all { margin: 2rem; }
    .xs-margin-2-half-rem-all { margin: 2.5rem; }
    .xs-margin-3-rem-all { margin: 3rem; }
    .xs-margin-3-half-rem-all { margin: 3.5rem; }
    .xs-margin-4-rem-all { margin: 4rem; }
    .xs-margin-4-half-rem-all { margin: 4.5rem; }
    .xs-margin-5-rem-all { margin: 5rem; }
    .xs-margin-5-half-rem-all { margin: 5.5rem; }
    .xs-margin-6-rem-all { margin: 6rem; }
    .xs-margin-6-half-rem-all { margin: 6.5rem; }
    .xs-margin-7-rem-all { margin: 7rem; }
    .xs-margin-7-half-rem-all { margin: 7.5rem; }
    .xs-margin-8-rem-all { margin: 8rem; }
    .xs-margin-8-half-rem-all { margin: 8.5rem; }
    .xs-margin-9-rem-all { margin: 9rem; }
    .xs-margin-9-half-rem-all { margin: 9.5rem; }
    .xs-margin-10-rem-all { margin: 10rem; }
    .xs-margin-10-half-rem-all { margin: 10.5rem; }

    /* margin top */
    .xs-margin-one-top { margin-top:1%; }
    .xs-margin-two-top { margin-top:2%; }
    .xs-margin-three-top { margin-top:3%; }
    .xs-margin-four-top { margin-top:4%; }
    .xs-margin-five-top { margin-top:5%; }
    .xs-margin-six-top { margin-top:6%; }
    .xs-margin-seven-top { margin-top:7%; }
    .xs-margin-eight-top { margin-top:8%; }
    .xs-margin-nine-top { margin-top:9%; }
    .xs-margin-ten-top { margin-top:10%; }
    .xs-margin-eleven-top { margin-top:11%; }
    .xs-margin-twelve-top { margin-top:12%; }
    .xs-margin-thirteen-top { margin-top:13%; }
    .xs-margin-fourteen-top { margin-top:14%; }
    .xs-margin-fifteen-top { margin-top:15%; }
    .xs-margin-sixteen-top { margin-top:16%; }
    .xs-margin-seventeen-top { margin-top:17%; }
    .xs-margin-eighteen-top { margin-top:18%; }
    .xs-margin-nineteen-top { margin-top:19%; }
    .xs-margin-twenty-top { margin-top:20%; }
    .xs-margin-5px-top { margin-top:5px; }
    .xs-margin-10px-top { margin-top:10px; }
    .xs-margin-15px-top { margin-top:15px; }
    .xs-margin-20px-top { margin-top:20px; }
    .xs-margin-25px-top { margin-top:25px; }
    .xs-margin-30px-top { margin-top:30px; }
    .xs-margin-35px-top { margin-top:35px; }
    .xs-margin-40px-top { margin-top:40px; }
    .xs-margin-45px-top { margin-top:45px; }
    .xs-margin-50px-top { margin-top:50px; }
    .xs-margin-55px-top { margin-top:55px; }
    .xs-margin-60px-top { margin-top:60px; }
    .xs-margin-65px-top { margin-top:65px; }
    .xs-margin-70px-top { margin-top:70px; }
    .xs-margin-75px-top { margin-top:75px; }
    .xs-margin-80px-top { margin-top:80px; }
    .xs-margin-85px-top { margin-top:85px; }
    .xs-margin-90px-top { margin-top:90px; }
    .xs-margin-95px-top { margin-top:95px; }
    .xs-margin-100px-top { margin-top:100px; }
    .xs-margin-1-rem-top { margin-top: 1rem; }
    .xs-margin-1-half-rem-top { margin-top: 1.5rem; }
    .xs-margin-2-rem-top { margin-top: 2rem; }
    .xs-margin-2-half-rem-top { margin-top: 2.5rem; }
    .xs-margin-3-rem-top { margin-top: 3rem; }
    .xs-margin-3-half-rem-top { margin-top: 3.5rem; }
    .xs-margin-4-rem-top { margin-top: 4rem; }
    .xs-margin-4-half-rem-top { margin-top: 4.5rem; }
    .xs-margin-5-rem-top { margin-top: 5rem; }
    .xs-margin-5-half-rem-top { margin-top: 5.5rem; }
    .xs-margin-6-rem-top { margin-top: 6rem; }
    .xs-margin-6-half-rem-top { margin-top: 6.5rem; }
    .xs-margin-7-rem-top { margin-top: 7rem; }
    .xs-margin-7-half-rem-top { margin-top: 7.5rem; }
    .xs-margin-8-rem-top { margin-top: 8rem; }
    .xs-margin-8-half-rem-top { margin-top: 8.5rem; }
    .xs-margin-9-rem-top { margin-top: 9rem; }
    .xs-margin-9-half-rem-top { margin-top: 9.5rem; }
    .xs-margin-10-rem-top { margin-top: 10rem; }
    .xs-margin-10-half-rem-top { margin-top: 10.5rem; }

    /* margin bottom */
    .xs-margin-one-bottom { margin-bottom:1%; }
    .xs-margin-two-bottom { margin-bottom:2%; }
    .xs-margin-three-bottom { margin-bottom:3%; }
    .xs-margin-four-bottom { margin-bottom:4%; }
    .xs-margin-five-bottom { margin-bottom:5%; }
    .xs-margin-six-bottom { margin-bottom:6%; }
    .xs-margin-seven-bottom { margin-bottom:7%; }
    .xs-margin-eight-bottom { margin-bottom:8%; }
    .xs-margin-nine-bottom { margin-bottom:9%; }
    .xs-margin-ten-bottom { margin-bottom:10%; }
    .xs-margin-eleven-bottom { margin-bottom:11%; }
    .xs-margin-twelve-bottom { margin-bottom:12%; }
    .xs-margin-thirteen-bottom { margin-bottom:13%; }
    .xs-margin-fourteen-bottom { margin-bottom:14%; }
    .xs-margin-fifteen-bottom { margin-bottom:15%; }
    .xs-margin-sixteen-bottom { margin-bottom:16%; }
    .xs-margin-seventeen-bottom { margin-bottom:17%; }
    .xs-margin-eighteen-bottom { margin-bottom:18%; }
    .xs-margin-nineteen-bottom { margin-bottom:19%; }
    .xs-margin-twenty-bottom { margin-bottom:20%; }
    .xs-margin-5px-bottom { margin-bottom:5px; }
    .xs-margin-10px-bottom { margin-bottom:10px; }
    .xs-margin-15px-bottom { margin-bottom:15px; }
    .xs-margin-20px-bottom { margin-bottom:20px; }
    .xs-margin-25px-bottom { margin-bottom:25px; }
    .xs-margin-30px-bottom { margin-bottom:30px; }
    .xs-margin-35px-bottom { margin-bottom:35px; }
    .xs-margin-40px-bottom { margin-bottom:40px; }
    .xs-margin-45px-bottom { margin-bottom:45px; }
    .xs-margin-50px-bottom { margin-bottom:50px; }
    .xs-margin-55px-bottom { margin-bottom:55px; }
    .xs-margin-60px-bottom { margin-bottom:60px; }
    .xs-margin-65px-bottom { margin-bottom:65px; }
    .xs-margin-70px-bottom { margin-bottom:70px; }
    .xs-margin-75px-bottom { margin-bottom:75px; }
    .xs-margin-80px-bottom { margin-bottom:80px; }
    .xs-margin-85px-bottom { margin-bottom:85px; }
    .xs-margin-90px-bottom { margin-bottom:90px; }
    .xs-margin-95px-bottom { margin-bottom:95px; }
    .xs-margin-100px-bottom { margin-bottom:100px; }
    .xs-margin-1-rem-bottom { margin-bottom: 1rem; }
    .xs-margin-1-half-rem-bottom { margin-bottom: 1.5rem; }
    .xs-margin-2-rem-bottom { margin-bottom: 2rem; }
    .xs-margin-2-half-rem-bottom { margin-bottom: 2.5rem; }
    .xs-margin-3-rem-bottom { margin-bottom: 3rem; }
    .xs-margin-3-half-rem-bottom { margin-bottom: 3.5rem; }
    .xs-margin-4-rem-bottom { margin-bottom: 4rem; }
    .xs-margin-4-half-rem-bottom { margin-bottom: 4.5rem; }
    .xs-margin-5-rem-bottom { margin-bottom: 5rem; }
    .xs-margin-5-half-rem-bottom { margin-bottom: 5.5rem; }
    .xs-margin-6-rem-bottom { margin-bottom: 6rem; }
    .xs-margin-6-half-rem-bottom { margin-bottom: 6.5rem; }
    .xs-margin-7-rem-bottom { margin-bottom: 7rem; }
    .xs-margin-7-half-rem-bottom { margin-bottom: 7.5rem; }
    .xs-margin-8-rem-bottom { margin-bottom: 8rem; }
    .xs-margin-8-half-rem-bottom { margin-bottom: 8.5rem; }
    .xs-margin-9-rem-bottom { margin-bottom: 9rem; }
    .xs-margin-9-half-rem-bottom { margin-bottom: 9.5rem; }
    .xs-margin-10-rem-bottom { margin-bottom: 10rem; }
    .xs-margin-10-half-rem-bottom { margin-bottom: 10.5rem; }

    /* margin right */
    .xs-margin-one-right { margin-right:1%; }
    .xs-margin-two-right { margin-right:2%; }
    .xs-margin-three-right { margin-right:3%; }
    .xs-margin-four-right { margin-right:4%; }
    .xs-margin-five-right { margin-right:5%; }
    .xs-margin-six-right { margin-right:6%; }
    .xs-margin-seven-right { margin-right:7%; }
    .xs-margin-eight-right { margin-right:8%; }
    .xs-margin-nine-right { margin-right:9%; }
    .xs-margin-ten-right { margin-right:10%; }
    .xs-margin-eleven-right { margin-right:11%; }
    .xs-margin-twelve-right { margin-right:12%; }
    .xs-margin-thirteen-right { margin-right:13%; }
    .xs-margin-fourteen-right { margin-right:14%; }
    .xs-margin-fifteen-right { margin-right:15%; }
    .xs-margin-sixteen-right { margin-right:16%; }
    .xs-margin-seventeen-right { margin-right:17%; }
    .xs-margin-eighteen-right { margin-right:18%; }
    .xs-margin-nineteen-right { margin-right:19%; }
    .xs-margin-twenty-right { margin-right:20%; }
    .xs-margin-5px-right { margin-right:5px; }
    .xs-margin-10px-right { margin-right:10px; }
    .xs-margin-15px-right { margin-right:15px; }
    .xs-margin-20px-right { margin-right:20px; }
    .xs-margin-25px-right { margin-right:25px; }
    .xs-margin-30px-right { margin-right:30px; }
    .xs-margin-35px-right { margin-right:35px; }
    .xs-margin-40px-right { margin-right:40px; }
    .xs-margin-45px-right { margin-right:45px; }
    .xs-margin-50px-right { margin-right:50px; }
    .xs-margin-55px-right { margin-right:55px; }
    .xs-margin-60px-right { margin-right:60px; }
    .xs-margin-65px-right { margin-right:65px; }
    .xs-margin-70px-right { margin-right:70px; }
    .xs-margin-75px-right { margin-right:75px; }
    .xs-margin-80px-right { margin-right:80px; }
    .xs-margin-85px-right { margin-right:85px; }
    .xs-margin-90px-right { margin-right:90px; }
    .xs-margin-95px-right { margin-right:95px; }
    .xs-margin-100px-right { margin-right:100px; }
    .xs-margin-1-rem-right { margin-right: 1rem; }
    .xs-margin-1-half-rem-right { margin-right: 1.5rem; }
    .xs-margin-2-rem-right { margin-right: 2rem; }
    .xs-margin-2-half-rem-right { margin-right: 2.5rem; }
    .xs-margin-3-rem-right { margin-right: 3rem; }
    .xs-margin-3-half-rem-right { margin-right: 3.5rem; }
    .xs-margin-4-rem-right { margin-right: 4rem; }
    .xs-margin-4-half-rem-right { margin-right: 4.5rem; }
    .xs-margin-5-rem-right { margin-right: 5rem; }
    .xs-margin-5-half-rem-right { margin-right: 5.5rem; }
    .xs-margin-6-rem-right { margin-right: 6rem; }
    .xs-margin-6-half-rem-right { margin-right: 6.5rem; }
    .xs-margin-7-rem-right { margin-right: 7rem; }
    .xs-margin-7-half-rem-right { margin-right: 7.5rem; }
    .xs-margin-8-rem-right { margin-right: 8rem; }
    .xs-margin-8-half-rem-right { margin-right: 8.5rem; }
    .xs-margin-9-rem-right { margin-right: 9rem; }
    .xs-margin-9-half-rem-right { margin-right: 9.5rem; }
    .xs-margin-10-rem-right { margin-right: 10rem; }
    .xs-margin-10-half-rem-right { margin-right: 10.5rem; }

    /* margin left */
    .xs-margin-one-left { margin-left:1%; }
    .xs-margin-two-left { margin-left:2%; }
    .xs-margin-three-left { margin-left:3%; }
    .xs-margin-four-left { margin-left:4%; }
    .xs-margin-five-left { margin-left:5%; }
    .xs-margin-six-left { margin-left:6%; }
    .xs-margin-seven-left { margin-left:7%; }
    .xs-margin-eight-left { margin-left:8%; }
    .xs-margin-nine-left { margin-left:9%; }
    .xs-margin-ten-left { margin-left:10%; }
    .xs-margin-eleven-left { margin-left:11%; }
    .xs-margin-twelve-left { margin-left:12%; }
    .xs-margin-thirteen-left { margin-left:13%; }
    .xs-margin-fourteen-left { margin-left:14%; }
    .xs-margin-fifteen-left { margin-left:15%; }
    .xs-margin-sixteen-left { margin-left:16%; }
    .xs-margin-seventeen-left { margin-left:17%; }
    .xs-margin-eighteen-left { margin-left:18%; }
    .xs-margin-nineteen-left { margin-left:19%; }
    .xs-margin-twenty-left { margin-left:20%; }
    .xs-margin-5px-left { margin-left:5px; }
    .xs-margin-10px-left { margin-left:10px; }
    .xs-margin-15px-left { margin-left:15px; }
    .xs-margin-20px-left { margin-left:20px; }
    .xs-margin-25px-left { margin-left:25px; }
    .xs-margin-30px-left { margin-left:30px; }
    .xs-margin-35px-left { margin-left:35px; }
    .xs-margin-40px-left { margin-left:40px; }
    .xs-margin-45px-left { margin-left:45px; }
    .xs-margin-50px-left { margin-left:50px; }
    .xs-margin-55px-left { margin-left:55px; }
    .xs-margin-60px-left { margin-left:60px; }
    .xs-margin-65px-left { margin-left:65px; }
    .xs-margin-70px-left { margin-left:70px; }
    .xs-margin-75px-left { margin-left:75px; }
    .xs-margin-80px-left { margin-left:80px; }
    .xs-margin-85px-left { margin-left:85px; }
    .xs-margin-90px-left { margin-left:90px; }
    .xs-margin-95px-left { margin-left:95px; }
    .xs-margin-100px-left { margin-left:100px; }
    .xs-margin-1-rem-left { margin-left: 1rem; }
    .xs-margin-1-half-rem-left { margin-left: 1.5rem; }
    .xs-margin-2-rem-left { margin-left: 2rem; }
    .xs-margin-2-half-rem-left { margin-left: 2.5rem; }
    .xs-margin-3-rem-left { margin-left: 3rem; }
    .xs-margin-3-half-rem-left { margin-left: 3.5rem; }
    .xs-margin-4-rem-left { margin-left: 4rem; }
    .xs-margin-4-half-rem-left { margin-left: 4.5rem; }
    .xs-margin-5-rem-left { margin-left: 5rem; }
    .xs-margin-5-half-rem-left { margin-left: 5.5rem; }
    .xs-margin-6-rem-left { margin-left: 6rem; }
    .xs-margin-6-half-rem-left { margin-left: 6.5rem; }
    .xs-margin-7-rem-left { margin-left: 7rem; }
    .xs-margin-7-half-rem-left { margin-left: 7.5rem; }
    .xs-margin-8-rem-left { margin-left: 8rem; }
    .xs-margin-8-half-rem-left { margin-left: 8.5rem; }
    .xs-margin-9-rem-left { margin-left: 9rem; }
    .xs-margin-9-half-rem-left { margin-left: 9.5rem; }
    .xs-margin-10-rem-left { margin-left: 10rem; }
    .xs-margin-10-half-rem-left { margin-left: 10.5rem; }

    /* margin left right */
    .xs-margin-one-lr { margin-left:1%; margin-right:1%; }
    .xs-margin-two-lr { margin-left:2%; margin-right:2%; }
    .xs-margin-three-lr { margin-left:3%; margin-right:3%; }
    .xs-margin-four-lr { margin-left:4%; margin-right:4%; }
    .xs-margin-five-lr { margin-left:5%; margin-right:5%; }
    .xs-margin-six-lr { margin-left:6%; margin-right:6%; }
    .xs-margin-seven-lr { margin-left:7%; margin-right:7%; }
    .xs-margin-eight-lr { margin-left:8%; margin-right:8%; }
    .xs-margin-nine-lr { margin-left:9%; margin-right:9%; }
    .xs-margin-ten-lr { margin-left:10%; margin-right:10%; }
    .xs-margin-eleven-lr { margin-left:11%; margin-right:11%; }
    .xs-margin-twelve-lr { margin-left:12%; margin-right:12%; }
    .xs-margin-thirteen-lr { margin-left:13%; margin-right:13%; }
    .xs-margin-fourteen-lr { margin-left:14%; margin-right:14%; }
    .xs-margin-fifteen-lr { margin-left:15%; margin-right:15%; }
    .xs-margin-sixteen-lr { margin-left:16%; margin-right:16%; }
    .xs-margin-seventeen-lr { margin-left:17%; margin-right:17%; }
    .xs-margin-eighteen-lr { margin-left:18%; margin-right:18%; }
    .xs-margin-nineteen-lr { margin-left:19%; margin-right:19%; }
    .xs-margin-twenty-lr { margin-left:20%; margin-right:20%; }
    .xs-margin-5px-lr { margin-left:5px; margin-right:5px; }
    .xs-margin-10px-lr { margin-left:10px; margin-right:10px; }
    .xs-margin-15px-lr { margin-left:15px; margin-right:15px; }
    .xs-margin-20px-lr { margin-left:20px; margin-right:20px; }
    .xs-margin-25px-lr { margin-left:25px; margin-right:25px; }
    .xs-margin-30px-lr { margin-left:30px; margin-right:30px; }
    .xs-margin-35px-lr { margin-left:35px; margin-right:35px; }
    .xs-margin-40px-lr { margin-left:40px; margin-right:40px; }
    .xs-margin-45px-lr { margin-left:45px; margin-right:45px; }
    .xs-margin-50px-lr { margin-left:50px; margin-right:50px; }
    .xs-margin-55px-lr { margin-left:55px; margin-right:55px; }
    .xs-margin-60px-lr { margin-left:60px; margin-right:60px; }
    .xs-margin-65px-lr { margin-left:65px; margin-right:65px; }
    .xs-margin-70px-lr { margin-left:70px; margin-right:70px; }
    .xs-margin-75px-lr { margin-left:75px; margin-right:75px; }
    .xs-margin-80px-lr { margin-left:80px; margin-right:80px; }
    .xs-margin-85px-lr { margin-left:85px; margin-right:85px; }
    .xs-margin-90px-lr { margin-left:90px; margin-right:90px; }
    .xs-margin-95px-lr { margin-left:95px; margin-right:95px; }
    .xs-margin-100px-lr { margin-left:100px; margin-right:100px; }
    .xs-margin-1-rem-lr { margin-left: 1rem; margin-right: 1rem; }
    .xs-margin-1-half-rem-lr { margin-left: 1.5rem; margin-right: 1.5rem; }
    .xs-margin-2-rem-lr { margin-left: 2rem; margin-right: 2rem; }
    .xs-margin-2-half-rem-lr { margin-left: 2.5rem; margin-right: 2.5rem; }
    .xs-margin-3-rem-lr { margin-left: 3rem; margin-right: 3rem; }
    .xs-margin-3-half-rem-lr { margin-left: 3.5rem; margin-right: 3.5rem; }
    .xs-margin-4-rem-lr { margin-left: 4rem; margin-right: 4rem; }
    .xs-margin-4-half-rem-lr { margin-left: 4.5rem; margin-right: 4.5rem; }
    .xs-margin-5-rem-lr { margin-left: 5rem; margin-right: 5rem; }
    .xs-margin-5-half-rem-lr { margin-left: 5.5rem; margin-right: 5.5rem; }
    .xs-margin-6-rem-lr { margin-left: 6rem; margin-right: 6rem; }
    .xs-margin-6-half-rem-lr { margin-left: 6.5rem; margin-right: 6.5rem; }
    .xs-margin-7-rem-lr { margin-left: 7rem; margin-right: 7rem; }
    .xs-margin-7-half-rem-lr { margin-left: 7.5rem; margin-right: 7.5rem; }
    .xs-margin-8-rem-lr { margin-left: 8rem; margin-right: 8rem; }
    .xs-margin-8-half-rem-lr { margin-left: 8.5rem; margin-right: 8.5rem; }
    .xs-margin-9-rem-lr { margin-left: 9rem; margin-right: 9rem; }
    .xs-margin-9-half-rem-lr { margin-left: 9.5rem; margin-right: 9.5rem; }
    .xs-margin-10-rem-lr { margin-left: 10rem; margin-right: 10rem; }
    .xs-margin-10-half-rem-lr { margin-left: 10.5rem; margin-right: 10.5rem; }

    /* margin top bottom */
    .xs-margin-one-tb { margin-top:1%; margin-bottom:1%; }
    .xs-margin-two-tb { margin-top:2%; margin-bottom:2%; }
    .xs-margin-three-tb { margin-top:3%; margin-bottom:3%; }
    .xs-margin-four-tb { margin-top:4%; margin-bottom:4%; }
    .xs-margin-five-tb { margin-top:5%; margin-bottom:5%; }
    .xs-margin-six-tb { margin-top:6%; margin-bottom:6%; }
    .xs-margin-seven-tb { margin-top:7%; margin-bottom:7%; }
    .xs-margin-eight-tb { margin-top:8%; margin-bottom:8%; }
    .xs-margin-nine-tb { margin-top:9%; margin-bottom:9%; }
    .xs-margin-ten-tb { margin-top:10%; margin-bottom:10%; }
    .xs-margin-eleven-tb { margin-top:11%; margin-bottom:11%; }
    .xs-margin-twelve-tb { margin-top:12%; margin-bottom:12%; }
    .xs-margin-thirteen-tb { margin-top:13%; margin-bottom:13%; }
    .xs-margin-fourteen-tb { margin-top:14%; margin-bottom:14%; }
    .xs-margin-fifteen-tb { margin-top:15%; margin-bottom:15%; }
    .xs-margin-sixteen-tb { margin-top:16%; margin-bottom:16%; }
    .xs-margin-seventeen-tb { margin-top:17%; margin-bottom:17%; }
    .xs-margin-eighteen-tb { margin-top:18%; margin-bottom:18%; }
    .xs-margin-nineteen-tb { margin-top:19%; margin-bottom:19%; }
    .xs-margin-twenty-tb { margin-top:20%; margin-bottom:20%; }
    .xs-margin-5px-tb { margin-top:5px; margin-bottom:5px; }
    .xs-margin-10px-tb { margin-top:10px; margin-bottom:10px; }
    .xs-margin-15px-tb { margin-top:15px; margin-bottom:15px; }
    .xs-margin-20px-tb { margin-top:20px; margin-bottom:20px; }
    .xs-margin-25px-tb { margin-top:25px; margin-bottom:25px; }
    .xs-margin-30px-tb { margin-top:30px; margin-bottom:30px; }
    .xs-margin-35px-tb { margin-top:35px; margin-bottom:35px; }
    .xs-margin-40px-tb { margin-top:40px; margin-bottom:40px; }
    .xs-margin-45px-tb { margin-top:45px; margin-bottom:45px; }
    .xs-margin-50px-tb { margin-top:50px; margin-bottom:50px; }
    .xs-margin-55px-tb { margin-top:55px; margin-bottom:55px; }
    .xs-margin-60px-tb { margin-top:60px; margin-bottom:60px; }
    .xs-margin-65px-tb { margin-top:65px; margin-bottom:65px; }
    .xs-margin-70px-tb { margin-top:70px; margin-bottom:70px; }
    .xs-margin-75px-tb { margin-top:75px; margin-bottom:75px; }
    .xs-margin-80px-tb { margin-top:80px; margin-bottom:80px; }
    .xs-margin-85px-tb { margin-top:85px; margin-bottom:85px; }
    .xs-margin-90px-tb { margin-top:90px; margin-bottom:90px; }
    .xs-margin-95px-tb { margin-top:95px; margin-bottom:95px; }
    .xs-margin-100px-tb { margin-top:100px; margin-bottom:100px; }
    .xs-margin-1-rem-tb { margin-top: 1rem; margin-bottom: 1rem; }
    .xs-margin-1-half-rem-tb { margin-top: 1.5rem; margin-bottom: 1.5rem; }
    .xs-margin-2-rem-tb { margin-top: 2rem; margin-bottom: 2rem; }
    .xs-margin-2-half-rem-tb { margin-top: 2.5rem; margin-bottom: 2.5rem; }
    .xs-margin-3-rem-tb { margin-top: 3rem; margin-bottom: 3rem; }
    .xs-margin-3-half-rem-tb { margin-top: 3.5rem; margin-bottom: 3.5rem; }
    .xs-margin-4-rem-tb { margin-top: 4rem; margin-bottom: 4rem; }
    .xs-margin-4-half-rem-tb { margin-top: 4.5rem; margin-bottom: 4.5rem; }
    .xs-margin-5-rem-tb { margin-top: 5rem; margin-bottom: 5rem; }
    .xs-margin-5-half-rem-tb { margin-top: 5.5rem; margin-bottom: 5.5rem; }
    .xs-margin-6-rem-tb { margin-top: 6rem; margin-bottom: 6rem; }
    .xs-margin-6-half-rem-tb { margin-top: 6.5rem; margin-bottom: 6.5rem; }
    .xs-margin-7-rem-tb { margin-top: 7rem; margin-bottom: 7rem; }
    .xs-margin-7-half-rem-tb { margin-top: 7.5rem; margin-bottom: 7.5rem; }
    .xs-margin-8-rem-tb { margin-top: 8rem; margin-bottom: 8rem; }
    .xs-margin-8-half-rem-tb { margin-top: 8.5rem; margin-bottom: 8.5rem; }
    .xs-margin-9-rem-tb { margin-top: 9rem; margin-bottom: 9rem; }
    .xs-margin-9-half-rem-tb { margin-top: 9.5rem; margin-bottom: 9.5rem; }
    .xs-margin-10-rem-tb { margin-top: 10rem; margin-bottom: 10rem; }
    .xs-margin-10-half-rem-tb { margin-top: 10.5rem; margin-bottom: 10.5rem; }

    .xs-margin-auto-lr { margin-left: auto !important; margin-right: auto !important }
    .xs-margin-auto { margin: auto; }
    .xs-no-margin { margin: 0 !important; }
    .xs-no-margin-top { margin-top: 0 !important; }
    .xs-no-margin-bottom { margin-bottom: 0 !important; }
    .xs-no-margin-left { margin-left: 0 !important; }
    .xs-no-margin-right { margin-right: 0 !important; }
    .xs-no-margin-tb { margin-top: 0 !important; margin-bottom: 0 !important; }
    .xs-no-margin-lr { margin-right: 0 !important; margin-left: 0 !important; }

    /* padding */
    .xs-padding-one-all { padding:1%; }
    .xs-padding-two-all { padding:2%; }
    .xs-padding-three-all { padding:3%; }
    .xs-padding-four-all { padding:4%; }
    .xs-padding-five-all { padding:5%; }
    .xs-padding-six-all { padding:6%; }
    .xs-padding-seven-all { padding:7%; }
    .xs-padding-eight-all { padding:8%; }
    .xs-padding-nine-all { padding:9%; }
    .xs-padding-ten-all { padding:10%; }
    .xs-padding-eleven-all { padding:11%; }
    .xs-padding-twelve-all { padding:12%; }
    .xs-padding-thirteen-all { padding:13%; }
    .xs-padding-fourteen-all { padding:14%; }
    .xs-padding-fifteen-all { padding:15%; }
    .xs-padding-sixteen-all { padding:16%; }
    .xs-padding-seventeen-all { padding:17%; }
    .xs-padding-eighteen-all { padding:18%; }
    .xs-padding-nineteen-all { padding:19%; }
    .xs-padding-twenty-all { padding:20%; }
    .xs-padding-5px-all { padding:5px; }
    .xs-padding-10px-all { padding:10px; }
    .xs-padding-15px-all { padding:15px; }
    .xs-padding-20px-all { padding:20px; }
    .xs-padding-25px-all { padding:25px; }
    .xs-padding-30px-all { padding:30px; }
    .xs-padding-35px-all { padding:35px; }
    .xs-padding-40px-all { padding:40px; }
    .xs-padding-45px-all { padding:45px; }
    .xs-padding-50px-all { padding:50px; }
    .xs-padding-55px-all { padding:55px; }
    .xs-padding-60px-all { padding:60px; }
    .xs-padding-65px-all { padding:65px; }
    .xs-padding-70px-all { padding:70px; }
    .xs-padding-75px-all { padding:75px; }
    .xs-padding-80px-all { padding:80px; }
    .xs-padding-85px-all { padding:85px; }
    .xs-padding-90px-all { padding:90px; }
    .xs-padding-95px-all { padding:95px; }
    .xs-padding-100px-all { padding:100px; }
    .xs-padding-1-rem-all { padding: 1rem; }
    .xs-padding-1-half-rem-all { padding: 1.5rem; }
    .xs-padding-2-rem-all { padding: 2rem; }
    .xs-padding-2-half-rem-all { padding: 2.5rem; }
    .xs-padding-3-rem-all { padding: 3rem; }
    .xs-padding-3-half-rem-all { padding: 3.5rem; }
    .xs-padding-4-rem-all { padding: 4rem; }
    .xs-padding-4-half-rem-all { padding: 4.5rem; }
    .xs-padding-5-rem-all { padding: 5rem; }
    .xs-padding-5-half-rem-all { padding: 5.5rem; }
    .xs-padding-6-rem-all { padding: 6rem; }
    .xs-padding-6-half-rem-all { padding: 6.5rem; }
    .xs-padding-7-rem-all { padding: 7rem; }
    .xs-padding-7-half-rem-all { padding: 7.5rem; }
    .xs-padding-8-rem-all { padding: 8rem; }
    .xs-padding-8-half-rem-all { padding: 8.5rem; }
    .xs-padding-9-rem-all { padding: 9rem; }
    .xs-padding-9-half-rem-all { padding: 9.5rem; }
    .xs-padding-10-rem-all { padding: 10rem; }
    .xs-padding-10-half-rem-all { padding: 10.5rem; }

    /* padding top */
    .xs-padding-one-top { padding-top:1%; }
    .xs-padding-two-top { padding-top:2%; }
    .xs-padding-three-top { padding-top:3%; }
    .xs-padding-four-top { padding-top:4%; }
    .xs-padding-five-top { padding-top:5%; }
    .xs-padding-six-top { padding-top:6%; }
    .xs-padding-seven-top { padding-top:7%; }
    .xs-padding-eight-top { padding-top:8%; }
    .xs-padding-nine-top { padding-top:9%; }
    .xs-padding-ten-top { padding-top:10%; }
    .xs-padding-eleven-top { padding-top:11%; }
    .xs-padding-twelve-top { padding-top:12%; }
    .xs-padding-thirteen-top { padding-top:13%; }
    .xs-padding-fourteen-top { padding-top:14%; }
    .xs-padding-fifteen-top { padding-top:15%; }
    .xs-padding-sixteen-top { padding-top:16%; }
    .xs-padding-seventeen-top { padding-top:17%; }
    .xs-padding-eighteen-top { padding-top:18%; }
    .xs-padding-nineteen-top { padding-top:19%; }
    .xs-padding-twenty-top { padding-top:20%; }
    .xs-padding-5px-top { padding-top:5px; }
    .xs-padding-10px-top { padding-top:10px; }
    .xs-padding-15px-top { padding-top:15px; }
    .xs-padding-20px-top { padding-top:20px; }
    .xs-padding-25px-top { padding-top:25px; }
    .xs-padding-30px-top { padding-top:30px; }
    .xs-padding-35px-top { padding-top:35px; }
    .xs-padding-40px-top { padding-top:40px; }
    .xs-padding-45px-top { padding-top:45px; }
    .xs-padding-50px-top { padding-top:50px; }
    .xs-padding-55px-top { padding-top:55px; }
    .xs-padding-60px-top { padding-top:60px; }
    .xs-padding-65px-top { padding-top:65px; }
    .xs-padding-70px-top { padding-top:70px; }
    .xs-padding-75px-top { padding-top:75px; }
    .xs-padding-80px-top { padding-top:80px; }
    .xs-padding-85px-top { padding-top:85px; }
    .xs-padding-90px-top { padding-top:90px; }
    .xs-padding-95px-top { padding-top:95px; }
    .xs-padding-100px-top { padding-top:100px; }
    .xs-padding-1-rem-top { padding-top: 1rem; }
    .xs-padding-1-half-rem-top { padding-top: 1.5rem; }
    .xs-padding-2-rem-top { padding-top: 2rem; }
    .xs-padding-2-half-rem-top { padding-top: 2.5rem; }
    .xs-padding-3-rem-top { padding-top: 3rem; }
    .xs-padding-3-half-rem-top { padding-top: 3.5rem; }
    .xs-padding-4-rem-top { padding-top: 4rem; }
    .xs-padding-4-half-rem-top { padding-top: 4.5rem; }
    .xs-padding-5-rem-top { padding-top: 5rem; }
    .xs-padding-5-half-rem-top { padding-top: 5.5rem; }
    .xs-padding-6-rem-top { padding-top: 6rem; }
    .xs-padding-6-half-rem-top { padding-top: 6.5rem; }
    .xs-padding-7-rem-top { padding-top: 7rem; }
    .xs-padding-7-half-rem-top { padding-top: 7.5rem; }
    .xs-padding-8-rem-top { padding-top: 8rem; }
    .xs-padding-8-half-rem-top { padding-top: 8.5rem; }
    .xs-padding-9-rem-top { padding-top: 9rem; }
    .xs-padding-9-half-rem-top { padding-top: 9.5rem; }
    .xs-padding-10-rem-top { padding-top: 10rem; }
    .xs-padding-10-half-rem-top { padding-top: 10.5rem; }

    /* padding bottom */
    .xs-padding-one-bottom { padding-bottom:1%; }
    .xs-padding-two-bottom { padding-bottom:2%; }
    .xs-padding-three-bottom { padding-bottom:3%; }
    .xs-padding-four-bottom { padding-bottom:4%; }
    .xs-padding-five-bottom { padding-bottom:5%; }
    .xs-padding-six-bottom { padding-bottom:6%; }
    .xs-padding-seven-bottom { padding-bottom:7%; }
    .xs-padding-eight-bottom { padding-bottom:8%; }
    .xs-padding-nine-bottom { padding-bottom:9%; }
    .xs-padding-ten-bottom { padding-bottom:10%; }
    .xs-padding-eleven-bottom { padding-bottom:11%; }
    .xs-padding-twelve-bottom { padding-bottom:12%; }
    .xs-padding-thirteen-bottom { padding-bottom:13%; }
    .xs-padding-fourteen-bottom { padding-bottom:14%; }
    .xs-padding-fifteen-bottom { padding-bottom:15%; }
    .xs-padding-sixteen-bottom { padding-bottom:16%; }
    .xs-padding-seventeen-bottom { padding-bottom:17%; }
    .xs-padding-eighteen-bottom { padding-bottom:18%; }
    .xs-padding-nineteen-bottom { padding-bottom:19%; }
    .xs-padding-twenty-bottom { padding-bottom:20%; }
    .xs-padding-5px-bottom { padding-bottom:5px; }
    .xs-padding-10px-bottom { padding-bottom:10px; }
    .xs-padding-15px-bottom { padding-bottom:15px; }
    .xs-padding-20px-bottom { padding-bottom:20px; }
    .xs-padding-25px-bottom { padding-bottom:25px; }
    .xs-padding-30px-bottom { padding-bottom:30px; }
    .xs-padding-35px-bottom { padding-bottom:35px; }
    .xs-padding-40px-bottom { padding-bottom:40px; }
    .xs-padding-45px-bottom { padding-bottom:45px; }
    .xs-padding-50px-bottom { padding-bottom:50px; }
    .xs-padding-55px-bottom { padding-bottom:55px; }
    .xs-padding-60px-bottom { padding-bottom:60px; }
    .xs-padding-65px-bottom { padding-bottom:65px; }
    .xs-padding-70px-bottom { padding-bottom:70px; }
    .xs-padding-75px-bottom { padding-bottom:75px; }
    .xs-padding-80px-bottom { padding-bottom:80px; }
    .xs-padding-85px-bottom { padding-bottom:85px; }
    .xs-padding-90px-bottom { padding-bottom:90px; }
    .xs-padding-95px-bottom { padding-bottom:95px; }
    .xs-padding-100px-bottom { padding-bottom:100px; }
    .xs-padding-1-rem-bottom { padding-bottom: 1rem; }
    .xs-padding-1-half-rem-bottom { padding-bottom: 1.5rem; }
    .xs-padding-2-rem-bottom { padding-bottom: 2rem; }
    .xs-padding-2-half-rem-bottom { padding-bottom: 2.5rem; }
    .xs-padding-3-rem-bottom { padding-bottom: 3rem; }
    .xs-padding-3-half-rem-bottom { padding-bottom: 3.5rem; }
    .xs-padding-4-rem-bottom { padding-bottom: 4rem; }
    .xs-padding-4-half-rem-bottom { padding-bottom: 4.5rem; }
    .xs-padding-5-rem-bottom { padding-bottom: 5rem; }
    .xs-padding-5-half-rem-bottom { padding-bottom: 5.5rem; }
    .xs-padding-6-rem-bottom { padding-bottom: 6rem; }
    .xs-padding-6-half-rem-bottom { padding-bottom: 6.5rem; }
    .xs-padding-7-rem-bottom { padding-bottom: 7rem; }
    .xs-padding-7-half-rem-bottom { padding-bottom: 7.5rem; }
    .xs-padding-8-rem-bottom { padding-bottom: 8rem; }
    .xs-padding-8-half-rem-bottom { padding-bottom: 8.5rem; }
    .xs-padding-9-rem-bottom { padding-bottom: 9rem; }
    .xs-padding-9-half-rem-bottom { padding-bottom: 9.5rem; }
    .xs-padding-10-rem-bottom { padding-bottom: 10rem; }
    .xs-padding-10-half-rem-bottom { padding-bottom: 10.5rem; }

    /* padding right */
    .xs-padding-one-right { padding-right:1%; }
    .xs-padding-two-right { padding-right:2%; }
    .xs-padding-three-right { padding-right:3%; }
    .xs-padding-four-right { padding-right:4% }
    .xs-padding-five-right { padding-right:5%; }
    .xs-padding-six-right { padding-right:6%; }
    .xs-padding-seven-right { padding-right:7%; }
    .xs-padding-eight-right { padding-right:8%; }
    .xs-padding-nine-right { padding-right:9%; }
    .xs-padding-ten-right { padding-right:10%; }
    .xs-padding-eleven-right { padding-right:11%; }
    .xs-padding-twelve-right { padding-right:12%; }
    .xs-padding-thirteen-right { padding-right:13%; }
    .xs-padding-fourteen-right { padding-right:14%; }
    .xs-padding-fifteen-right { padding-right:15%; }
    .xs-padding-sixteen-right { padding-right:16%; }
    .xs-padding-seventeen-right { padding-right:17%; }
    .xs-padding-eighteen-right { padding-right:18%; }
    .xs-padding-nineteen-right { padding-right:19%; }
    .xs-padding-twenty-right { padding-right:20%; }
    .xs-padding-5px-right { padding-right:5px; }
    .xs-padding-10px-right { padding-right:10px; }
    .xs-padding-15px-right { padding-right:15px; }
    .xs-padding-20px-right { padding-right:20px; }
    .xs-padding-25px-right { padding-right:25px; }
    .xs-padding-30px-right { padding-right:30px; }
    .xs-padding-35px-right { padding-right:35px; }
    .xs-padding-40px-right { padding-right:40px; }
    .xs-padding-45px-right { padding-right:45px; }
    .xs-padding-50px-right { padding-right:50px; }
    .xs-padding-55px-right { padding-right:55px; }
    .xs-padding-60px-right { padding-right:60px; }
    .xs-padding-65px-right { padding-right:65px; }
    .xs-padding-70px-right { padding-right:70px; }
    .xs-padding-75px-right { padding-right:75px; }
    .xs-padding-80px-right { padding-right:80px; }
    .xs-padding-85px-right { padding-right:85px; }
    .xs-padding-90px-right { padding-right:90px; }
    .xs-padding-95px-right { padding-right:95px; }
    .xs-padding-100px-right { padding-right:100px; }
    .xs-padding-1-rem-right { padding-right: 1rem; }
    .xs-padding-1-half-rem-right { padding-right: 1.5rem; }
    .xs-padding-2-rem-right { padding-right: 2rem; }
    .xs-padding-2-half-rem-right { padding-right: 2.5rem; }
    .xs-padding-3-rem-right { padding-right: 3rem; }
    .xs-padding-3-half-rem-right { padding-right: 3.5rem; }
    .xs-padding-4-rem-right { padding-right: 4rem; }
    .xs-padding-4-half-rem-right { padding-right: 4.5rem; }
    .xs-padding-5-rem-right { padding-right: 5rem; }
    .xs-padding-5-half-rem-right { padding-right: 5.5rem; }
    .xs-padding-6-rem-right { padding-right: 6rem; }
    .xs-padding-6-half-rem-right { padding-right: 6.5rem; }
    .xs-padding-7-rem-right { padding-right: 7rem; }
    .xs-padding-7-half-rem-right { padding-right: 7.5rem; }
    .xs-padding-8-rem-right { padding-right: 8rem; }
    .xs-padding-8-half-rem-right { padding-right: 8.5rem; }
    .xs-padding-9-rem-right { padding-right: 9rem; }
    .xs-padding-9-half-rem-right { padding-right: 9.5rem; }
    .xs-padding-10-rem-right { padding-right: 10rem; }
    .xs-padding-10-half-rem-right { padding-right: 10.5rem; }

    /* padding left */
    .xs-padding-one-left { padding-left:1%; }
    .xs-padding-two-left { padding-left:2%; }
    .xs-padding-three-left { padding-left:3%; }
    .xs-padding-four-left { padding-left:4%; }
    .xs-padding-five-left { padding-left:5%; }
    .xs-padding-six-left { padding-left:6%; }
    .xs-padding-seven-left { padding-left:7%; }
    .xs-padding-eight-left { padding-left:8%; }
    .xs-padding-nine-left { padding-left:9%; }
    .xs-padding-ten-left { padding-left:10%; }
    .xs-padding-eleven-left { padding-left:11%; }
    .xs-padding-twelve-left { padding-left:12%; }
    .xs-padding-thirteen-left { padding-left:13%; }
    .xs-padding-fourteen-left { padding-left:14%; }
    .xs-padding-fifteen-left { padding-left:15%; }
    .xs-padding-sixteen-left { padding-left:16%; }
    .xs-padding-seventeen-left { padding-left:17%; }
    .xs-padding-eighteen-left { padding-left:18%; }
    .xs-padding-nineteen-left { padding-left:19%; }
    .xs-padding-twenty-left { padding-left:20%; }
    .xs-padding-5px-left { padding-left:5px; }
    .xs-padding-10px-left { padding-left:10px; }
    .xs-padding-15px-left { padding-left:15px; }
    .xs-padding-20px-left { padding-left:20px; }
    .xs-padding-25px-left { padding-left:25px; }
    .xs-padding-30px-left { padding-left:30px; }
    .xs-padding-35px-left { padding-left:35px; }
    .xs-padding-40px-left { padding-left:40px; }
    .xs-padding-45px-left { padding-left:45px; }
    .xs-padding-50px-left { padding-left:50px; }
    .xs-padding-55px-left { padding-left:55px; }
    .xs-padding-60px-left { padding-left:60px; }
    .xs-padding-65px-left { padding-left:65px; }
    .xs-padding-70px-left { padding-left:70px; }
    .xs-padding-75px-left { padding-left:75px; }
    .xs-padding-80px-left { padding-left:80px; }
    .xs-padding-85px-left { padding-left:85px; }
    .xs-padding-90px-left { padding-left:90px; }
    .xs-padding-95px-left { padding-left:95px; }
    .xs-padding-100px-left { padding-left:100px; }
    .xs-padding-1-rem-left { padding-left: 1rem; }
    .xs-padding-1-half-rem-left { padding-left: 1.5rem; }
    .xs-padding-2-rem-left { padding-left: 2rem; }
    .xs-padding-2-half-rem-left { padding-left: 2.5rem; }
    .xs-padding-3-rem-left { padding-left: 3rem; }
    .xs-padding-3-half-rem-left { padding-left: 3.5rem; }
    .xs-padding-4-rem-left { padding-left: 4rem; }
    .xs-padding-4-half-rem-left { padding-left: 4.5rem; }
    .xs-padding-5-rem-left { padding-left: 5rem; }
    .xs-padding-5-half-rem-left { padding-left: 5.5rem; }
    .xs-padding-6-rem-left { padding-left: 6rem; }
    .xs-padding-6-half-rem-left { padding-left: 6.5rem; }
    .xs-padding-7-rem-left { padding-left: 7rem; }
    .xs-padding-7-half-rem-left { padding-left: 7.5rem; }
    .xs-padding-8-rem-left { padding-left: 8rem; }
    .xs-padding-8-half-rem-left { padding-left: 8.5rem; }
    .xs-padding-9-rem-left { padding-left: 9rem; }
    .xs-padding-9-half-rem-left { padding-left: 9.5rem; }
    .xs-padding-10-rem-left { padding-left: 10rem; }
    .xs-padding-10-half-rem-left { padding-left: 10.5rem; }

    /* padding top bottom */
    .xs-padding-one-tb { padding-top:1%; padding-bottom:1%; }
    .xs-padding-two-tb { padding-top:2%; padding-bottom:2%; }
    .xs-padding-three-tb { padding-top:3%; padding-bottom:3%; }
    .xs-padding-four-tb { padding-top:4%; padding-bottom:4%; }
    .xs-padding-five-tb { padding-top:5%; padding-bottom:5%; }
    .xs-padding-six-tb { padding-top:6%; padding-bottom:6%; }
    .xs-padding-seven-tb { padding-top:7%; padding-bottom:7%; }
    .xs-padding-eight-tb { padding-top:8%; padding-bottom:8%; }
    .xs-padding-nine-tb { padding-top:9%; padding-bottom:9%; }
    .xs-padding-ten-tb { padding-top:10%; padding-bottom:10%; }
    .xs-padding-eleven-tb { padding-top:11%; padding-bottom:11%; }
    .xs-padding-twelve-tb { padding-top:12%; padding-bottom:12%; }
    .xs-padding-thirteen-tb { padding-top:13%; padding-bottom:13%; }
    .xs-padding-fourteen-tb { padding-top:14%; padding-bottom:14%; }
    .xs-padding-fifteen-tb { padding-top:15%; padding-bottom:15%; }
    .xs-padding-sixteen-tb { padding-top:16%; padding-bottom:16%; }
    .xs-padding-seventeen-tb { padding-top:17%; padding-bottom:17%; }
    .xs-padding-eighteen-tb { padding-top:18%; padding-bottom:18%; }
    .xs-padding-nineteen-tb { padding-top:19%; padding-bottom:19%; }
    .xs-padding-twenty-tb { padding-top:20%; padding-bottom:20%; }
    .xs-padding-5px-tb { padding-top:5px; padding-bottom:5px; }
    .xs-padding-10px-tb { padding-top:10px; padding-bottom:10px; }
    .xs-padding-15px-tb { padding-top:15px; padding-bottom:15px; }
    .xs-padding-20px-tb { padding-top:20px; padding-bottom:20px; }
    .xs-padding-25px-tb { padding-top:25px; padding-bottom:25px; }
    .xs-padding-30px-tb { padding-top:30px; padding-bottom:30px; }
    .xs-padding-35px-tb { padding-top:35px; padding-bottom:35px; }
    .xs-padding-40px-tb { padding-top:40px; padding-bottom:40px; }
    .xs-padding-45px-tb { padding-top:45px; padding-bottom:45px; }
    .xs-padding-50px-tb { padding-top:50px; padding-bottom:50px; }
    .xs-padding-55px-tb { padding-top:55px; padding-bottom:55px; }
    .xs-padding-60px-tb { padding-top:60px; padding-bottom:60px; }
    .xs-padding-65px-tb { padding-top:65px; padding-bottom:65px; }
    .xs-padding-70px-tb { padding-top:70px; padding-bottom:70px; }
    .xs-padding-75px-tb { padding-top:75px; padding-bottom:75px; }
    .xs-padding-80px-tb { padding-top:80px; padding-bottom:80px; }
    .xs-padding-85px-tb { padding-top:85px; padding-bottom:85px; }
    .xs-padding-90px-tb { padding-top:90px; padding-bottom:90px; }
    .xs-padding-95px-tb { padding-top:95px; padding-bottom:95px; }
    .xs-padding-100px-tb { padding-top:100px; padding-bottom:100px; }
    .xs-padding-1-rem-tb { padding-top: 1rem; padding-bottom: 1rem; }
    .xs-padding-1-half-rem-tb { padding-top: 1.5rem; padding-bottom: 1.5rem; }
    .xs-padding-2-rem-tb { padding-top: 2rem; padding-bottom: 2rem; }
    .xs-padding-2-half-rem-tb { padding-top: 2.5rem; padding-bottom: 2.5rem; }
    .xs-padding-3-rem-tb { padding-top: 3rem; padding-bottom: 3rem; }
    .xs-padding-3-half-rem-tb { padding-top: 3.5rem; padding-bottom: 3.5rem; }
    .xs-padding-4-rem-tb { padding-top: 4rem; padding-bottom: 4rem; }
    .xs-padding-4-half-rem-tb { padding-top: 4.5rem; padding-bottom: 4.5rem; }
    .xs-padding-5-rem-tb { padding-top: 5rem; padding-bottom: 5rem; }
    .xs-padding-5-half-rem-tb { padding-top: 5.5rem; padding-bottom: 5.5rem; }
    .xs-padding-6-rem-tb { padding-top: 6rem; padding-bottom: 6rem; }
    .xs-padding-6-half-rem-tb { padding-top: 6.5rem; padding-bottom: 6.5rem; }
    .xs-padding-7-rem-tb { padding-top: 7rem; padding-bottom: 7rem; }
    .xs-padding-7-half-rem-tb { padding-top: 7.5rem; padding-bottom: 7.5rem; }
    .xs-padding-8-rem-tb { padding-top: 8rem; padding-bottom: 8rem; }
    .xs-padding-8-half-rem-tb { padding-top: 8.5rem; padding-bottom: 8.5rem; }
    .xs-padding-9-rem-tb { padding-top: 9rem; padding-bottom: 9rem; }
    .xs-padding-9-half-rem-tb { padding-top: 9.5rem; padding-bottom: 9.5rem; }
    .xs-padding-10-rem-tb { padding-top: 10rem; padding-bottom: 10rem; }
    .xs-padding-10-half-rem-tb { padding-top: 10.5rem; padding-bottom: 10.5rem; }

    /* padding left right */
    .xs-padding-one-lr { padding-left:1%; padding-right:1%; }
    .xs-padding-two-lr { padding-left:2%; padding-right:2%; }
    .xs-padding-three-lr { padding-left:3%; padding-right:3%; }
    .xs-padding-four-lr { padding-left:4%; padding-right:4%; }
    .xs-padding-five-lr { padding-left:5%; padding-right:5%; }
    .xs-padding-six-lr { padding-left:6%; padding-right:6%; }
    .xs-padding-seven-lr { padding-left:7%; padding-right:7%; }
    .xs-padding-eight-lr { padding-left:8%; padding-right:8%; }
    .xs-padding-nine-lr { padding-left:9%; padding-right:9%; }
    .xs-padding-ten-lr { padding-left:10%; padding-right:10%; }
    .xs-padding-eleven-lr { padding-left:11%; padding-right:11%; }
    .xs-padding-twelve-lr { padding-left:12%; padding-right:12%; }
    .xs-padding-thirteen-lr { padding-left:13%; padding-right:13%; }
    .xs-padding-fourteen-lr { padding-left:14%; padding-right:14%; }
    .xs-padding-fifteen-lr { padding-left:15%; padding-right:15%; }
    .xs-padding-sixteen-lr { padding-left:16%; padding-right:16%; }
    .xs-padding-seventeen-lr { padding-left:17%; padding-right:17%; }
    .xs-padding-eighteen-lr { padding-left:18%; padding-right:18%; }
    .xs-padding-nineteen-lr { padding-left:19%; padding-right:19%; }
    .xs-padding-twenty-lr { padding-left:20%; padding-right:20%; }
    .xs-padding-5px-lr { padding-left:5px; padding-right:5px; }
    .xs-padding-10px-lr { padding-left:10px; padding-right:10px; }
    .xs-padding-15px-lr { padding-left:15px; padding-right:15px; }
    .xs-padding-20px-lr { padding-left:20px; padding-right:20px; }
    .xs-padding-25px-lr { padding-left:25px; padding-right:25px; }
    .xs-padding-30px-lr { padding-left:30px; padding-right:30px; }
    .xs-padding-35px-lr { padding-left:35px; padding-right:35px; }
    .xs-padding-40px-lr { padding-left:40px; padding-right:40px; }
    .xs-padding-45px-lr { padding-left:45px; padding-right:45px; }
    .xs-padding-50px-lr { padding-left:50px; padding-right:50px; }
    .xs-padding-55px-lr { padding-left:55px; padding-right:55px; }
    .xs-padding-60px-lr { padding-left:60px; padding-right:60px; }
    .xs-padding-65px-lr { padding-left:65px; padding-right:65px; }
    .xs-padding-70px-lr { padding-left:70px; padding-right:70px; }
    .xs-padding-75px-lr { padding-left:75px; padding-right:75px; }
    .xs-padding-80px-lr { padding-left:80px; padding-right:80px; }
    .xs-padding-85px-lr { padding-left:85px; padding-right:85px; }
    .xs-padding-90px-lr { padding-left:90px; padding-right:90px; }
    .xs-padding-95px-lr { padding-left:95px; padding-right:95px; }
    .xs-padding-100px-lr { padding-left:100px; padding-right:100px; }
    .xs-padding-1-rem-lr { padding-left: 1rem; padding-right: 1rem; }
    .xs-padding-1-half-rem-lr { padding-left: 1.5rem; padding-right: 1.5rem; }
    .xs-padding-2-rem-lr { padding-left: 2rem; padding-right: 2rem; }
    .xs-padding-2-half-rem-lr { padding-left: 2.5rem; padding-right: 2.5rem; }
    .xs-padding-3-rem-lr { padding-left: 3rem; padding-right: 3rem; }
    .xs-padding-3-half-rem-lr { padding-left: 3.5rem; padding-right: 3.5rem; }
    .xs-padding-4-rem-lr { padding-left: 4rem; padding-right: 4rem; }
    .xs-padding-4-half-rem-lr { padding-left: 4.5rem; padding-right: 4.5rem; }
    .xs-padding-5-rem-lr { padding-left: 5rem; padding-right: 5rem; }
    .xs-padding-5-half-rem-lr { padding-left: 5.5rem; padding-right: 5.5rem; }
    .xs-padding-6-rem-lr { padding-left: 6rem; padding-right: 6rem; }
    .xs-padding-6-half-rem-lr { padding-left: 6.5rem; padding-right: 6.5rem; }
    .xs-padding-7-rem-lr { padding-left: 7rem; padding-right: 7rem; }
    .xs-padding-7-half-rem-lr { padding-left: 7.5rem; padding-right: 7.5rem; }
    .xs-padding-8-rem-lr { padding-left: 8rem; padding-right: 8rem; }
    .xs-padding-8-half-rem-lr { padding-left: 8.5rem; padding-right: 8.5rem; }
    .xs-padding-9-rem-lr { padding-left: 9rem; padding-right: 9rem; }
    .xs-padding-9-half-rem-lr { padding-left: 9.5rem; padding-right: 9.5rem; }
    .xs-padding-10-rem-lr { padding-left: 10rem; padding-right: 10rem; }
    .xs-padding-10-half-rem-lr { padding-left: 10.5rem; padding-right: 10.5rem; }

    .xs-no-padding { padding:0 !important; }
    .xs-no-padding-lr { padding-left: 0 !important; padding-right: 0 !important; }
    .xs-no-padding-tb { padding-top: 0 !important; padding-bottom: 0 !important; }
    .xs-no-padding-top { padding-top:0 !important; }
    .xs-no-padding-bottom { padding-bottom:0 !important; }
    .xs-no-padding-left { padding-left:0 !important; }
    .xs-no-padding-right { padding-right:0 !important; }

    /* display and overflow */
    .xs-d-initial { display: initial !important; }
    .xs-overflow-hidden { overflow:hidden !important; }
    .xs-overflow-visible { overflow:visible !important; }
    .xs-overflow-auto { overflow:auto !important; }

    /* position */
    .xs-position-relative { position: relative !important; }
    .xs-position-absolute { position: absolute !important; }
    .xs-position-fixed { position: fixed !important; }
    .xs-position-inherit { position: inherit !important; }
    .xs-position-initial { position: initial !important; }

    /* top */
    .xs-top-0px { top: 0; }
    .xs-top-1px { top: 1px; }
    .xs-top-2px { top: 2px; }
    .xs-top-3px { top: 3px; }
    .xs-top-4px { top: 4px; }
    .xs-top-5px { top: 5px; }
    .xs-top-6px { top: 6px; }
    .xs-top-7px { top: 7px; }
    .xs-top-8px { top: 8px; }
    .xs-top-9px { top: 9px; }
    .xs-top-10px { top: 10px; }
    .xs-top-15px { top: 15px; }
    .xs-top-20px { top: 20px; }
    .xs-top-25px { top: 25px; }
    .xs-top-30px { top: 30px; }
    .xs-top-35px { top: 35px; }
    .xs-top-40px { top: 40px; }
    .xs-top-45px { top: 45px; }
    .xs-top-50px { top: 50px; }
    .xs-top-auto { top:auto; }
    .xs-top-inherit { top:inherit; }

    /* top minus */
    .xs-top-minus-1px { top: -1px; }
    .xs-top-minus-2px { top: -2px; }
    .xs-top-minus-3px { top: -3px; }
    .xs-top-minus-4px { top: -4px; }
    .xs-top-minus-5px { top: -5px; }
    .xs-top-minus-6px { top: -6px; }
    .xs-top-minus-7px { top: -7px; }
    .xs-top-minus-8px { top: -8px; }
    .xs-top-minus-9px { top: -9px; }
    .xs-top-minus-10px { top: -10px; }
    .xs-top-minus-15px { top: -15px; }
    .xs-top-minus-20px { top: -20px; }
    .xs-top-minus-25px { top: -25px; }
    .xs-top-minus-30px { top: -30px; }
    .xs-top-minus-35px { top: -35px; }
    .xs-top-minus-40px { top: -40px; }
    .xs-top-minus-45px { top: -45px; }
    .xs-top-minus-50px { top: -50px; }

    /* bottom */
    .xs-bottom-0px { bottom:0; }
    .xs-bottom-1px { bottom:1px; }
    .xs-bottom-2px { bottom:2px; }
    .xs-bottom-3px { bottom:3px; }
    .xs-bottom-4px { bottom:4px; }
    .xs-bottom-5px { bottom:5px; }
    .xs-bottom-6px { bottom:6px; }
    .xs-bottom-7px { bottom:7px; }
    .xs-bottom-8px { bottom:8px; }
    .xs-bottom-9px { bottom:9px; }
    .xs-bottom-10px { bottom:10px; }
    .xs-bottom-15px { bottom:15px; }
    .xs-bottom-20px { bottom:20px; }
    .xs-bottom-25px { bottom:25px; }
    .xs-bottom-30px { bottom:30px; }
    .xs-bottom-35px { bottom:35px; }
    .xs-bottom-40px { bottom:40px; }
    .xs-bottom-45px { bottom:45px; }
    .xs-bottom-50px { bottom:50px; }
    .xs-bottom-55px { bottom:55px; }
    .xs-bottom-60px { bottom:60px; }
    .xs-bottom-auto { bottom: auto; }
    .xs-bottom-inherit { bottom: inherit; }

    /* bottom minus */
    .xs-bottom-minus-1px { bottom: -1px; }
    .xs-bottom-minus-2px { bottom: -2px; }
    .xs-bottom-minus-3px { bottom: -3px; }
    .xs-bottom-minus-4px { bottom: -4px; }
    .xs-bottom-minus-5px { bottom: -5px; }
    .xs-bottom-minus-6px { bottom: -6px; }
    .xs-bottom-minus-7px { bottom: -7px; }
    .xs-bottom-minus-8px { bottom: -8px; }
    .xs-bottom-minus-9px { bottom: -9px; }
    .xs-bottom-minus-10px { bottom: -10px; }
    .xs-bottom-minus-15px { bottom: -15px; }
    .xs-bottom-minus-20px { bottom: -20px; }
    .xs-bottom-minus-25px { bottom: -25px; }
    .xs-bottom-minus-30px { bottom: -30px; }
    .xs-bottom-minus-35px { bottom: -35px; }
    .xs-bottom-minus-40px { bottom: -40px; }
    .xs-bottom-minus-45px { bottom: -45px; }
    .xs-bottom-minus-50px { bottom: -50px; }

    /* right */
    .xs-right-0px { right: 0; }
    .xs-right-1px { right: 1px; }
    .xs-right-2px { right: 2px; }
    .xs-right-3px { right: 3px; }
    .xs-right-4px { right: 4px; }
    .xs-right-5px { right: 5px; }
    .xs-right-6px { right: 6px; }
    .xs-right-7px { right: 7px; }
    .xs-right-8px { right: 8px; }
    .xs-right-9px { right: 9px; }
    .xs-right-10px { right: 10px; }
    .xs-right-15px { right: 15px; }
    .xs-right-20px { right: 20px; }
    .xs-right-25px { right: 25px; }
    .xs-right-30px { right: 30px; }
    .xs-right-35px { right: 35px; }
    .xs-right-40px { right: 40px; }
    .xs-right-45px { right: 45px; }
    .xs-right-50px { right: 50px; }
    .xs-right-auto { right: auto; }
    .xs-right-inherit { right: inherit; }

    /* right minus */
    .xs-right-minus-1px { right: -1px; }
    .xs-right-minus-2px { right: -2px; }
    .xs-right-minus-3px { right: -3px; }
    .xs-right-minus-4px { right: -4px; }
    .xs-right-minus-5px { right: -5px; }
    .xs-right-minus-6px { right: -6px; }
    .xs-right-minus-7px { right: -7px; }
    .xs-right-minus-8px { right: -8px; }
    .xs-right-minus-9px { right: -9px; }
    .xs-right-minus-10px { right: -10px; }
    .xs-right-minus-15px { right: -15px; }
    .xs-right-minus-20px { right: -20px; }
    .xs-right-minus-25px { right: -25px; }
    .xs-right-minus-30px { right: -30px; }
    .xs-right-minus-35px { right: -35px; }
    .xs-right-minus-40px { right: -40px; }
    .xs-right-minus-45px { right: -45px; }
    .xs-right-minus-50px { right: -50px; }

    /* left */
    .xs-left-0px { left: 0; }
    .xs-left-1px { left: 1px; }
    .xs-left-2px { left: 2px; }
    .xs-left-3px { left: 3px; }
    .xs-left-4px { left: 4px; }
    .xs-left-5px { left: 5px; }
    .xs-left-6px { left: 6px; }
    .xs-left-7px { left: 7px; }
    .xs-left-8px { left: 8px; }
    .xs-left-9px { left: 9px; }
    .xs-left-10px { left: 10px; }
    .xs-left-15px { left: 15px; }
    .xs-left-20px { left: 20px; }
    .xs-left-25px { left: 25px; }
    .xs-left-30px { left: 30px; }
    .xs-left-35px { left: 35px; }
    .xs-left-40px { left: 40px; }
    .xs-left-45px { left: 45px; }
    .xs-left-50px { left: 50px; }
    .xs-left-55px { left: 55px; }
    .xs-left-60px { left: 60px; }
    .xs-left-auto { left: auto; }
    .xs-left-inherit { left: inherit; }

    /* left minus */
    .xs-left-minus-1px { left: -1px; }
    .xs-left-minus-2px { left: -2px; }
    .xs-left-minus-3px { left: -3px; }
    .xs-left-minus-4px { left: -4px; }
    .xs-left-minus-5px { left: -5px; }
    .xs-left-minus-6px { left: -6px; }
    .xs-left-minus-7px { left: -7px; }
    .xs-left-minus-8px { left: -8px; }
    .xs-left-minus-9px { left: -9px; }
    .xs-left-minus-10px { left: -10px; }
    .xs-left-minus-15px { left: -15px; }
    .xs-left-minus-20px { left: -20px; }
    .xs-left-minus-25px { left: -25px; }
    .xs-left-minus-30px { left: -30px; }
    .xs-left-minus-35px { left: -35px; }
    .xs-left-minus-40px { left: -40px; }
    .xs-left-minus-45px { left: -45px; }
    .xs-left-minus-50px { left: -50px; }

    /* width */
    .xs-w-1px { width:1px !important; }
    .xs-w-2px { width:2px !important; }
    .xs-w-3px { width:3px !important; }
    .xs-w-4px { width:4px !important; }
    .xs-w-5px { width:5px !important; }
    .xs-w-6px { width:6px !important; }
    .xs-w-7px { width:7px !important; }
    .xs-w-8px { width:8px !important; }
    .xs-w-9px { width:9px !important; }
    .xs-w-10px { width:10px !important; }
    .xs-w-15px { width:15px !important; }
    .xs-w-20px { width:20px !important; }
    .xs-w-25px { width:25px !important; }
    .xs-w-30px { width:30px !important; }
    .xs-w-35px { width:35px !important; }
    .xs-w-40px { width:40px !important; }
    .xs-w-50px { width:50px !important; }
    .xs-w-55px { width:55px !important; }
    .xs-w-60px { width:60px !important; }
    .xs-w-65px { width:65px !important; }
    .xs-w-70px { width:70px !important; }
    .xs-w-75px { width:75px !important; }
    .xs-w-80px { width:80px !important; }
    .xs-w-85px { width:85px !important; }
    .xs-w-90px { width:90px !important; }
    .xs-w-95px { width:95px !important; }
    .xs-w-100px { width:100px !important; }
    .xs-w-110px { width:110px !important; }
    .xs-w-120px { width:120px !important; }
    .xs-w-130px { width:130px !important; }
    .xs-w-140px { width:140px !important; }
    .xs-w-150px { width:150px !important; }
    .xs-w-160px { width:160px !important; }
    .xs-w-170px { width:170px !important; }
    .xs-w-180px { width:180px !important; }
    .xs-w-190px { width:190px !important; }
    .xs-w-200px { width:200px !important; }
    .xs-w-250px { width:250px !important; }
    .xs-w-300px { width:300px !important; }
    .xs-w-350px { width:350px !important; }
    .xs-w-400px { width:400px !important; }
    .xs-w-450px { width:450px !important; }
    .xs-w-500px { width:500px !important; }
    .xs-w-550px { width:550px !important; }
    .xs-w-600px { width:600px !important; }
    .xs-w-650px { width:650px !important; }
    .xs-w-700px { width:700px !important; }
    .xs-w-750px { width:750px !important; }
    .xs-w-800px { width:800px !important; }
    .xs-w-850px { width:850px !important; }
    .xs-w-900px { width:900px !important; }
    .xs-w-950px { width:950px !important; }
    .xs-w-1000px { width:1000px !important; }
    .xs-w-10 { width: 10% !important; }
    .xs-w-15 { width: 15% !important; }
    .xs-w-20 { width: 20% !important; }
    .xs-w-25 { width: 25% !important; }
    .xs-w-30 { width: 30% !important; }
    .xs-w-35 { width: 35% !important; }
    .xs-w-40 { width: 40% !important; }
    .xs-w-45 { width: 45% !important; }
    .xs-w-50 { width: 50% !important; }
    .xs-w-55 { width: 55% !important; }
    .xs-w-60 { width: 60% !important; }
    .xs-w-65 { width: 65% !important; }
    .xs-w-70 { width: 70% !important; }
    .xs-w-75 { width: 75% !important; }
    .xs-w-80 { width: 80% !important; }
    .xs-w-85 { width: 85% !important; }
    .xs-w-90 { width: 90% !important; }
    .xs-w-95 { width: 95% !important; }
    .xs-w-100 { width: 100% !important; }
    .xs-w-auto { width:auto !important; }

    /* height */
    .xs-h-1px { height: 1px !important; }
    .xs-h-2px { height: 2px !important; }
    .xs-h-3px { height: 3px !important; }
    .xs-h-4px { height: 4px !important; }
    .xs-h-5px { height: 5px !important; }
    .xs-h-6px { height: 6px !important; }
    .xs-h-7px { height: 7px !important; }
    .xs-h-8px { height: 8px !important; }
    .xs-h-9px { height: 9px !important; }
    .xs-h-10px { height: 10px !important; }
    .xs-h-20px { height: 20px !important; }
    .xs-h-30px { height: 30px !important; }
    .xs-h-40px { height: 40px !important; }
    .xs-h-42px { height: 42px !important; }
    .xs-h-50px { height: 50px !important; }
    .xs-h-60px { height: 60px !important; }
    .xs-h-70px { height: 70px !important; }
    .xs-h-80px { height: 80px !important; }
    .xs-h-90px { height: 90px !important; }
    .xs-h-100px { height: 100px !important; }
    .xs-h-110px { height: 110px !important; }
    .xs-h-120px { height: 120px !important; }
    .xs-h-130px { height: 130px !important; }
    .xs-h-140px { height: 140px !important; }
    .xs-h-150px { height: 150px !important; }
    .xs-h-160px { height: 160px !important; }
    .xs-h-170px { height: 170px !important; }
    .xs-h-180px { height: 180px !important; }
    .xs-h-190px { height: 190px !important; }
    .xs-h-200px { height: 200px !important; }
    .xs-h-250px { height: 250px !important; }
    .xs-h-300px { height: 300px !important; }
    .xs-h-350px { height: 350px !important; }
    .xs-h-400px { height: 400px !important; }
    .xs-h-450px { height: 450px !important; }
    .xs-h-500px { height: 500px !important; }
    .xs-h-520px { height: 520px !important; }
    .xs-h-550px { height: 550px !important; }
    .xs-h-580px { height: 580px !important; }
    .xs-h-600px { height: 600px !important; }
    .xs-h-650px { height: 650px !important; }
    .xs-h-700px { height: 700px !important; }
    .xs-h-720px { height: 720px !important; }
    .xs-h-750px { height: 750px !important; }
    .xs-h-800px { height: 800px !important; }
    .xs-h-820px { height: 820px !important; }
    .xs-h-830px { height: 830px !important; }
    .xs-h-850px { height: 850px !important; }

    .xs-h-50 { height: 50% !important; }
    .xs-h-100 { height: 100% !important; }
    .xs-h-auto { height:auto !important; }

    /* min-height */
    .xs-min-h-100px { min-height: 100px; }
    .xs-min-h-200px { min-height: 200px; }
    .xs-min-h-300px { min-height: 300px; }
    .xs-min-h-400px { min-height: 400px; }
    .xs-min-h-500px { min-height: 500px; }
    .xs-min-h-600px { min-height: 600px; }
    .xs-min-h-700px { min-height: 700px; }

    /* screen height */
    .small-screen { height: 280px; }

    /* interactive banner style 05 */
    .interactive-banners-style-05 .interactive-banners-content, .interactive-banners-style-05 .interactive-banners-overlayer {  transform: translateY(calc(100% - 105px)); -webkit-transform: translateY(calc(100% - 105px)); -moz-transform: translateY(calc(100% - 105px)); -ms-transform: translateY(calc(100% - 105px));  }

    /* interactive banner style 09 */
    .interactive-banners-style-09 .interactive-banners-content .interactive-banners-hover-icon { left: 5rem; bottom: 5rem; }

    /* accordion style 04 */
    .accordion-style-04 .panel .panel-time { min-width: 100%; padding-right: 15px;}
    .accordion-style-04 .panel .accordion-toggle { width: 100%; }
    .accordion-style-04 .panel .panel-body { margin-left: 0; width: 100%; }

    /* accordion style 05 */
    .accordion-style-05 .panel .panel-heading { padding: 15px 60px 15px 20px; }
    .accordion-style-05 .panel .panel-heading.active-accordion { padding-top: 20px; }
    .accordion-style-05 .panel .panel-body { padding: 0 40px 20px 20px; }
    .accordion-style-05 .panel .collapse.show .panel-body, .accordion-style-05 .panel .collapsing .panel-body { padding-bottom: 20px; }
    .accordion-style-05 .panel .panel-heading.active-accordion i { top: 50%; }

    /* table style 01 */
    .table-style-01 + .mfp-close { width: 34px; height: 34px; line-height: 34px; }

    /* tab style 04 */
    .tab-style-04 .nav-tabs > li.nav-item { width: 100%; }

    /* process step style 02 */
    .process-step-style-02 .process-step-icon-wrap { margin-right: 25px; }

    /* process step style 03 */
    .process-step-style-03 .process-step-item-box .process-step-item-box-bfr { width: calc(100% - 50px); left: 50%; transform: translateX(-50%); -webkit-transform: translateX(-50%); -moz-transform: translateX(-50%); -ms-transform: translateX(-50%); }

    /* countdown style 02 */
    .countdown.countdown-style-02 .countdown-box { margin-bottom: 15px; padding: 0 15px; width: 50%; }
    .countdown.countdown-style-02 .countdown-box:nth-child(2):after { display: none; }
    .countdown.countdown-style-02 .countdown-box .number { margin: 0 auto;}

    /* countdown style 03 */
    .countdown.countdown-style-03 .countdown-box { margin-bottom: 15px; padding: 0 15px; width: 50%; }
    .countdown.countdown-style-03 .countdown-box:nth-child(2):after { display: none; }
    .countdown.countdown-style-03 .countdown-box .number { margin: 0 auto;}

    /* newsletter style 04 */
    .newsletter-style-04 input { padding-right: 25px; }
    .newsletter-style-04 .btn { position: static; top: inherit; right: inherit; transform: translateY(0px); border-radius: 6px; margin-top: 15px; padding: 12px 35px; height: auto; width: 100%;}

    /* button */
    .btn-dual .btn { margin-left: 4px; margin-right: 4px; }

    /* no border */
    .xs-no-border-top { border-top:0 !important }
    .xs-no-border-bottom { border-bottom:0 !important }
    .xs-no-border-right { border-right:0 !important }
    .xs-no-border-left { border-left:0 !important }
    .xs-no-border-all { border: 0 !important }
    .xs-no-border-radius { border-radius: 0 !important }

    /* border width */
    .xs-border-width-1px { border-width:1px !important; }
    .xs-border-width-2px { border-width:2px !important; }
    .xs-border-width-3px { border-width:3px !important; }
    .xs-border-width-4px { border-width:4px !important; }
    .xs-border-width-5px { border-width:5px !important; }
    .xs-border-width-6px { border-width:6px !important; }
    .xs-border-width-7px { border-width:7px !important; }
    .xs-border-width-8px { border-width:8px !important; }
    .xs-border-width-9px { border-width:9px !important; }
    .xs-border-width-10px { border-width:10px !important; }
    .xs-border-width-11px { border-width:11px !important; }
    .xs-border-width-12px { border-width:12px !important; }
    .xs-border-width-13px { border-width:13px !important; }
    .xs-border-width-14px { border-width:14px !important; }
    .xs-border-width-15px { border-width:15px !important; }
    .xs-border-width-16px { border-width:16px !important; }
    .xs-border-width-17px { border-width:17px !important; }
    .xs-border-width-18px { border-width:18px !important; }
    .xs-border-width-19px { border-width:19px !important; }
    .xs-border-width-20px { border-width:20px !important; }

    /* border */
    .xs-border-all { border: 1px solid; }
    .xs-border-top { border-top: 1px solid; }
    .xs-border-bottom { border-bottom: 1px solid; }
    .xs-border-left { border-left: 1px solid; }
    .xs-border-right { border-right: 1px solid; }
    .xs-border-lr { border-left: 1px solid; border-right: 1px solid; }
    .xs-border-tb { border-top: 1px solid; border-bottom: 1px solid; }

    /* border color */
    .xs-border-color-white { border-color: #fff; }
    .xs-border-color-black { border-color: #000; }
    .xs-border-color-sky-blue { border-color: #2e94eb; }
    .xs-border-color-extra-dark-gray { border-color: #232323; }
    .xs-border-color-medium-dark-gray { border-color: #363636; }
    .xs-border-color-dark-gray { border-color: #939393; }
    .xs-border-color-extra-medium-gray { border-color: #dbdbdb; }
    .xs-border-color-medium-gray { border-color: #e4e4e4; }
    .xs-border-color-extra-light-gray { border-color: #ededed; }
    .xs-border-color-light-gray { border-color: #f5f5f5; }
    .xs-border-color-light-pink { border-color: #862237; }
    .xs-border-color-deep-pink { border-color: #ff214f; }
    .xs-border-color-pink { border-color: #ff357c; }
    .xs-border-color-fast-blue { border-color: #0038e3; }
    .xs-border-color-orange { border-color: #ff6437; }
    .xs-border-color-green { border-color: #45d690; }
    .xs-border-color-golden { border-color: #d0ba6d; }
    .xs-border-color-persian-blue { border-color: #0039CC; }
    .xs-border-color-purple { border-color: #7342ac; }
    .xs-border-color-parrot-green { border-color: #cee002; }
    .xs-border-color-dark-red { border-color: #e12837; }

    /* transparent border */
    .xs-border-color-transparent { border-color: transparent; }
    .xs-border-color-black-transparent { border-color: rgba(0,0,0,.1); }
    .xs-border-color-white-transparent { border-color: rgba(255,255,255,.1); }
    .xs-border-color-golden-transparent { border-color: rgba(208, 186, 109, 0.2); }
    .xs-border-color-pink-transparent { border-color: rgba(255, 33, 79, 0.45); }
    .xs-border-color-dark-white-transparent { border-color: rgba(255,255,255,0.2); }
    .xs-border-color-medium-white-transparent { border-color: rgba(255,255,255,0.4); }
    .xs-border-color-full-dark-white-transparent { border-color: rgba(255,255,255,0.05); }
    .xs-border-color-light-white-transparent { border-color: rgba(255,255,255,0.1); }
    .xs-border-color-nero-transparent { border-color: rgba(25,25,25,0.1); }
    .xs-border-color-extra-medium-gray-transparent { border-color: rgba(219,219,219,.04); }

    /* border style */
    .xs-border-dotted { border-style: dotted !important; }
    .xs-border-dashed { border-style: dashed !important; }
    .xs-border-solid { border-style: solid !important; }
    .xs-border-double { border-style: double !important; }
    .xs-border-groove { border-style: groove !important; }
    .xs-border-ridge { border-style: ridge !important; }
    .xs-border-inset { border-style: inset !important; }
    .xs-border-outset { border-style: outset !important; }
    .xs-border-none { border-style: none !important; }
    .xs-border-hidden { border-style: hidden !important; }
    .xs-border-transperent { border-color: transparent !important; }

    /* header search form */
    .form-wrapper .search-form-box { width: 90%; }
    .search-form .search-input { font-size: 18px; padding: 8px 38px 8px 2px; line-height: 30px; }
    .form-wrapper .search-form .search-button { bottom: 26px; }
    .form-wrapper .search-close { top: 5px; right: 5px; }
    .header-search-icon, .header-cart-icon, .header-language, .header-push-button, .header-button { padding-left: 15px; }

    /* menu modern */
    .navbar-collapse-show[data-mobile-nav-style=modern] .navbar, .navbar-collapse-show[data-mobile-nav-style=modern] .page-layout, .navbar-collapse-show[data-mobile-nav-style=modern] .top-bar { -webkit-transform: translate3d(-85vw, 0, 0); transform: translate3d(-85vw, 0, 0); }
    [data-mobile-nav-style=modern] .navbar-modern-inner { width: 85vw; }
    [data-mobile-nav-style=modern] .navbar-modern-inner .navbar-collapse { padding-right: 10vw; padding-left: 10vw; }
    [data-mobile-nav-style=modern][data-mobile-nav-trigger-alignment=left] .navbar-modern-inner { width: 85vw; }
    [data-mobile-nav-style=modern][data-mobile-nav-trigger-alignment=left] .navbar-modern-inner .navbar-collapse { padding-right: 10vw; padding-left: 10vw; }
    .navbar-collapse-show[data-mobile-nav-style=modern][data-mobile-nav-trigger-alignment=left] .navbar, .navbar-collapse-show[data-mobile-nav-style=modern][data-mobile-nav-trigger-alignment=left] .page-layout, .navbar-collapse-show[data-mobile-nav-style=modern][data-mobile-nav-trigger-alignment=left] .top-bar { -webkit-transform: translate3d(85vw, 0, 0); transform: translate3d(85vw, 0, 0); }

    /* full-screen-menu */
    [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .navbar-collapse { padding: 60px 0 }
    [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .navbar-toggler { top: 20px; right: 20px; }

    /* push menu */
    .push-menu { width: 100%; }

    /* sidebar nav menu */
    .side-menu-button { right: 15px; left: inherit; transform: translateY(-50%); -webkit-transform: translateY(-50%); -moz-transform: translateY(-50%); -ms-transform: translateY(-50%); }
    .menu-style-2 .sidebar-nav-menu { width: 100%; }
    .side-menu-header .side-menu-button { right: 15px; }

    /* hamburger menu */
    .menu-list-wrapper.menu-list-wrapper-small { max-height: calc(100vh - 60px); height: calc(100vh - 60px); }

    /* header push menu close button */
    .close-menu { right: 0; top: 0;}

    /* swiper carousel */
    .swiper-container-horizontal > .swiper-pagination-bullets, .swiper-pagination-fraction { bottom: 30px; }

    /* swiper custom image bullets */
    .slider-custom-image.swiper-container-horizontal > .swiper-pagination-bullets { bottom: 0; }
    .slider-custom-image-pagination .swiper-pagination-bullet { width: 32px; height: 32px; margin: 0 7px !important; }

    /* slider navigation style 02 */
    .slider-navigation-style-02.swiper-button-prev { left: 0;}
    .slider-navigation-style-02.swiper-button-next { right: 0;}

    /* slider navigation style 05 */
    .slider-navigation-style-05.swiper-button-prev { left: 15px; }
    .slider-navigation-style-05.swiper-button-next { right: 15px; }

    /* slider navigation style 07 */
    .slider-navigation-style-07.swiper-button-prev.light { left: 0;}
    .slider-navigation-style-07.swiper-button-next.light { right: 0;}
    .slider-navigation-style-07.swiper-button-next { right: 10px;}
    .slider-navigation-style-07.swiper-button-prev { left: 10px;}

    /* swiper custom text */
    .slider-custom-text-prev.swiper-button-prev { padding-left: 8px; padding-right: 8px; }
    .slider-custom-text-next.swiper-button-next { padding-left: 8px; padding-right: 8px; }

    /* swiper vertical */
    .slider-vertical >.swiper-pagination-bullets { right: 15px; }

    /* swiper pagination */
    .slider-multy-scroll-right > .swiper-pagination-bullets { right: 15px; }

    /* grid */
    .grid.xs-grid-6col li { width: 16.67%; }
    .grid.xs-grid-6col li.grid-item-double { width: 33.33%; }
    .grid.xs-grid-5col li { width: 20%; }
    .grid.xs-grid-5col li.grid-item-double { width: 40%; }
    .grid.xs-grid-4col li { width: 25%; }
    .grid.xs-grid-4col li.grid-item-double { width: 50%; }
    .grid.xs-grid-3col li { width: 33.33%; }
    .grid.xs-grid-3col li.grid-item-double { width: 66.67%; }
    .grid.xs-grid-2col li { width: 50%; }
    .grid.xs-grid-2col li.grid-item-double { width: 100%; }
    .grid.xs-grid-1col li { width: 100%; }

    /* gutter size */
    .grid.gutter-medium, .grid.gutter-large { margin: 0 -15px; }
    .grid.gutter-medium li, .grid.gutter-large li { padding: 7px 15px }

    /* blog simple */
    .blog-simple .blog-post { -webkit-box-orient: vertical; -webkit-box-direction: normal; -ms-flex-direction: column; flex-direction: column; }
    .blog-simple .blog-post-image, .blog-simple .post-details { width: 100%; }

    /* blog side image */
    .blog-side-image .separator { display: none; }

    /* blog widget */
    .blog-widget li figure { width: 100px; }
    .blog-widget li .media-body { padding-left: 15px; }

    /* sidebar latest post */
    .latest-post-sidebar li figure { width: 80px; }

    /* pagination style 01 */
    .pagination-style-01 .page-link { margin: 0 5px; }

    /* newsletter email */
    .newsletter-email input { padding-right: 65px; }

    /* newsletter style 01 */
    .newsletter-style-01 .btn { position: static; top: 0; border-radius: 100px; width: 100%; transform: translateY(0); -moz-transform: translateY(0); -webkit-transform: translateY(0); -ms-transform: translateY(0); margin-top: 15px; }

    /* letter spacing */
    .xs-letter-spacing-normal { letter-spacing: normal; }
    .xs-letter-spacing-1-half { letter-spacing: 0.50px; }
    .xs-letter-spacing-1px { letter-spacing: 1px; }
    .xs-letter-spacing-2px { letter-spacing: 2px; }
    .xs-letter-spacing-3px { letter-spacing: 3px; }
    .xs-letter-spacing-4px { letter-spacing: 4px; }
    .xs-letter-spacing-5px { letter-spacing: 5px; }

    /* text size */
    .text-extra-big { font-size: 55px; line-height: 55px; }
    .text-big { font-size: 50px; line-height: 50px; }

    /* countdown style 01 */
    .countdown.countdown-style-01 .countdown-box, .countdown.countdown-style-01 .countdown-box:first-child, .countdown.countdown-style-01 .countdown-box:last-child { margin: 0 0 30px; width: 50%; }

    /* footer */
    footer .footer-horizontal-link li { margin: 0 0 7px; }

    /* events conference */
    .home-events-conference h1 { font-size: 28px; line-height: 38px; }

    /* marketing agency */
    .team-tulip-bubble { bottom: 70px; padding: 15px; }

    /* interactive list style */
    .fullscreen-hover-list .hover-list-item.active .interactive-number { transform: translateX(30px); -webkit-transform: translateX(30px); -moz-transform: translateX(30px); -ms-transform: translateX(30px); }
    .fullscreen-hover-list .hover-list-item .interactive-line { width: 15px; margin-left: 10px; }
    .fullscreen-hover-list .hover-list-item .interactive-title:after { left: 15px; bottom: 15px; height: calc(100% - 30px); }
    .fullscreen-hover-list .hover-list-item.active .interactive-title:after { width: calc(100% - 30px); }
    .fullscreen-hover-list.light .hover-list-item .interactive-title { -webkit-text-stroke: 1px #4e4e4f; text-stroke: 1px #4e4e4f; }
    .fullscreen-hover-list .hover-list-item .interactive-icon { font-size: 40px; }

    /* interactive portfolio */
    .home-interactive-portfolio .fullscreen-hover-box .interactive-title { font-size: 30px; line-height: 30px; padding: 15px; }

    /* vertical portfolio */
    .home-vertical-portfolio .navbar { padding: 0;}

    /* swiper vertical */
    .slider-vertical .swiper-number-pagination { left: 17px; }

    /* personal portfolio */
    .home-personal-portfolio .title-large-2 { font-size: 50px; line-height: 55px; }

    /* photography */
    .home-photography .title-extra-large-heavy { font-size: 5rem; }
    .home-photography .interactive-banners-style-13 .interactive-banners-content { width: 35%; }

    /* freelancer */
    .home-freelancer .icon-extra-medium { font-size: 25px; }

    /* creative agency */
    .home-creative-agency h1.title-extra-large { font-size: 60px; line-height: 65px; }

    /* digital agency */
    .home-digital-agency h1.title-extra-large { font-size: 60px; line-height: 65px; }

    /* landing page */
    .litho-parallax-bg { right: 0px; top: -50px; }
    .landing-page-auto-slider .swiper-container.swiper-auto-slide .swiper-slide { width: 100% !important; }
    .landing-page-footer .title-large-2 { font-size: 35px; line-height: 42px; }

    /* split portfolio */
    .home-split-portfolio .title-large { font-size: 45px; line-height: 40px; }
}

@media screen and (max-width: 480px) {
    /* yoga meditation*/
    .home-yoga-meditation .rev_slider_wrapper { height: calc(100vh - 72px) !important; }

    /* home decor */
    .home-decor .rev_slider_wrapper { height: calc(100vh - 100px) !important; }
    /*.home-decor .zeus { transform: matrix(1, 0, 0, 1, -26, -55) !important; }*/
    .home-decor .collection-btn { top: auto !important; }
    .home-decor .collection-btn span{ width: 45px !important; height: 45px !important; }

    /* photography*/
    .home-photography .ares { display: none !important; }
    .home-photography .zeus { display: block !important; }
    .home-photography .zeus .tp-bullet { height: 8px; width: 8px; border: 1px solid #fff; opacity: 0.5; }
    .home-photography .zeus .tp-bullet.selected { opacity: 1; }
    .home-photography .tp-bullet-image,.home-photography .tp-bullet-title,.home-photography .tp-bullet-imageoverlay { display: none !important; }

    /* startup*/
    .home-startup .rs-btn div { height: 30px !important; width: 30px !important; }
    .home-startup .tp-bullet.selected { opacity: 1; }

    /* architecture*/
    .home-architecture .metis.tparrows { width: 50px; height: 50px; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; top: auto !important; bottom: -25px; -webkit-transform: translate(-50%,-50%) !important; -ms-transform: translate(-50%,-50%) !important; transform: translate(-50%,-50%) !important; }
    .home-architecture .metis.tparrows:before { padding-top: 0; }
    .home-architecture .metis.tparrows:hover:before { -webkit-transform: scale(1); -ms-transform: scale(1); transform: scale(1);}
    .home-architecture .metis.tparrows.tp-leftarrow { left: 94% !important; }
    .home-architecture .metis.tparrows.tp-rightarrow { left: 94% !important; bottom: 25px; }

    /* split slider */
    .home-split-portfolio .swiper-slide-l { background-size: 53%; }

    /* countdown style 02 */
    .countdown.countdown-style-02 .countdown-box { margin-bottom: 15px; padding: 0 25px; }
}
@media (max-height: 460px) {
    /* header search */
    .active-form .form-wrapper .search-form { height: 85vh; }
}

@media (max-height: 600px) {
    /* modern menu */
    [data-mobile-nav-style=modern] .navbar-modern-inner .navbar-collapse { padding-top: 30px; padding-bottom: 30px; }

    /* full menu */
    [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .navbar-collapse { padding-top: 30px; padding-bottom: 30px; }
    [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .navbar-toggler { right: 15px; top: 15px; }

    /* height */
    .full-screen-auto { height: auto !important; }

}